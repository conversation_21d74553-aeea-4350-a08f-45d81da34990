{"version": 3, "sources": ["../../src/build/build-context.ts"], "sourcesContent": ["import type { LoadedEnvFiles } from '@next/env'\nimport type { Rewrite, Redirect } from '../lib/load-custom-routes'\nimport type { __ApiPreviewProps } from '../server/api-utils'\nimport type { NextConfigComplete } from '../server/config-shared'\nimport type { Span } from '../trace'\nimport type getBaseWebpackConfig from './webpack-config'\nimport type { TelemetryPluginState } from './webpack/plugins/telemetry-plugin/telemetry-plugin'\nimport type { Telemetry } from '../telemetry/storage'\n\n// A layer for storing data that is used by plugins to communicate with each\n// other between different steps of the build process. This is only internal\n// to Next.js and will not be a part of the final build output.\n// These states don't need to be deeply merged.\nlet pluginState: Record<string, any> = {}\nexport function resumePluginState(resumedState?: Record<string, any>) {\n  Object.assign(pluginState, resumedState)\n}\n\n// This method gives you the plugin state with typed and mutable value fields\n// behind a proxy so we can lazily initialize the values **after** resuming the\n// plugin state.\nexport function getProxiedPluginState<State extends Record<string, any>>(\n  initialState: State\n) {\n  return new Proxy(pluginState, {\n    get(target, key: string) {\n      if (typeof target[key] === 'undefined') {\n        return (target[key] = initialState[key])\n      }\n      return target[key]\n    },\n    set(target, key: string, value) {\n      target[key] = value\n      return true\n    },\n  }) as State\n}\n\nexport function getPluginState() {\n  return pluginState\n}\n\nexport interface MappedPages {\n  [page: string]: string\n}\n\n// a global object to store context for the current build\n// this is used to pass data between different steps of the build without having\n// to pass it through function arguments.\n// Not exhaustive, but should be extended to as needed whilst refactoring\nexport const NextBuildContext: Partial<{\n  compilerIdx?: number\n  pluginState: Record<string, any>\n  // core fields\n  dir: string\n  distDir: string\n  buildId: string\n  encryptionKey: string\n  config: NextConfigComplete\n  appDir: string\n  pagesDir: string\n  rewrites: {\n    fallback: Rewrite[]\n    afterFiles: Rewrite[]\n    beforeFiles: Rewrite[]\n  }\n  originalRewrites: {\n    fallback: Rewrite[]\n    afterFiles: Rewrite[]\n    beforeFiles: Rewrite[]\n  }\n  hasRewrites: boolean\n  originalRedirects: Redirect[]\n  loadedEnvFiles: LoadedEnvFiles\n  previewProps: __ApiPreviewProps\n  mappedPages: MappedPages | undefined\n  mappedAppPages: MappedPages | undefined\n  mappedRootPaths: MappedPages\n  hasInstrumentationHook: boolean\n\n  // misc fields\n  telemetry: Telemetry\n  telemetryState: TelemetryPluginState\n  nextBuildSpan: Span\n\n  // cli fields\n  reactProductionProfiling: boolean\n  noMangling: boolean\n  appDirOnly: boolean\n  clientRouterFilters: Parameters<\n    typeof getBaseWebpackConfig\n  >[1]['clientRouterFilters']\n  previewModeId: string\n  fetchCacheKeyPrefix?: string\n  allowedRevalidateHeaderKeys?: string[]\n}> = {}\n"], "names": ["pluginState", "resumePluginState", "resumedState", "Object", "assign", "getProxiedPluginState", "initialState", "Proxy", "get", "target", "key", "set", "value", "getPluginState", "NextBuildContext"], "mappings": "AASA,4EAA4E;AAC5E,4EAA4E;AAC5E,+DAA+D;AAC/D,+CAA+C;AAC/C,IAAIA,cAAmC,CAAC;AACxC,OAAO,SAASC,kBAAkBC,YAAkC;IAClEC,OAAOC,MAAM,CAACJ,aAAaE;AAC7B;AAEA,6EAA6E;AAC7E,+EAA+E;AAC/E,gBAAgB;AAChB,OAAO,SAASG,sBACdC,YAAmB;IAEnB,OAAO,IAAIC,MAAMP,aAAa;QAC5BQ,KAAIC,MAAM,EAAEC,GAAW;YACrB,IAAI,OAAOD,MAAM,CAACC,IAAI,KAAK,aAAa;gBACtC,OAAQD,MAAM,CAACC,IAAI,GAAGJ,YAAY,CAACI,IAAI;YACzC;YACA,OAAOD,MAAM,CAACC,IAAI;QACpB;QACAC,KAAIF,MAAM,EAAEC,GAAW,EAAEE,KAAK;YAC5BH,MAAM,CAACC,IAAI,GAAGE;YACd,OAAO;QACT;IACF;AACF;AAEA,OAAO,SAASC;IACd,OAAOb;AACT;AAMA,yDAAyD;AACzD,gFAAgF;AAChF,yCAAyC;AACzC,yEAAyE;AACzE,OAAO,MAAMc,mBA6CR,CAAC,EAAC"}