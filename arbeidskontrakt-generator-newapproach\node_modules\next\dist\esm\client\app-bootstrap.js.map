{"version": 3, "sources": ["../../src/client/app-bootstrap.ts"], "sourcesContent": ["/**\n * Before starting the Next.js runtime and requiring any module, we need to make\n * sure the following scripts are executed in the correct order:\n * - Polyfills\n * - next/script with `beforeInteractive` strategy\n */\n\nconst version = process.env.__NEXT_VERSION\n\nwindow.next = {\n  version,\n  appDir: true,\n}\n\nfunction loadScriptsInSequence(\n  scripts: [src: string, props: { [prop: string]: any }][],\n  hydrate: () => void\n) {\n  if (!scripts || !scripts.length) {\n    return hydrate()\n  }\n\n  return scripts\n    .reduce((promise, [src, props]) => {\n      return promise.then(() => {\n        return new Promise<void>((resolve, reject) => {\n          const el = document.createElement('script')\n\n          if (props) {\n            for (const key in props) {\n              if (key !== 'children') {\n                el.setAttribute(key, props[key])\n              }\n            }\n          }\n\n          if (src) {\n            el.src = src\n            el.onload = () => resolve()\n            el.onerror = reject\n          } else if (props) {\n            el.innerHTML = props.children\n            setTimeout(resolve)\n          }\n\n          document.head.appendChild(el)\n        })\n      })\n    }, Promise.resolve())\n    .catch((err: Error) => {\n      console.error(err)\n      // Still try to hydrate even if there's an error.\n    })\n    .then(() => {\n      hydrate()\n    })\n}\n\nexport function appBootstrap(hydrate: () => void) {\n  loadScriptsInSequence((self as any).__next_s, () => {\n    // If the static shell is being debugged, skip hydration if the\n    // `__nextppronly` query is present. This is only enabled when the\n    // environment variable `__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING` is\n    // set to `1`. Otherwise the following is optimized out.\n    if (process.env.__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING === '1') {\n      const search = new URLSearchParams(window.location.search)\n      if (\n        search.get('__nextppronly') === 'fallback' ||\n        search.get('__nextppronly') === '1'\n      ) {\n        console.warn(\n          `Skipping hydration due to __nextppronly=${search.get('__nextppronly')}`\n        )\n        return\n      }\n    }\n\n    hydrate()\n  })\n}\n"], "names": ["version", "process", "env", "__NEXT_VERSION", "window", "next", "appDir", "loadScriptsInSequence", "scripts", "hydrate", "length", "reduce", "promise", "src", "props", "then", "Promise", "resolve", "reject", "el", "document", "createElement", "key", "setAttribute", "onload", "onerror", "innerHTML", "children", "setTimeout", "head", "append<PERSON><PERSON><PERSON>", "catch", "err", "console", "error", "appBootstrap", "self", "__next_s", "__NEXT_EXPERIMENTAL_STATIC_SHELL_DEBUGGING", "search", "URLSearchParams", "location", "get", "warn"], "mappings": "AAAA;;;;;CAKC,GAED,MAAMA,UAAUC,QAAQC,GAAG,CAACC,cAAc;AAE1CC,OAAOC,IAAI,GAAG;IACZL;IACAM,QAAQ;AACV;AAEA,SAASC,sBACPC,OAAwD,EACxDC,OAAmB;IAEnB,IAAI,CAACD,WAAW,CAACA,QAAQE,MAAM,EAAE;QAC/B,OAAOD;IACT;IAEA,OAAOD,QACJG,MAAM,CAAC,CAACC;YAAS,CAACC,KAAKC,MAAM;QAC5B,OAAOF,QAAQG,IAAI,CAAC;YAClB,OAAO,IAAIC,QAAc,CAACC,SAASC;gBACjC,MAAMC,KAAKC,SAASC,aAAa,CAAC;gBAElC,IAAIP,OAAO;oBACT,IAAK,MAAMQ,OAAOR,MAAO;wBACvB,IAAIQ,QAAQ,YAAY;4BACtBH,GAAGI,YAAY,CAACD,KAAKR,KAAK,CAACQ,IAAI;wBACjC;oBACF;gBACF;gBAEA,IAAIT,KAAK;oBACPM,GAAGN,GAAG,GAAGA;oBACTM,GAAGK,MAAM,GAAG,IAAMP;oBAClBE,GAAGM,OAAO,GAAGP;gBACf,OAAO,IAAIJ,OAAO;oBAChBK,GAAGO,SAAS,GAAGZ,MAAMa,QAAQ;oBAC7BC,WAAWX;gBACb;gBAEAG,SAASS,IAAI,CAACC,WAAW,CAACX;YAC5B;QACF;IACF,GAAGH,QAAQC,OAAO,IACjBc,KAAK,CAAC,CAACC;QACNC,QAAQC,KAAK,CAACF;IACd,iDAAiD;IACnD,GACCjB,IAAI,CAAC;QACJN;IACF;AACJ;AAEA,OAAO,SAAS0B,aAAa1B,OAAmB;IAC9CF,sBAAsB,AAAC6B,KAAaC,QAAQ,EAAE;QAC5C,+DAA+D;QAC/D,kEAAkE;QAClE,uEAAuE;QACvE,wDAAwD;QACxD,IAAIpC,QAAQC,GAAG,CAACoC,0CAA0C,KAAK,KAAK;YAClE,MAAMC,SAAS,IAAIC,gBAAgBpC,OAAOqC,QAAQ,CAACF,MAAM;YACzD,IACEA,OAAOG,GAAG,CAAC,qBAAqB,cAChCH,OAAOG,GAAG,CAAC,qBAAqB,KAChC;gBACAT,QAAQU,IAAI,CACV,AAAC,6CAA0CJ,OAAOG,GAAG,CAAC;gBAExD;YACF;QACF;QAEAjC;IACF;AACF"}