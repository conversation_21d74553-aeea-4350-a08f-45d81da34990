{"version": 3, "sources": ["../../../src/server/app-render/action-async-storage-instance.ts"], "sourcesContent": ["import type { ActionAsyncStorage } from './action-async-storage.external'\nimport { createAsyncLocalStorage } from './async-local-storage'\n\nexport const actionAsyncStorageInstance: ActionAsyncStorage =\n  createAsyncLocalStorage()\n"], "names": ["createAsyncLocalStorage", "actionAsyncStorageInstance"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,wBAAuB;AAE/D,OAAO,MAAMC,6BACXD,0BAAyB"}