{"version": 3, "sources": ["../../../src/lib/memory/trace.ts"], "sourcesContent": ["import v8 from 'v8'\nimport { info, warn } from '../../build/output/log'\nimport { type Span, trace } from '../../trace'\nimport { bold, italic } from '../picocolors'\nimport { join } from 'path'\nimport { traceGlobals } from '../../trace/shared'\n\nconst HEAP_SNAPSHOT_THRESHOLD_PERCENT = 70\nlet alreadyGeneratedHeapSnapshot = false\n\nconst TRACE_MEMORY_USAGE_TIMER_MS = 20000\nlet traceMemoryUsageTimer: NodeJS.Timeout | undefined\n\ninterface MemoryUsage {\n  'memory.rss': number\n  'memory.heapUsed': number\n  'memory.heapTotal': number\n  'memory.heapMax': number\n}\n\nconst allMemoryUsage: MemoryUsage[] = []\n\n/**\n * Begins a timer that will record memory usage periodically to understand\n * memory usage across the lifetime of the process.\n */\nexport function startPeriodicMemoryUsageTracing(): void {\n  traceMemoryUsageTimer = setTimeout(() => {\n    traceMemoryUsage('periodic memory snapshot')\n    startPeriodicMemoryUsageTracing()\n  }, TRACE_MEMORY_USAGE_TIMER_MS)\n}\n\nexport function stopPeriodicMemoryUsageTracing(): void {\n  if (traceMemoryUsageTimer) {\n    clearTimeout(traceMemoryUsageTimer)\n  }\n}\n\n/**\n * Returns the list of all recorded memory usage snapshots from the process.\n */\nexport function getAllMemoryUsageSpans(): MemoryUsage[] {\n  return allMemoryUsage\n}\n\n/**\n * Records a snapshot of memory usage at this moment in time to the .next/trace\n * file.\n */\nexport function traceMemoryUsage(\n  description: string,\n  parentSpan?: Span | undefined\n): void {\n  const memoryUsage = process.memoryUsage()\n  const v8HeapStatistics = v8.getHeapStatistics()\n  const heapUsed = v8HeapStatistics.used_heap_size\n  const heapMax = v8HeapStatistics.heap_size_limit\n  const tracedMemoryUsage: MemoryUsage = {\n    'memory.rss': memoryUsage.rss,\n    'memory.heapUsed': heapUsed,\n    'memory.heapTotal': memoryUsage.heapTotal,\n    'memory.heapMax': heapMax,\n  }\n  allMemoryUsage.push(tracedMemoryUsage)\n  const tracedMemoryUsageAsStrings = Object.fromEntries(\n    Object.entries(tracedMemoryUsage).map(([key, value]) => [\n      key,\n      String(value),\n    ])\n  )\n  if (parentSpan) {\n    parentSpan.traceChild('memory-usage', tracedMemoryUsageAsStrings)\n  } else {\n    trace('memory-usage', undefined, tracedMemoryUsageAsStrings)\n  }\n  if (process.env.EXPERIMENTAL_DEBUG_MEMORY_USAGE) {\n    const percentageHeapUsed = (100 * heapUsed) / heapMax\n\n    info('')\n    info('***************************************')\n    info(`Memory usage report at \"${description}\":`)\n    info(` - RSS: ${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB`)\n    info(` - Heap Used: ${(heapUsed / 1024 / 1024).toFixed(2)} MB`)\n    info(\n      ` - Heap Total Allocated: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(\n        2\n      )} MB`\n    )\n    info(` - Heap Max: ${(heapMax / 1024 / 1024).toFixed(2)} MB`)\n    info(` - Percentage Heap Used: ${percentageHeapUsed.toFixed(2)}%`)\n    info('***************************************')\n    info('')\n\n    if (percentageHeapUsed > HEAP_SNAPSHOT_THRESHOLD_PERCENT) {\n      const distDir = traceGlobals.get('distDir')\n      const heapFilename = join(\n        distDir,\n        `${description.replace(' ', '-')}.heapsnapshot`\n      )\n      warn(\n        bold(\n          `Heap usage is close to the limit. ${percentageHeapUsed.toFixed(\n            2\n          )}% of heap has been used.`\n        )\n      )\n      if (!alreadyGeneratedHeapSnapshot) {\n        warn(\n          bold(\n            `Saving heap snapshot to ${heapFilename}.  ${italic(\n              'Note: this will take some time.'\n            )}`\n          )\n        )\n        v8.writeHeapSnapshot(heapFilename)\n        alreadyGeneratedHeapSnapshot = true\n      } else {\n        warn(\n          'Skipping heap snapshot generation since heap snapshot has already been generated.'\n        )\n      }\n    }\n  }\n}\n"], "names": ["v8", "info", "warn", "trace", "bold", "italic", "join", "traceGlobals", "HEAP_SNAPSHOT_THRESHOLD_PERCENT", "alreadyGeneratedHeapSnapshot", "TRACE_MEMORY_USAGE_TIMER_MS", "traceMemoryUsageTimer", "allMemoryUsage", "startPeriodicMemoryUsageTracing", "setTimeout", "traceMemoryUsage", "stopPeriodicMemoryUsageTracing", "clearTimeout", "getAllMemoryUsageSpans", "description", "parentSpan", "memoryUsage", "process", "v8HeapStatistics", "getHeapStatistics", "heapUsed", "used_heap_size", "heapMax", "heap_size_limit", "tracedMemoryUsage", "rss", "heapTotal", "push", "tracedMemoryUsageAsStrings", "Object", "fromEntries", "entries", "map", "key", "value", "String", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "env", "EXPERIMENTAL_DEBUG_MEMORY_USAGE", "percentageHeapUsed", "toFixed", "distDir", "get", "heapFilename", "replace", "writeHeapSnapshot"], "mappings": "AAAA,OAAOA,QAAQ,KAAI;AACnB,SAASC,IAAI,EAAEC,IAAI,QAAQ,yBAAwB;AACnD,SAAoBC,KAAK,QAAQ,cAAa;AAC9C,SAASC,IAAI,EAAEC,MAAM,QAAQ,gBAAe;AAC5C,SAASC,IAAI,QAAQ,OAAM;AAC3B,SAASC,YAAY,QAAQ,qBAAoB;AAEjD,MAAMC,kCAAkC;AACxC,IAAIC,+BAA+B;AAEnC,MAAMC,8BAA8B;AACpC,IAAIC;AASJ,MAAMC,iBAAgC,EAAE;AAExC;;;CAGC,GACD,OAAO,SAASC;IACdF,wBAAwBG,WAAW;QACjCC,iBAAiB;QACjBF;IACF,GAAGH;AACL;AAEA,OAAO,SAASM;IACd,IAAIL,uBAAuB;QACzBM,aAAaN;IACf;AACF;AAEA;;CAEC,GACD,OAAO,SAASO;IACd,OAAON;AACT;AAEA;;;CAGC,GACD,OAAO,SAASG,iBACdI,WAAmB,EACnBC,UAA6B;IAE7B,MAAMC,cAAcC,QAAQD,WAAW;IACvC,MAAME,mBAAmBvB,GAAGwB,iBAAiB;IAC7C,MAAMC,WAAWF,iBAAiBG,cAAc;IAChD,MAAMC,UAAUJ,iBAAiBK,eAAe;IAChD,MAAMC,oBAAiC;QACrC,cAAcR,YAAYS,GAAG;QAC7B,mBAAmBL;QACnB,oBAAoBJ,YAAYU,SAAS;QACzC,kBAAkBJ;IACpB;IACAf,eAAeoB,IAAI,CAACH;IACpB,MAAMI,6BAA6BC,OAAOC,WAAW,CACnDD,OAAOE,OAAO,CAACP,mBAAmBQ,GAAG,CAAC,CAAC,CAACC,KAAKC,MAAM,GAAK;YACtDD;YACAE,OAAOD;SACR;IAEH,IAAInB,YAAY;QACdA,WAAWqB,UAAU,CAAC,gBAAgBR;IACxC,OAAO;QACL9B,MAAM,gBAAgBuC,WAAWT;IACnC;IACA,IAAIX,QAAQqB,GAAG,CAACC,+BAA+B,EAAE;QAC/C,MAAMC,qBAAqB,AAAC,MAAMpB,WAAYE;QAE9C1B,KAAK;QACLA,KAAK;QACLA,KAAK,CAAC,wBAAwB,EAAEkB,YAAY,EAAE,CAAC;QAC/ClB,KAAK,CAAC,QAAQ,EAAE,AAACoB,CAAAA,YAAYS,GAAG,GAAG,OAAO,IAAG,EAAGgB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC/D7C,KAAK,CAAC,cAAc,EAAE,AAACwB,CAAAA,WAAW,OAAO,IAAG,EAAGqB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC9D7C,KACE,CAAC,yBAAyB,EAAE,AAACoB,CAAAA,YAAYU,SAAS,GAAG,OAAO,IAAG,EAAGe,OAAO,CACvE,GACA,GAAG,CAAC;QAER7C,KAAK,CAAC,aAAa,EAAE,AAAC0B,CAAAA,UAAU,OAAO,IAAG,EAAGmB,OAAO,CAAC,GAAG,GAAG,CAAC;QAC5D7C,KAAK,CAAC,yBAAyB,EAAE4C,mBAAmBC,OAAO,CAAC,GAAG,CAAC,CAAC;QACjE7C,KAAK;QACLA,KAAK;QAEL,IAAI4C,qBAAqBrC,iCAAiC;YACxD,MAAMuC,UAAUxC,aAAayC,GAAG,CAAC;YACjC,MAAMC,eAAe3C,KACnByC,SACA,GAAG5B,YAAY+B,OAAO,CAAC,KAAK,KAAK,aAAa,CAAC;YAEjDhD,KACEE,KACE,CAAC,kCAAkC,EAAEyC,mBAAmBC,OAAO,CAC7D,GACA,wBAAwB,CAAC;YAG/B,IAAI,CAACrC,8BAA8B;gBACjCP,KACEE,KACE,CAAC,wBAAwB,EAAE6C,aAAa,GAAG,EAAE5C,OAC3C,oCACC;gBAGPL,GAAGmD,iBAAiB,CAACF;gBACrBxC,+BAA+B;YACjC,OAAO;gBACLP,KACE;YAEJ;QACF;IACF;AACF"}