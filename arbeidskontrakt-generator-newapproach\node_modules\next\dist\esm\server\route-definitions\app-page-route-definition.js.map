{"version": 3, "sources": ["../../../src/server/route-definitions/app-page-route-definition.ts"], "sourcesContent": ["import type { RouteDefinition } from './route-definition'\nimport { RouteKind } from '../route-kind'\n\nexport interface AppPageRouteDefinition\n  extends RouteDefinition<RouteKind.APP_PAGE> {\n  readonly appPaths: ReadonlyArray<string>\n}\n\n/**\n * Returns true if the given definition is an App Page route definition.\n */\nexport function isAppPageRouteDefinition(\n  definition: RouteDefinition\n): definition is AppPageRouteDefinition {\n  return definition.kind === RouteKind.APP_PAGE\n}\n"], "names": ["RouteKind", "isAppPageRouteDefinition", "definition", "kind", "APP_PAGE"], "mappings": "AACA,SAASA,SAAS,QAAQ,gBAAe;AAOzC;;CAEC,GACD,OAAO,SAASC,yBACdC,UAA2B;IAE3B,OAAOA,WAAWC,IAAI,KAAKH,UAAUI,QAAQ;AAC/C"}