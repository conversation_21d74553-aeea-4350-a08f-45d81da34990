{"version": 3, "file": "app-route-turbo-experimental.runtime.prod.js", "mappings": "8EAAAA,CAAAA,EAAOC,OAAO,CAAGC,QAAQ,+D,oECCzB,IAAIC,EAAYC,OAAOC,cAAc,CACjCC,EAAmBF,OAAOG,wBAAwB,CAClDC,EAAoBJ,OAAOK,mBAAmB,CAC9CC,EAAeN,OAAOO,SAAS,CAACC,cAAc,CAgB9CC,EAAc,CAAC,EAWnB,SAASC,EAAgBC,CAAC,EACxB,IAAIC,EACJ,IAAMC,EAAQ,CACZ,SAAUF,GAAKA,EAAEG,IAAI,EAAI,CAAC,KAAK,EAAEH,EAAEG,IAAI,CAAC,CAAC,CACzC,YAAaH,GAAMA,CAAAA,EAAEI,OAAO,EAAIJ,IAAAA,EAAEI,OAAO,GAAW,CAAC,QAAQ,EAAE,CAAC,iBAAOJ,EAAEI,OAAO,CAAgB,IAAIC,KAAKL,EAAEI,OAAO,EAAIJ,EAAEI,OAAO,EAAEE,WAAW,GAAG,CAAC,CAChJ,WAAYN,GAAK,iBAAOA,EAAEO,MAAM,EAAiB,CAAC,QAAQ,EAAEP,EAAEO,MAAM,CAAC,CAAC,CACtE,WAAYP,GAAKA,EAAEQ,MAAM,EAAI,CAAC,OAAO,EAAER,EAAEQ,MAAM,CAAC,CAAC,CACjD,WAAYR,GAAKA,EAAES,MAAM,EAAI,SAC7B,aAAcT,GAAKA,EAAEU,QAAQ,EAAI,WACjC,aAAcV,GAAKA,EAAEW,QAAQ,EAAI,CAAC,SAAS,EAAEX,EAAEW,QAAQ,CAAC,CAAC,CACzD,gBAAiBX,GAAKA,EAAEY,WAAW,EAAI,cACvC,aAAcZ,GAAKA,EAAEa,QAAQ,EAAI,CAAC,SAAS,EAAEb,EAAEa,QAAQ,CAAC,CAAC,CAC1D,CAACC,MAAM,CAACC,SACHC,EAAc,CAAC,EAAEhB,EAAEiB,IAAI,CAAC,CAAC,EAAEC,mBAAmB,MAACjB,CAAAA,EAAKD,EAAEmB,KAAK,EAAYlB,EAAK,IAAI,CAAC,CACvF,OAAOC,IAAAA,EAAMkB,MAAM,CAASJ,EAAc,CAAC,EAAEA,EAAY,EAAE,EAAEd,EAAMmB,IAAI,CAAC,MAAM,CAAC,CAEjF,SAASC,EAAYC,CAAM,EACzB,IAAMC,EAAM,aAAa,EAAG,IAAIC,IAChC,IAAK,IAAMC,KAAQH,EAAOI,KAAK,CAAC,OAAQ,CACtC,GAAI,CAACD,EACH,SACF,IAAME,EAAUF,EAAKG,OAAO,CAAC,KAC7B,GAAID,KAAAA,EAAgB,CAClBJ,EAAIM,GAAG,CAACJ,EAAM,QACd,QACF,CACA,GAAM,CAACK,EAAKZ,EAAM,CAAG,CAACO,EAAKM,KAAK,CAAC,EAAGJ,GAAUF,EAAKM,KAAK,CAACJ,EAAU,GAAG,CACtE,GAAI,CACFJ,EAAIM,GAAG,CAACC,EAAKE,mBAAmBd,MAAAA,EAAgBA,EAAQ,QAC1D,CAAE,KAAM,CACR,CACF,CACA,OAAOK,CACT,CACA,SAASU,EAAeC,CAAS,MA8CVC,EAKAA,EAlDrB,GAAI,CAACD,EACH,OAEF,GAAM,CAAC,CAAClB,EAAME,EAAM,CAAE,GAAGkB,EAAW,CAAGf,EAAYa,GAC7C,CACJ3B,OAAAA,CAAM,CACNJ,QAAAA,CAAO,CACPkC,SAAAA,CAAQ,CACRC,OAAAA,CAAM,CACNpC,KAAAA,CAAI,CACJqC,SAAAA,CAAQ,CACR/B,OAAAA,CAAM,CACNG,YAAAA,CAAW,CACXC,SAAAA,CAAQ,CACT,CAAGxB,OAAOoD,WAAW,CACpBJ,EAAWb,GAAG,CAAC,CAAC,CAACO,EAAKW,EAAO,GAAK,CAChCX,EAAIY,WAAW,GAAGC,OAAO,CAAC,KAAM,IAChCF,EACD,GAeH,OAAOG,SAEQC,CAAC,EAChB,IAAMC,EAAO,CAAC,EACd,IAAK,IAAMhB,KAAOe,EACZA,CAAC,CAACf,EAAI,EACRgB,CAAAA,CAAI,CAAChB,EAAI,CAAGe,CAAC,CAACf,EAAI,EAGtB,OAAOgB,CACT,EAvBiB,CACb9B,KAAAA,EACAE,MAAOc,mBAAmBd,GAC1BX,OAAAA,EACA,GAAGJ,GAAW,CAAEA,QAAS,IAAIC,KAAKD,EAAS,CAAC,CAC5C,GAAGkC,GAAY,CAAE5B,SAAU,EAAK,CAAC,CACjC,GAAG,iBAAO6B,GAAuB,CAAEhC,OAAQyC,OAAOT,EAAQ,CAAC,CAC3DpC,KAAAA,EACA,GAAGqC,GAAY,CAAE7B,QAAQ,CAmBpBsC,EAAUC,QAAQ,CADzBd,EAASA,CADYA,EAjBsBI,GAkB3BG,WAAW,IACSP,EAAS,KAAK,CAnBG,CAAC,CACpD,GAAG3B,GAAU,CAAEA,OAAQ,EAAK,CAAC,CAC7B,GAAGI,GAAY,CAAEA,QAAQ,CAsBpBsC,EAASD,QAAQ,CADxBd,EAASA,CADYA,EApBsBvB,GAqB3B8B,WAAW,IACQP,EAAS,KAAK,CAtBI,CAAC,CACpD,GAAGxB,GAAe,CAAEA,YAAa,EAAK,CAAC,EAG3C,CA/EAwC,CAhBe,CAACC,EAAQC,KACtB,IAAK,IAAIrC,KAAQqC,EACflE,EAAUiE,EAAQpC,EAAM,CAAEsC,IAAKD,CAAG,CAACrC,EAAK,CAAEuC,WAAY,EAAK,EAC/D,GAaS1D,EAAa,CACpB2D,eAAgB,IAAMA,EACtBC,gBAAiB,IAAMA,EACvBpC,YAAa,IAAMA,EACnBY,eAAgB,IAAMA,EACtBnC,gBAAiB,IAAMA,CACzB,GACAd,EAAOC,OAAO,CAnBI,EAACyE,EAAIC,EAAMC,EAAQC,KACnC,GAAIF,GAAQ,iBAAOA,GAAqB,mBAAOA,EAC7C,IAAK,IAAI7B,KAAOtC,EAAkBmE,GAC3BjE,EAAaoE,IAAI,CAACJ,EAAI5B,IAAQA,IAAQ8B,GACzCzE,EAAUuE,EAAI5B,EAAK,CAAEwB,IAAK,IAAMK,CAAI,CAAC7B,EAAI,CAAEyB,WAAY,CAAEM,CAAAA,EAAOvE,EAAiBqE,EAAM7B,EAAG,GAAM+B,EAAKN,UAAU,GAErH,OAAOG,CACT,GACwCvE,EAAU,CAAC,EAAG,aAAc,CAAE+B,MAAO,EAAK,GAWpDrB,GAkF9B,IAAImD,EAAY,CAAC,SAAU,MAAO,OAAO,CAKrCE,EAAW,CAAC,MAAO,SAAU,OAAO,CA0DpCM,EAAiB,MACnBO,YAAYC,CAAc,CAAE,CAE1B,IAAI,CAACC,OAAO,CAAG,aAAa,EAAG,IAAIzC,IACnC,IAAI,CAAC0C,QAAQ,CAAGF,EAChB,IAAMG,EAASH,EAAeV,GAAG,CAAC,UAClC,GAAIa,EAEF,IAAK,GAAM,CAACnD,EAAME,EAAM,GADTG,EAAY8C,GAEzB,IAAI,CAACF,OAAO,CAACpC,GAAG,CAACb,EAAM,CAAEA,KAAAA,EAAME,MAAAA,CAAM,EAG3C,CACA,CAACkD,OAAOC,QAAQ,CAAC,EAAG,CAClB,OAAO,IAAI,CAACJ,OAAO,CAACG,OAAOC,QAAQ,CAAC,EACtC,CAIA,IAAIC,MAAO,CACT,OAAO,IAAI,CAACL,OAAO,CAACK,IAAI,CAE1BhB,IAAI,GAAGiB,CAAI,CAAE,CACX,IAAMvD,EAAO,iBAAOuD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACvD,IAAI,CACjE,OAAO,IAAI,CAACiD,OAAO,CAACX,GAAG,CAACtC,EAC1B,CACAwD,OAAO,GAAGD,CAAI,CAAE,CACd,IAAIvE,EACJ,IAAMqD,EAAMoB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,EACnC,GAAI,CAACM,EAAKpD,MAAM,CACd,OAAOkC,EAAI9B,GAAG,CAAC,CAAC,CAACmD,EAAGxD,EAAM,GAAKA,GAEjC,IAAMF,EAAO,iBAAOuD,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAG,MAACvE,CAAAA,EAAKuE,CAAI,CAAC,EAAE,EAAY,KAAK,EAAIvE,EAAGgB,IAAI,CAC9F,OAAOqC,EAAIxC,MAAM,CAAC,CAAC,CAAC8D,EAAE,GAAKA,IAAM3D,GAAMO,GAAG,CAAC,CAAC,CAACmD,EAAGxD,EAAM,GAAKA,EAC7D,CACA0D,IAAI5D,CAAI,CAAE,CACR,OAAO,IAAI,CAACiD,OAAO,CAACW,GAAG,CAAC5D,EAC1B,CACAa,IAAI,GAAG0C,CAAI,CAAE,CACX,GAAM,CAACvD,EAAME,EAAM,CAAGqD,IAAAA,EAAKpD,MAAM,CAAS,CAACoD,CAAI,CAAC,EAAE,CAACvD,IAAI,CAAEuD,CAAI,CAAC,EAAE,CAACrD,KAAK,CAAC,CAAGqD,EACpEhD,EAAM,IAAI,CAAC0C,OAAO,CAMxB,OALA1C,EAAIM,GAAG,CAACb,EAAM,CAAEA,KAAAA,EAAME,MAAAA,CAAM,GAC5B,IAAI,CAACgD,QAAQ,CAACrC,GAAG,CACf,SACA4C,MAAMd,IAAI,CAACpC,GAAKA,GAAG,CAAC,CAAC,CAACmD,EAAGjC,EAAO,GAAK3C,EAAgB2C,IAASrB,IAAI,CAAC,OAE9D,IAAI,CAKbyD,OAAOC,CAAK,CAAE,CACZ,IAAMvD,EAAM,IAAI,CAAC0C,OAAO,CAClBc,EAAS,MAAOC,OAAO,CAACF,GAA6BA,EAAMvD,GAAG,CAAC,GAAUA,EAAIsD,MAAM,CAAC7D,IAAnDO,EAAIsD,MAAM,CAACC,GAKlD,OAJA,IAAI,CAACZ,QAAQ,CAACrC,GAAG,CACf,SACA4C,MAAMd,IAAI,CAACpC,GAAKA,GAAG,CAAC,CAAC,CAACmD,EAAGxD,EAAM,GAAKpB,EAAgBoB,IAAQE,IAAI,CAAC,OAE5D2D,CACT,CAIAE,OAAQ,CAEN,OADA,IAAI,CAACJ,MAAM,CAACJ,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACiB,IAAI,KACjC,IAAI,CAKb,CAACd,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,eAAe,EAAEC,KAAKC,SAAS,CAACjG,OAAOoD,WAAW,CAAC,IAAI,CAACyB,OAAO,GAAG,CAAC,CAE7EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAChE,GAAG,CAAC,GAAO,CAAC,EAAEiE,EAAExE,IAAI,CAAC,CAAC,EAAEC,mBAAmBuE,EAAEtE,KAAK,EAAE,CAAC,EAAEE,IAAI,CAAC,KAChG,CACF,EAGIqC,EAAkB,MACpBM,YAAY0B,CAAe,CAAE,KAGvBzF,EAAI0F,EAAIC,CADZ,KAAI,CAAC1B,OAAO,CAAG,aAAa,EAAG,IAAIzC,IAEnC,IAAI,CAAC0C,QAAQ,CAAGuB,EAChB,IAAMvD,EAAY,MAACyD,CAAAA,EAAK,MAACD,CAAAA,EAAK,MAAC1F,CAAAA,EAAKyF,EAAgBG,YAAY,EAAY,KAAK,EAAI5F,EAAG8D,IAAI,CAAC2B,EAAe,EAAaC,EAAKD,EAAgBnC,GAAG,CAAC,aAAY,EAAaqC,EAAK,EAAE,CAElL,IAAK,IAAME,KADWpB,MAAMO,OAAO,CAAC9C,GAAaA,EAAY4D,SA3IrCC,CAAa,EACvC,GAAI,CAACA,EACH,MAAO,EAAE,CACX,IAEIC,EACAC,EACAC,EACAC,EACAC,EANAC,EAAiB,EAAE,CACnBC,EAAM,EAMV,SAASC,IACP,KAAOD,EAAMP,EAAc5E,MAAM,EAAI,KAAKqF,IAAI,CAACT,EAAcU,MAAM,CAACH,KAClEA,GAAO,EAET,OAAOA,EAAMP,EAAc5E,MAAM,CAMnC,KAAOmF,EAAMP,EAAc5E,MAAM,EAAE,CAGjC,IAFA6E,EAAQM,EACRF,EAAwB,GACjBG,KAEL,GAAIN,MADJA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,EACb,CAKd,IAJAJ,EAAYI,EACZA,GAAO,EACPC,IACAJ,EAAYG,EACLA,EAAMP,EAAc5E,MAAM,EAZ9B8E,MADPA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,GACRL,MAAAA,GAAcA,MAAAA,GAa7BK,GAAO,CAELA,CAAAA,EAAMP,EAAc5E,MAAM,EAAI4E,MAAAA,EAAcU,MAAM,CAACH,IACrDF,EAAwB,GACxBE,EAAMH,EACNE,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOE,IACnDF,EAAQM,GAERA,EAAMJ,EAAY,CAEtB,MACEI,GAAO,EAGP,EAACF,GAAyBE,GAAOP,EAAc5E,MAAM,GACvDkF,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOD,EAAc5E,MAAM,EAE3E,CACA,OAAOkF,CACT,EAyFoFnE,GACtC,CACxC,IAAM0E,EAAS3E,EAAe4D,GAC1Be,GACF,IAAI,CAAC3C,OAAO,CAACpC,GAAG,CAAC+E,EAAO5F,IAAI,CAAE4F,EAClC,CACF,CAIAtD,IAAI,GAAGiB,CAAI,CAAE,CACX,IAAMzC,EAAM,iBAAOyC,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACvD,IAAI,CAChE,OAAO,IAAI,CAACiD,OAAO,CAACX,GAAG,CAACxB,EAC1B,CAIA0C,OAAO,GAAGD,CAAI,CAAE,CACd,IAAIvE,EACJ,IAAMqD,EAAMoB,MAAMd,IAAI,CAAC,IAAI,CAACM,OAAO,CAACsB,MAAM,IAC1C,GAAI,CAAChB,EAAKpD,MAAM,CACd,OAAOkC,EAET,IAAMvB,EAAM,iBAAOyC,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAG,MAACvE,CAAAA,EAAKuE,CAAI,CAAC,EAAE,EAAY,KAAK,EAAIvE,EAAGgB,IAAI,CAC7F,OAAOqC,EAAIxC,MAAM,CAAC,GAAOd,EAAEiB,IAAI,GAAKc,EACtC,CACA8C,IAAI5D,CAAI,CAAE,CACR,OAAO,IAAI,CAACiD,OAAO,CAACW,GAAG,CAAC5D,EAC1B,CAIAa,IAAI,GAAG0C,CAAI,CAAE,CACX,GAAM,CAACvD,EAAME,EAAOI,EAAO,CAAGiD,IAAAA,EAAKpD,MAAM,CAAS,CAACoD,CAAI,CAAC,EAAE,CAACvD,IAAI,CAAEuD,CAAI,CAAC,EAAE,CAACrD,KAAK,CAAEqD,CAAI,CAAC,EAAE,CAAC,CAAGA,EACrFhD,EAAM,IAAI,CAAC0C,OAAO,CAGxB,OAFA1C,EAAIM,GAAG,CAACb,EAAM6F,SAyBOvF,EAAS,CAAEN,KAAM,GAAIE,MAAO,EAAG,CAAC,EAUvD,MAT8B,UAA1B,OAAOI,EAAOnB,OAAO,EACvBmB,CAAAA,EAAOnB,OAAO,CAAG,IAAIC,KAAKkB,EAAOnB,OAAO,GAEtCmB,EAAOhB,MAAM,EACfgB,CAAAA,EAAOnB,OAAO,CAAG,IAAIC,KAAKA,KAAK0G,GAAG,GAAKxF,IAAAA,EAAOhB,MAAM,CAAM,EAExDgB,CAAAA,OAAAA,EAAOpB,IAAI,EAAaoB,KAAqB,IAArBA,EAAOpB,IAAI,GACrCoB,CAAAA,EAAOpB,IAAI,CAAG,GAAE,EAEXoB,CACT,EApCkC,CAAEN,KAAAA,EAAME,MAAAA,EAAO,GAAGI,CAAM,IACtDqB,SAiBaoE,CAAG,CAAEC,CAAO,EAE3B,IAAK,GAAM,EAAG9F,EAAM,GADpB8F,EAAQnC,MAAM,CAAC,cACSkC,GAAK,CAC3B,IAAME,EAAanH,EAAgBoB,GACnC8F,EAAQE,MAAM,CAAC,aAAcD,EAC/B,CACF,EAvBY1F,EAAK,IAAI,CAAC2C,QAAQ,EACnB,IAAI,CAKbW,OAAO,GAAGN,CAAI,CAAE,CACd,GAAM,CAACvD,EAAMmG,EAAQ,CAAG,iBAAO5C,CAAI,CAAC,EAAE,CAAgB,CAACA,CAAI,CAAC,EAAE,CAAC,CAAG,CAACA,CAAI,CAAC,EAAE,CAACvD,IAAI,CAAEuD,CAAI,CAAC,EAAE,CAAC,CACzF,OAAO,IAAI,CAAC1C,GAAG,CAAC,CAAE,GAAGsF,CAAO,CAAEnG,KAAAA,EAAME,MAAO,GAAIf,QAAS,aAAa,EAAG,IAAIC,KAAK,EAAG,EACtF,CACA,CAACgE,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CAAC,gBAAgB,EAAEC,KAAKC,SAAS,CAACjG,OAAOoD,WAAW,CAAC,IAAI,CAACyB,OAAO,GAAG,CAAC,CAE9EqB,UAAW,CACT,MAAO,IAAI,IAAI,CAACrB,OAAO,CAACsB,MAAM,GAAG,CAAChE,GAAG,CAACzB,GAAiBsB,IAAI,CAAC,KAC9D,CACF,C,wCCvTA,CAAC,KAAK,YAA6C,cAA7B,OAAOgG,qBAAkCA,CAAAA,oBAAoBC,EAAE,CAACC,UAAU,GAAE,EAAE,IAAIC,EAAE,CAAC,EAAE,CAAC,KAC9G;;;;;CAKC,EAAEC,EAAEC,KAAK,CAAyI,SAAeF,CAAC,CAACC,CAAC,EAAE,GAAG,iBAAOD,EAAc,MAAM,UAAc,iCAAyF,IAAI,IAAxD1E,EAAE,CAAC,EAAkB6E,EAAEH,EAAE7F,KAAK,CAACiG,GAAOC,EAAEjD,CAA7B6C,GAAG,CAAC,GAA2BK,MAAM,EAAEC,EAAUC,EAAE,EAAEA,EAAEL,EAAEvG,MAAM,CAAC4G,IAAI,CAAC,IAAIC,EAAEN,CAAC,CAACK,EAAE,CAAKE,EAAED,EAAEpG,OAAO,CAAC,KAAK,IAAGqG,CAAAA,EAAE,IAAY,IAAIzC,EAAEwC,EAAEE,MAAM,CAAC,EAAED,GAAGE,IAAI,GAAOpI,EAAEiI,EAAEE,MAAM,CAAC,EAAED,EAAED,EAAE7G,MAAM,EAAEgH,IAAI,EAAM,MAAKpI,CAAC,CAAC,EAAE,EAAEA,CAAAA,EAAEA,EAAEgC,KAAK,CAAC,EAAE,GAAE,EAAKqG,KAAAA,GAAWvF,CAAC,CAAC2C,EAAE,EAAE3C,CAAAA,CAAC,CAAC2C,EAAE,CAAC6C,SAA8qCd,CAAC,CAACC,CAAC,EAAE,GAAG,CAAC,OAAOA,EAAED,EAAE,CAAC,MAAMC,EAAE,CAAC,OAAOD,CAAC,CAAC,EAA3sCxH,EAAE6H,EAAC,EAAE,CAAC,OAAO/E,CAAC,EAAtf2E,EAAEc,SAAS,CAA4e,SAAmBf,CAAC,CAACC,CAAC,CAACM,CAAC,EAAE,IAAIH,EAAEG,GAAG,CAAC,EAAMJ,EAAEC,EAAEY,MAAM,EAAE1F,EAAE,GAAG,mBAAO6E,EAAgB,MAAM,UAAc,4BAA4B,GAAG,CAAC/C,EAAE6B,IAAI,CAACe,GAAI,MAAM,UAAc,4BAA4B,IAAIK,EAAEF,EAAEF,GAAG,GAAGI,GAAG,CAACjD,EAAE6B,IAAI,CAACoB,GAAI,MAAM,UAAc,2BAA2B,IAAIG,EAAER,EAAE,IAAIK,EAAE,GAAG,MAAMD,EAAErH,MAAM,CAAC,CAAC,IAAI0H,EAAEL,EAAErH,MAAM,CAAC,EAAE,GAAGkI,MAAMR,IAAI,CAACS,SAAST,GAAI,MAAM,UAAc,4BAA4BD,GAAG,aAAaW,KAAKC,KAAK,CAACX,EAAE,CAAC,GAAGL,EAAEpH,MAAM,CAAC,CAAC,GAAG,CAACoE,EAAE6B,IAAI,CAACmB,EAAEpH,MAAM,EAAG,MAAM,UAAc,4BAA4BwH,GAAG,YAAYJ,EAAEpH,MAAM,CAAC,GAAGoH,EAAEzH,IAAI,CAAC,CAAC,GAAG,CAACyE,EAAE6B,IAAI,CAACmB,EAAEzH,IAAI,EAAG,MAAM,UAAc,0BAA0B6H,GAAG,UAAUJ,EAAEzH,IAAI,CAAC,GAAGyH,EAAExH,OAAO,CAAC,CAAC,GAAG,mBAAOwH,EAAExH,OAAO,CAACE,WAAW,CAAe,MAAM,UAAc,6BAA6B0H,GAAG,aAAaJ,EAAExH,OAAO,CAACE,WAAW,EAAE,CAA2D,GAAvDsH,EAAElH,QAAQ,EAAEsH,CAAAA,GAAG,YAAW,EAAKJ,EAAEnH,MAAM,EAAEuH,CAAAA,GAAG,UAAS,EAAKJ,EAAEjH,QAAQ,CAAyE,OAAjE,iBAAOiH,EAAEjH,QAAQ,CAAYiH,EAAEjH,QAAQ,CAACgC,WAAW,GAAGiF,EAAEjH,QAAQ,EAAW,IAAK,GAAsE,IAAI,SAArEqH,GAAG,oBAAoB,KAAM,KAAI,MAAMA,GAAG,iBAAiB,KAAgD,KAAI,OAAOA,GAAG,kBAAkB,KAAM,SAAQ,MAAM,UAAc,6BAA6B,CAAE,OAAOA,CAAC,EAAlmD,IAAID,EAAE9F,mBAAuBa,EAAE5B,mBAAuB0G,EAAE,MAAUhD,EAAE,uCAA0lD,KAAK3F,EAAOC,OAAO,CAACsI,CAAC,I,yCCN1tD,CAAC,KAAK,aAAa,IAAIA,EAAE,CAAC,IAAIA,IAAI,IAAI1E,EAAEzD,OAAOO,SAAS,CAACC,cAAc,CAAC+E,EAAE,IAAI,SAASiE,IAAS,CAA2F,SAASC,EAAGtB,CAAC,CAAC1E,CAAC,CAAC8B,CAAC,EAAE,IAAI,CAACmE,EAAE,CAACvB,EAAE,IAAI,CAACwB,OAAO,CAAClG,EAAE,IAAI,CAACmG,IAAI,CAACrE,GAAG,EAAK,CAAC,SAASsE,EAAY1B,CAAC,CAAC1E,CAAC,CAAC2E,CAAC,CAACM,CAAC,CAACF,CAAC,EAAE,GAAG,mBAAOJ,EAAgB,MAAM,UAAc,mCAAmC,IAAIE,EAAE,IAAImB,EAAGrB,EAAEM,GAAGP,EAAEK,GAAGK,EAAEtD,EAAEA,EAAE9B,EAAEA,EAAoI,OAA9H0E,EAAE2B,OAAO,CAACjB,EAAE,CAA0CV,EAAE2B,OAAO,CAACjB,EAAE,CAACa,EAAE,CAA2BvB,EAAE2B,OAAO,CAACjB,EAAE,CAAC,CAACV,EAAE2B,OAAO,CAACjB,EAAE,CAACP,EAAE,CAAvDH,EAAE2B,OAAO,CAACjB,EAAE,CAACvB,IAAI,CAACgB,GAA3EH,CAAAA,EAAE2B,OAAO,CAACjB,EAAE,CAACP,EAAEH,EAAE4B,YAAY,EAAC,EAA0F5B,CAAC,CAAC,SAAS6B,EAAW7B,CAAC,CAAC1E,CAAC,EAAK,KAAE0E,EAAE4B,YAAY,CAAK5B,EAAE2B,OAAO,CAAC,IAAIN,EAAY,OAAOrB,EAAE2B,OAAO,CAACrG,EAAE,CAAC,SAASwG,IAAe,IAAI,CAACH,OAAO,CAAC,IAAIN,EAAO,IAAI,CAACO,YAAY,CAAC,CAAC,CAArlB/J,OAAOkK,MAAM,GAAEV,EAAOjJ,SAAS,CAACP,OAAOkK,MAAM,CAAC,MAAU,CAAC,IAAIV,CAAK,EAAGW,SAAS,EAAC5E,CAAAA,EAAE,EAAI,GAAigB0E,EAAa1J,SAAS,CAAC6J,UAAU,CAAC,WAAsB,IAAShC,EAAEM,EAAPP,EAAE,EAAE,CAAK,GAAG,QAAI,CAAC4B,YAAY,CAAK,OAAO5B,EAAE,IAAIO,KAAKN,EAAE,IAAI,CAAC0B,OAAO,CAAKrG,EAAEiB,IAAI,CAAC0D,EAAEM,IAAGP,EAAEb,IAAI,CAAC/B,EAAEmD,EAAE/F,KAAK,CAAC,GAAG+F,UAAG,OAAU2B,qBAAqB,CAASlC,EAAEmC,MAAM,CAACtK,OAAOqK,qBAAqB,CAACjC,IAAWD,CAAC,EAAE8B,EAAa1J,SAAS,CAACgK,SAAS,CAAC,SAAmBpC,CAAC,EAAE,IAAI1E,EAAE8B,EAAEA,EAAE4C,EAAEA,EAAEC,EAAE,IAAI,CAAC0B,OAAO,CAACrG,EAAE,CAAC,GAAG,CAAC2E,EAAE,MAAM,EAAE,CAAC,GAAGA,EAAEsB,EAAE,CAAC,MAAM,CAACtB,EAAEsB,EAAE,CAAC,CAAC,IAAI,IAAIhB,EAAE,EAAEF,EAAEJ,EAAErG,MAAM,CAACuG,EAAE,MAAUE,GAAGE,EAAEF,EAAEE,IAAKJ,CAAC,CAACI,EAAE,CAACN,CAAC,CAACM,EAAE,CAACgB,EAAE,CAAC,OAAOpB,CAAC,EAAE2B,EAAa1J,SAAS,CAACiK,aAAa,CAAC,SAAuBrC,CAAC,EAAE,IAAI1E,EAAE8B,EAAEA,EAAE4C,EAAEA,EAAEC,EAAE,IAAI,CAAC0B,OAAO,CAACrG,EAAE,QAAC,EAAkB2E,EAAEsB,EAAE,CAAQ,EAAStB,EAAErG,MAAM,CAAlC,CAAkC,EAAEkI,EAAa1J,SAAS,CAACkK,IAAI,CAAC,SAActC,CAAC,CAAC1E,CAAC,CAAC2E,CAAC,CAACM,CAAC,CAACF,CAAC,CAACF,CAAC,EAAE,IAAIO,EAAEtD,EAAEA,EAAE4C,EAAEA,EAAE,GAAG,CAAC,IAAI,CAAC2B,OAAO,CAACjB,EAAE,CAAC,MAAO,GAAM,IAAyClI,EAAE+J,EAAvCnC,EAAE,IAAI,CAACuB,OAAO,CAACjB,EAAE,CAAC8B,EAAEC,UAAU7I,MAAM,CAAK,GAAGwG,EAAEmB,EAAE,CAAC,CAAsD,OAAlDnB,EAAEqB,IAAI,EAAC,IAAI,CAACiB,cAAc,CAAC1C,EAAEI,EAAEmB,EAAE,CAACV,KAAAA,EAAU,IAAa2B,GAAG,KAAK,EAAE,OAAOpC,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,EAAE,EAAK,MAAK,EAAE,OAAOpB,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,CAAClG,GAAG,EAAK,MAAK,EAAE,OAAO8E,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,CAAClG,EAAE2E,GAAG,EAAK,MAAK,EAAE,OAAOG,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,CAAClG,EAAE2E,EAAEM,GAAG,EAAK,MAAK,EAAE,OAAOH,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,CAAClG,EAAE2E,EAAEM,EAAEF,GAAG,EAAK,MAAK,EAAE,OAAOD,EAAEmB,EAAE,CAAChF,IAAI,CAAC6D,EAAEoB,OAAO,CAAClG,EAAE2E,EAAEM,EAAEF,EAAEF,GAAG,EAAI,CAAC,IAAIoC,EAAE,EAAE/J,EAAE,MAAUgK,EAAE,GAAGD,EAAEC,EAAED,IAAK/J,CAAC,CAAC+J,EAAE,EAAE,CAACE,SAAS,CAACF,EAAE,CAACnC,EAAEmB,EAAE,CAACoB,KAAK,CAACvC,EAAEoB,OAAO,CAAChJ,EAAE,KAAK,CAAC,IAAeiI,EAAXtD,EAAEiD,EAAExG,MAAM,CAAG,IAAI2I,EAAE,EAAEA,EAAEpF,EAAEoF,IAAgE,OAAxDnC,CAAC,CAACmC,EAAE,CAACd,IAAI,EAAC,IAAI,CAACiB,cAAc,CAAC1C,EAAEI,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAACV,KAAAA,EAAU,IAAa2B,GAAG,KAAK,EAAEpC,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAAChF,IAAI,CAAC6D,CAAC,CAACmC,EAAE,CAACf,OAAO,EAAE,KAAM,MAAK,EAAEpB,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAAChF,IAAI,CAAC6D,CAAC,CAACmC,EAAE,CAACf,OAAO,CAAClG,GAAG,KAAM,MAAK,EAAE8E,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAAChF,IAAI,CAAC6D,CAAC,CAACmC,EAAE,CAACf,OAAO,CAAClG,EAAE2E,GAAG,KAAM,MAAK,EAAEG,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAAChF,IAAI,CAAC6D,CAAC,CAACmC,EAAE,CAACf,OAAO,CAAClG,EAAE2E,EAAEM,GAAG,KAAM,SAAQ,GAAG,CAAC/H,EAAE,IAAIiI,EAAE,EAAEjI,EAAE,MAAUgK,EAAE,GAAG/B,EAAE+B,EAAE/B,IAAKjI,CAAC,CAACiI,EAAE,EAAE,CAACgC,SAAS,CAAChC,EAAE,CAACL,CAAC,CAACmC,EAAE,CAAChB,EAAE,CAACoB,KAAK,CAACvC,CAAC,CAACmC,EAAE,CAACf,OAAO,CAAChJ,EAAE,CAAE,CAAC,MAAO,EAAI,EAAEsJ,EAAa1J,SAAS,CAACwK,EAAE,CAAC,SAAY5C,CAAC,CAAC1E,CAAC,CAAC8B,CAAC,EAAE,OAAOsE,EAAY,IAAI,CAAC1B,EAAE1E,EAAE8B,EAAE,GAAM,EAAE0E,EAAa1J,SAAS,CAACqJ,IAAI,CAAC,SAAczB,CAAC,CAAC1E,CAAC,CAAC8B,CAAC,EAAE,OAAOsE,EAAY,IAAI,CAAC1B,EAAE1E,EAAE8B,EAAE,GAAK,EAAE0E,EAAa1J,SAAS,CAACsK,cAAc,CAAC,SAAwB1C,CAAC,CAAC1E,CAAC,CAAC2E,CAAC,CAACM,CAAC,EAAE,IAAIF,EAAEjD,EAAEA,EAAE4C,EAAEA,EAAE,GAAG,CAAC,IAAI,CAAC2B,OAAO,CAACtB,EAAE,CAAC,OAAO,IAAI,CAAC,GAAG,CAAC/E,EAAsB,OAAnBuG,EAAW,IAAI,CAACxB,GAAU,IAAI,CAAC,IAAIF,EAAE,IAAI,CAACwB,OAAO,CAACtB,EAAE,CAAC,GAAGF,EAAEoB,EAAE,CAAKpB,EAAEoB,EAAE,GAAGjG,GAAI,IAAI6E,EAAEsB,IAAI,EAAI,GAAItB,EAAEqB,OAAO,GAAGvB,GAAI4B,EAAW,IAAI,CAACxB,OAAQ,CAAC,IAAI,IAAIK,EAAE,EAAEN,EAAE,EAAE,CAACoC,EAAErC,EAAEvG,MAAM,CAAC8G,EAAE8B,EAAE9B,IAAQP,CAAAA,CAAC,CAACO,EAAE,CAACa,EAAE,GAAGjG,GAAGiF,GAAG,CAACJ,CAAC,CAACO,EAAE,CAACe,IAAI,EAAExB,GAAGE,CAAC,CAACO,EAAE,CAACc,OAAO,GAAGvB,CAAAA,GAAGG,EAAEjB,IAAI,CAACgB,CAAC,CAACO,EAAE,CAAMN,CAAAA,EAAExG,MAAM,CAAC,IAAI,CAAC+H,OAAO,CAACtB,EAAE,CAACD,IAAAA,EAAExG,MAAM,CAAKwG,CAAC,CAAC,EAAE,CAACA,EAAOyB,EAAW,IAAI,CAACxB,EAAE,CAAC,OAAO,IAAI,EAAEyB,EAAa1J,SAAS,CAACyK,kBAAkB,CAAC,SAA4B7C,CAAC,EAAE,IAAI1E,EAAyG,OAApG0E,GAAG1E,EAAE8B,EAAEA,EAAE4C,EAAEA,EAAK,IAAI,CAAC2B,OAAO,CAACrG,EAAE,EAACuG,EAAW,IAAI,CAACvG,KAAQ,IAAI,CAACqG,OAAO,CAAC,IAAIN,EAAO,IAAI,CAACO,YAAY,CAAC,GAAS,IAAI,EAAEE,EAAa1J,SAAS,CAAC0K,GAAG,CAAChB,EAAa1J,SAAS,CAACsK,cAAc,CAACZ,EAAa1J,SAAS,CAACsJ,WAAW,CAACI,EAAa1J,SAAS,CAACwK,EAAE,CAACd,EAAaiB,QAAQ,CAAC3F,EAAE0E,EAAaA,YAAY,CAACA,EAAsB9B,EAAEtI,OAAO,CAACoK,CAAa,EAAE,IAAI9B,IAAIA,EAAEtI,OAAO,CAAC,CAACsI,EAAE1E,KAAKA,EAAEA,GAAI,MAAK,GAAU0E,EAAEgD,IAAI,CAAEhD,GAAG,IAAIiD,QAASjD,IAAIA,EAAE1E,IAAI,GAAI0H,IAAI,CAAE,IAAIhD,GAAMA,GAAG,IAAIiD,QAASjD,IAAIA,EAAE1E,IAAI,GAAI0H,IAAI,CAAE,KAAK,MAAMhD,CAAC,IAAM,EAAE,IAAI,CAACA,EAAE1E,KAAKzD,OAAOC,cAAc,CAACwD,EAAE,aAAa,CAAC3B,MAAM,EAAI,GAAyI2B,EAAE,OAAU,CAAlJ,SAAoB0E,CAAC,CAAC1E,CAAC,CAAC8B,CAAC,EAAE,IAAI6C,EAAE,EAAMM,EAAEP,EAAEpG,MAAM,CAAC,KAAM2G,EAAE,GAAE,CAAC,IAAMF,EAAEE,EAAE,EAAE,EAAMJ,EAAEF,EAAEI,CAAKjD,CAAW,GAAXA,EAAE4C,CAAC,CAACG,EAAE,CAAC7E,IAAO2E,EAAE,EAAEE,EAAEI,GAAGF,EAAE,GAAOE,EAAEF,CAAE,CAAC,OAAOJ,CAAC,CAAwB,EAAE,IAAI,CAACD,EAAE1E,EAAE8B,KAAKvF,OAAOC,cAAc,CAACwD,EAAE,aAAa,CAAC3B,MAAM,EAAI,GAAG,IAAMsG,EAAE7C,EAAE,IAAggB9B,CAAAA,EAAE,OAAU,CAAvgB,MAAoBkB,aAAa,CAAC,IAAI,CAAC0G,MAAM,CAAC,EAAE,CAACC,QAAQnD,CAAC,CAAC1E,CAAC,CAAC,CAAiC,IAAM8B,EAAE,CAAC/D,SAASiC,CAAlDA,EAAEzD,OAAOuL,MAAM,CAAC,CAAC/J,SAAS,CAAC,EAAEiC,EAAC,EAAsBjC,QAAQ,CAACgK,IAAIrD,CAAC,EAAE,GAAG,IAAI,CAACjD,IAAI,EAAE,IAAI,CAACmG,MAAM,CAAC,IAAI,CAACnG,IAAI,CAAC,EAAE,CAAC1D,QAAQ,EAAEiC,EAAEjC,QAAQ,CAAC,CAAC,IAAI,CAAC6J,MAAM,CAAC/D,IAAI,CAAC/B,GAAG,MAAM,CAAC,IAAMmD,EAAEN,EAAEqD,OAAO,CAAC,IAAI,CAACJ,MAAM,CAAC9F,EAAG,CAAC4C,EAAE1E,IAAIA,EAAEjC,QAAQ,CAAC2G,EAAE3G,QAAQ,EAAG,IAAI,CAAC6J,MAAM,CAACK,MAAM,CAAChD,EAAE,EAAEnD,EAAE,CAACoG,SAAS,CAAC,IAAMxD,EAAE,IAAI,CAACkD,MAAM,CAACO,KAAK,GAAG,OAAOzD,MAAAA,EAAqB,KAAK,EAAEA,EAAEqD,GAAG,CAAC/J,OAAO0G,CAAC,CAAC,CAAC,OAAO,IAAI,CAACkD,MAAM,CAAC5J,MAAM,CAAEgC,GAAGA,EAAEjC,QAAQ,GAAG2G,EAAE3G,QAAQ,EAAGW,GAAG,CAAEgG,GAAGA,EAAEqD,GAAG,CAAE,CAAC,IAAItG,MAAM,CAAC,OAAO,IAAI,CAACmG,MAAM,CAACtJ,MAAM,CAAC,CAA2B,EAAE,IAAI,CAACoG,EAAE1E,EAAE8B,KAAK,IAAM6C,EAAE7C,EAAE,IAAK,OAAMsG,UAAqBC,MAAMnH,YAAYwD,CAAC,CAAC,CAAC,KAAK,CAACA,GAAG,IAAI,CAACvG,IAAI,CAAC,cAAc,CAAC,CAAC,IAAMmK,EAAS,CAAC5D,EAAE1E,EAAE8B,IAAI,IAAI6F,QAAS,CAAC1C,EAAEF,KAAK,GAAG,iBAAO/E,GAAcA,EAAE,EAAG,MAAM,UAAc,mDAAmD,GAAGA,IAAIuI,IAAS,CAACtD,EAAEP,GAAG,MAAM,CAAC,IAAMG,EAAE2D,WAAY,KAAK,GAAG,mBAAO1G,EAAe,CAAC,GAAG,CAACmD,EAAEnD,IAAI,CAAC,MAAM4C,EAAE,CAACK,EAAEL,EAAE,CAAC,MAAM,CAAC,IAAMC,EAAE,iBAAO7C,EAAaA,EAAE,CAAC,wBAAwB,EAAE9B,EAAE,aAAa,CAAC,CAAO6E,EAAE/C,aAAauG,MAAMvG,EAAE,IAAIsG,EAAazD,EAAwB,aAAlB,OAAOD,EAAE+D,MAAM,EAAe/D,EAAE+D,MAAM,GAAG1D,EAAEF,EAAE,EAAG7E,GAAG2E,EAAED,EAAEgD,IAAI,CAACzC,EAAEF,GAAI,KAAK2D,aAAa7D,EAAE,EAAG,EAAIH,CAAAA,EAAEtI,OAAO,CAACkM,EAAS5D,EAAEtI,OAAO,CAAC,OAAU,CAACkM,EAAS5D,EAAEtI,OAAO,CAACgM,YAAY,CAACA,CAAY,CAAC,EAAMpI,EAAE,CAAC,EAAE,SAASuE,EAAoBzC,CAAC,EAAE,IAAI6C,EAAE3E,CAAC,CAAC8B,EAAE,CAAC,GAAG6C,KAAIY,IAAJZ,EAAe,OAAOA,EAAEvI,OAAO,CAAC,IAAI6I,EAAEjF,CAAC,CAAC8B,EAAE,CAAC,CAAC1F,QAAQ,CAAC,CAAC,EAAM2I,EAAE,GAAK,GAAG,CAACL,CAAC,CAAC5C,EAAE,CAACmD,EAAEA,EAAE7I,OAAO,CAACmI,GAAqBQ,EAAE,EAAK,QAAQ,CAAIA,GAAE,OAAO/E,CAAC,CAAC8B,EAAE,CAAC,OAAOmD,EAAE7I,OAAO,CAA6CmI,EAAoBC,EAAE,CAACC,UAAU,IAAI,IAAI3C,EAAE,CAAC,EAAE,CAAC,KAAavF,OAAOC,cAAc,CAAvBsF,EAA0B,aAAa,CAACzD,MAAM,EAAI,GAAG,IAAM2B,EAAEuE,EAAoB,KAAWI,EAAEJ,EAAoB,KAAWU,EAAEV,EAAoB,KAAWoE,EAAM,KAAK,EAAQ5D,EAAE,IAAIJ,EAAEyD,YAAY,CAArMtG,EAA6/I,OAAU,CAAj0I,cAAqB9B,EAAEkB,YAAYwD,CAAC,CAAC,CAAC,IAAI1E,EAAE8B,EAAE6C,EAAEI,EAAwQ,GAAtQ,KAAK,GAAG,IAAI,CAAC6D,cAAc,CAAC,EAAE,IAAI,CAACC,YAAY,CAAC,EAAE,IAAI,CAACC,aAAa,CAAC,EAAE,IAAI,CAACC,aAAa,CAACJ,EAAM,IAAI,CAACK,YAAY,CAACL,EAAuJ,CAAE,iBAAOjE,CAA1JA,EAAEnI,OAAOuL,MAAM,CAAC,CAACmB,0BAA0B,GAAMC,YAAYX,IAASY,SAAS,EAAEC,YAAYb,IAASc,UAAU,GAAKC,WAAWrE,EAAE+C,OAAO,EAAEtD,EAAC,EAAgBwE,WAAW,EAAaxE,EAAEwE,WAAW,EAAE,GAAI,MAAM,UAAc,CAAC,6DAA6D,EAAE,OAACpH,CAAAA,EAAE,OAAC9B,CAAAA,EAAE0E,EAAEwE,WAAW,GAAUlJ,KAAS,IAATA,EAAW,KAAK,EAAEA,EAAEyC,QAAQ,EAAC,GAAWX,KAAS,IAATA,EAAWA,EAAE,GAAG,IAAI,EAAE,OAAO4C,EAAEwE,WAAW,CAAC,CAAC,CAAC,EAAE,GAAGxE,KAAaa,IAAbb,EAAEyE,QAAQ,EAAc,CAAEjJ,CAAAA,OAAO0F,QAAQ,CAAClB,EAAEyE,QAAQ,GAAGzE,EAAEyE,QAAQ,EAAE,GAAI,MAAM,UAAc,CAAC,wDAAwD,EAAE,OAACpE,CAAAA,EAAE,OAACJ,CAAAA,EAAED,EAAEyE,QAAQ,GAAUxE,KAAS,IAATA,EAAW,KAAK,EAAEA,EAAElC,QAAQ,EAAC,GAAWsC,KAAS,IAATA,EAAWA,EAAE,GAAG,IAAI,EAAE,OAAOL,EAAEyE,QAAQ,CAAC,CAAC,CAAC,CAAE,KAAI,CAACI,0BAA0B,CAAC7E,EAAEuE,yBAAyB,CAAC,IAAI,CAACO,kBAAkB,CAAC9E,EAAEwE,WAAW,GAAGX,KAAU7D,IAAAA,EAAEyE,QAAQ,CAAK,IAAI,CAACM,YAAY,CAAC/E,EAAEwE,WAAW,CAAC,IAAI,CAACQ,SAAS,CAAChF,EAAEyE,QAAQ,CAAC,IAAI,CAACvB,MAAM,CAAC,IAAIlD,EAAE4E,UAAU,CAAC,IAAI,CAACK,WAAW,CAACjF,EAAE4E,UAAU,CAAC,IAAI,CAACF,WAAW,CAAC1E,EAAE0E,WAAW,CAAC,IAAI,CAACQ,QAAQ,CAAClF,EAAEmF,OAAO,CAAC,IAAI,CAACC,eAAe,CAACpF,CAAmB,IAAnBA,EAAEqF,cAAc,CAAQ,IAAI,CAACC,SAAS,CAACtF,CAAc,IAAdA,EAAE2E,SAAS,CAAS,IAAIY,2BAA2B,CAAC,OAAO,IAAI,CAACT,kBAAkB,EAAE,IAAI,CAACZ,cAAc,CAAC,IAAI,CAACa,YAAY,CAAC,IAAIS,6BAA6B,CAAC,OAAO,IAAI,CAACpB,aAAa,CAAC,IAAI,CAACqB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACtB,aAAa,GAAG,IAAI,CAACuB,kBAAkB,GAAG,IAAI,CAACrD,IAAI,CAAC,OAAO,CAACsD,kBAAkB,CAAC,IAAI,CAACvB,aAAa,GAAG,IAAI,CAACA,aAAa,CAACJ,EAA8B,IAArB,IAAI,CAACG,aAAa,GAAM,IAAI,CAACE,YAAY,GAAG,IAAI,CAACA,YAAY,CAACL,EAAM,IAAI,CAAC3B,IAAI,CAAC,QAAQ,CAACuD,mBAAmB,CAAC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACC,2BAA2B,GAAG,IAAI,CAACC,UAAU,CAACnF,KAAAA,CAAS,CAACoF,mBAAmB,CAAC,IAAMjG,EAAEnH,KAAK0G,GAAG,GAAG,GAAG,KAAmBsB,IAAnB,IAAI,CAACqF,WAAW,CAAa,CAAC,IAAM5K,EAAE,IAAI,CAAC6I,YAAY,CAACnE,EAAE,IAAG1E,CAAAA,EAAE,GAAgL,OAA1EuF,KAAAA,IAAlB,IAAI,CAACmF,UAAU,EAAc,KAAI,CAACA,UAAU,CAAClC,WAAY,KAAK,IAAI,CAAC+B,iBAAiB,EAAE,EAAGvK,EAAC,EAAS,EAApL,KAAI,CAAC4I,cAAc,CAAC,IAAI,CAACW,0BAA0B,CAAC,IAAI,CAACT,aAAa,CAAC,CAAkH,CAAC,MAAO,EAAK,CAACuB,oBAAoB,CAAC,GAAG,QAAI,CAACzC,MAAM,CAACnG,IAAI,CAA8G,OAArG,IAAI,CAACmJ,WAAW,EAAEC,cAAc,IAAI,CAACD,WAAW,EAAE,IAAI,CAACA,WAAW,CAACrF,KAAAA,EAAU,IAAI,CAAC+E,gBAAgB,GAAU,GAAM,GAAG,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC,IAAMtF,EAAE,CAAC,IAAI,CAACiG,iBAAiB,GAAG,GAAG,IAAI,CAACV,yBAAyB,EAAE,IAAI,CAACC,2BAA2B,CAAC,CAAC,IAAMlK,EAAE,IAAI,CAAC4H,MAAM,CAACM,OAAO,SAAG,EAAIlI,IAAgB,IAAI,CAACgH,IAAI,CAAC,UAAUhH,IAAO0E,GAAG,IAAI,CAAC+F,2BAA2B,GAAU,GAAI,CAAC,CAAC,MAAO,EAAK,CAACA,6BAA6B,CAAI,IAAI,CAACjB,kBAAkB,EAAE,KAAmBjE,IAAnB,IAAI,CAACqF,WAAW,GAAqB,IAAI,CAACA,WAAW,CAACE,YAAa,KAAK,IAAI,CAACN,WAAW,EAAE,EAAG,IAAI,CAACd,SAAS,EAAE,IAAI,CAACb,YAAY,CAACtL,KAAK0G,GAAG,GAAG,IAAI,CAACyF,SAAS,EAACc,aAAa,CAA0B,IAAtB,IAAI,CAAC5B,cAAc,EAAM,QAAI,CAACE,aAAa,EAAM,IAAI,CAAC8B,WAAW,GAAEC,cAAc,IAAI,CAACD,WAAW,EAAE,IAAI,CAACA,WAAW,CAACrF,KAAAA,GAAU,IAAI,CAACqD,cAAc,CAAC,IAAI,CAACW,0BAA0B,CAAC,IAAI,CAACT,aAAa,CAAC,EAAE,IAAI,CAACiC,aAAa,EAAE,CAACA,eAAe,CAAC,KAAM,IAAI,CAACV,kBAAkB,KAAK,CAAC,IAAIjB,aAAa,CAAC,OAAO,IAAI,CAACe,YAAY,CAAC,IAAIf,YAAY1E,CAAC,CAAC,CAAC,GAAG,CAAE,kBAAOA,GAAcA,GAAG,GAAI,MAAM,UAAc,CAAC,6DAA6D,EAAEA,EAAE,IAAI,EAAE,OAAOA,EAAE,CAAC,CAAC,CAAE,KAAI,CAACyF,YAAY,CAACzF,EAAE,IAAI,CAACqG,aAAa,EAAE,CAAC,MAAMC,IAAItG,CAAC,CAAC1E,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,IAAI2H,QAAS,CAAC7F,EAAEmD,KAAK,IAAM8C,EAAI,UAAU,IAAI,CAACe,aAAa,GAAG,IAAI,CAACF,cAAc,GAAG,GAAG,CAAC,IAAM/D,EAAE,KAAgBU,IAAhB,IAAI,CAACqE,QAAQ,EAAc5J,KAAYuF,IAAZvF,EAAE6J,OAAO,CAAanF,IAAIC,EAAEqD,OAAO,CAACL,QAAQsD,OAAO,CAACvG,KAAK1E,KAAYuF,IAAZvF,EAAE6J,OAAO,CAAa,IAAI,CAACD,QAAQ,CAAC5J,EAAE6J,OAAO,CAAE,KAAQ7J,CAAAA,KAAmBuF,IAAnBvF,EAAE+J,cAAc,CAAa,IAAI,CAACD,eAAe,CAAC9J,EAAE+J,cAAc,GAAE9E,EAAEF,EAAmB,GAAIjD,EAAE,MAAM+C,EAAE,CAAC,MAAMH,EAAE,CAACO,EAAEP,EAAE,CAAC,IAAI,CAAC0F,KAAK,EAAE,EAAE,IAAI,CAACxC,MAAM,CAACC,OAAO,CAACE,EAAI/H,GAAG,IAAI,CAACqK,kBAAkB,GAAG,IAAI,CAACrD,IAAI,CAAC,MAAM,EAAG,CAAC,MAAMkE,OAAOxG,CAAC,CAAC1E,CAAC,CAAC,CAAC,OAAO2H,QAAQnH,GAAG,CAACkE,EAAEhG,GAAG,CAAE,MAAMgG,GAAG,IAAI,CAACsG,GAAG,CAACtG,EAAE1E,IAAK,CAACmD,OAAO,QAAK,IAAI,CAAC6G,SAAS,GAAc,IAAI,CAACA,SAAS,CAAC,GAAM,IAAI,CAACe,aAAa,IAA5C,IAAI,CAAuDI,OAAO,CAAC,IAAI,CAACnB,SAAS,CAAC,EAAI,CAAC5H,OAAO,CAAC,IAAI,CAACwF,MAAM,CAAC,IAAI,IAAI,CAAC+B,WAAW,CAAC,MAAMyB,SAAS,CAAC,GAAG,QAAI,CAACxD,MAAM,CAACnG,IAAI,CAAa,OAAO,IAAIkG,QAASjD,IAAI,IAAM1E,EAAE,IAAI,CAAC+I,aAAa,CAAC,IAAI,CAACA,aAAa,CAAC,KAAK/I,IAAI0E,GAAG,CAAC,EAAG,CAAC,MAAM2G,QAAQ,CAAC,GAAG,QAAI,CAACvC,aAAa,EAAM,QAAI,CAAClB,MAAM,CAACnG,IAAI,CAAa,OAAO,IAAIkG,QAASjD,IAAI,IAAM1E,EAAE,IAAI,CAACgJ,YAAY,CAAC,IAAI,CAACA,YAAY,CAAC,KAAKhJ,IAAI0E,GAAG,CAAC,EAAG,CAAC,IAAIjD,MAAM,CAAC,OAAO,IAAI,CAACmG,MAAM,CAACnG,IAAI,CAAC6J,OAAO5G,CAAC,CAAC,CAAC,OAAO,IAAI,CAACkD,MAAM,CAAC5J,MAAM,CAAC0G,GAAGpG,MAAM,CAAC,IAAIiN,SAAS,CAAC,OAAO,IAAI,CAACzC,aAAa,CAAC,IAAI0C,UAAU,CAAC,OAAO,IAAI,CAACxB,SAAS,CAAC,IAAIH,SAAS,CAAC,OAAO,IAAI,CAACD,QAAQ,CAAC,IAAIC,QAAQnF,CAAC,CAAC,CAAC,IAAI,CAACkF,QAAQ,CAAClF,CAAC,CAAC,CAAoB,KAAKvI,EAAOC,OAAO,CAAC0F,CAAC,I,mFCA3rT;;;;;;;;CAQC,EAGD,IAAI2J,EAAqBlK,OAAOe,GAAG,CAAC,8BAClCoJ,EAAoBnK,OAAOe,GAAG,CAAC,gBAC/BqJ,EAAsBpK,OAAOe,GAAG,CAAC,kBACjCsJ,EAAyBrK,OAAOe,GAAG,CAAC,qBACpCuJ,EAAsBtK,OAAOe,GAAG,CAAC,kBACjCwJ,EAAsBvK,OAAOe,GAAG,CAAC,kBACjCyJ,EAAqBxK,OAAOe,GAAG,CAAC,iBAChC0J,EAAyBzK,OAAOe,GAAG,CAAC,qBACpC2J,EAAsB1K,OAAOe,GAAG,CAAC,kBACjC4J,EAA2B3K,OAAOe,GAAG,CAAC,uBACtC6J,EAAkB5K,OAAOe,GAAG,CAAC,cAC7B8J,EAAkB7K,OAAOe,GAAG,CAAC,cAC7B+J,EAAuB9K,OAAOe,GAAG,CAAC,mBAClCgK,EAAsB/K,OAAOe,GAAG,CAAC,kBACjCiK,EAA6BhL,OAAOe,GAAG,CAAC,yBACxCkK,EAAwBjL,OAAOC,QAAQ,CAQrCiL,EAAuB,CACvBC,UAAW,WACT,MAAO,CAAC,CACV,EACAC,mBAAoB,WAAa,EACjCC,oBAAqB,WAAa,EAClCC,gBAAiB,WAAa,CAChC,EACA/E,EAASvL,OAAOuL,MAAM,CACtBgF,EAAc,CAAC,EACjB,SAASC,EAAUC,CAAK,CAAE9G,CAAO,CAAE+G,CAAO,EACxC,IAAI,CAACD,KAAK,CAAGA,EACb,IAAI,CAAC9G,OAAO,CAAGA,EACf,IAAI,CAACgH,IAAI,CAAGJ,EACZ,IAAI,CAACG,OAAO,CAAGA,GAAWR,CAC5B,CAgBA,SAASU,IAAkB,CAE3B,SAASC,EAAcJ,CAAK,CAAE9G,CAAO,CAAE+G,CAAO,EAC5C,IAAI,CAACD,KAAK,CAAGA,EACb,IAAI,CAAC9G,OAAO,CAAGA,EACf,IAAI,CAACgH,IAAI,CAAGJ,EACZ,IAAI,CAACG,OAAO,CAAGA,GAAWR,CAC5B,CAtBAM,EAAUjQ,SAAS,CAACuQ,gBAAgB,CAAG,CAAC,EACxCN,EAAUjQ,SAAS,CAACwQ,QAAQ,CAAG,SAAUC,CAAY,CAAEC,CAAQ,EAC7D,GACE,UAAa,OAAOD,GACpB,YAAe,OAAOA,GACtB,MAAQA,EAER,MAAMlF,MACJ,0GAEJ,IAAI,CAAC4E,OAAO,CAACJ,eAAe,CAAC,IAAI,CAAEU,EAAcC,EAAU,WAC7D,EACAT,EAAUjQ,SAAS,CAAC2Q,WAAW,CAAG,SAAUD,CAAQ,EAClD,IAAI,CAACP,OAAO,CAACN,kBAAkB,CAAC,IAAI,CAAEa,EAAU,cAClD,EAEAL,EAAerQ,SAAS,CAAGiQ,EAAUjQ,SAAS,CAO9C,IAAI4Q,EAA0BN,EAActQ,SAAS,CAAG,IAAIqQ,CAC5DO,CAAAA,EAAuBxM,WAAW,CAAGkM,EACrCtF,EAAO4F,EAAwBX,EAAUjQ,SAAS,EAClD4Q,EAAuBC,oBAAoB,CAAG,CAAC,EAC/C,IAAIC,EAAchM,MAAMO,OAAO,CAC7B0L,EAAuB,CAAEC,EAAG,KAAMC,EAAG,KAAMC,EAAG,KAAMC,EAAG,KAAMC,EAAG,IAAK,EACrEnR,EAAiBR,OAAOO,SAAS,CAACC,cAAc,CAClD,SAASoR,EAAaC,CAAI,CAAEnP,CAAG,CAAEoP,CAAI,CAAEC,CAAM,CAAEC,CAAK,CAAEvB,CAAK,EAEzD,MAAO,CACLwB,SAAU/C,EACV2C,KAAMA,EACNnP,IAAKA,EACLwP,IAAK,KAAK,IALZJ,CAAAA,EAAOrB,EAAMyB,GAAG,EAKSJ,EAAO,KAC9BrB,MAAOA,CACT,CACF,CAWA,SAAS0B,EAAeC,CAAM,EAC5B,MACE,UAAa,OAAOA,GACpB,OAASA,GACTA,EAAOH,QAAQ,GAAK/C,CAExB,CAUA,IAAImD,EAA6B,OACjC,SAASC,EAAcC,CAAO,CAAEC,CAAK,MAVrB9P,EACV+P,EAUJ,MAAO,UAAa,OAAOF,GAAW,OAASA,GAAW,MAAQA,EAAQ7P,GAAG,EAX/DA,EAYH,GAAK6P,EAAQ7P,GAAG,CAXvB+P,EAAgB,CAAE,IAAK,KAAM,IAAK,IAAK,EAEzC,IACA/P,EAAIa,OAAO,CAAC,QAAS,SAAUmP,CAAK,EAClC,OAAOD,CAAa,CAACC,EAAM,IAQ3BF,EAAMtM,QAAQ,CAAC,GACrB,CACA,SAASyM,IAAU,CA8InB,SAASC,EAAYC,CAAQ,CAAEC,CAAI,CAAEnJ,CAAO,EAC1C,GAAI,MAAQkJ,EAAU,OAAOA,EAC7B,IAAIlN,EAAS,EAAE,CACboN,EAAQ,EAIV,OAHAC,SAjHOA,EAAaH,CAAQ,CAAEI,CAAK,CAAEC,CAAa,CAAEC,CAAS,CAAElC,CAAQ,EACvE,IAlE0BmC,EAAYC,EA/DjBC,EAiIjBzB,EAAO,OAAOgB,EACd,eAAgBhB,GAAQ,YAAcA,CAAG,GAAGgB,CAAAA,EAAW,IAAG,EAC9D,IAAIU,EAAiB,CAAC,EACtB,GAAI,OAASV,EAAUU,EAAiB,CAAC,OAEvC,OAAQ1B,GACN,IAAK,SACL,IAAK,SACL,IAAK,SACH0B,EAAiB,CAAC,EAClB,KACF,KAAK,SACH,OAAQV,EAASZ,QAAQ,EACvB,KAAK/C,EACL,KAAKC,EACHoE,EAAiB,CAAC,EAClB,KACF,MAAK1D,EACH,OACE,EAEE0D,CAFDA,EAAiBV,EAASW,KAAK,EAEfX,EAASY,QAAQ,EAChCR,EACAC,EACAC,EACAlC,EAGR,CACJ,CACF,GAAIsC,EACF,OACE,EAAYtC,EAAS4B,GACpBU,EACC,KAAOJ,EAAY,IAAMb,EAAcO,EAAU,GAAKM,EACxD9B,EAAYJ,GACP,GAAiB,GAClB,MAAQsC,GACLL,CAAAA,EACCK,EAAehQ,OAAO,CAAC8O,EAA4B,OAAS,GAAE,EAClEW,EAAa/B,EAAUgC,EAAOC,EAAe,GAAI,SAAUvS,CAAC,EAC1D,OAAOA,CACT,EAAC,EACD,MAAQsQ,GACPkB,CAAAA,EAAelB,KA9GEmC,EAgHdnC,EAhH0BoC,EAiH1BH,EACG,OAAQjC,EAASvO,GAAG,EACpBmQ,GAAYA,EAASnQ,GAAG,GAAKuO,EAASvO,GAAG,CACtC,GACA,CAAC,GAAKuO,EAASvO,GAAG,EAAEa,OAAO,CACzB8O,EACA,OACE,GAAE,EACVkB,EAVHtC,EA9GJW,EACLwB,EAAWvB,IAAI,CACfwB,EACA,KAAK,EACL,KAAK,EACL,KAAK,EACLD,EAAW3C,KAAK,GAoHVwC,EAAM3L,IAAI,CAAC2J,EAAQ,EACvB,EAEJsC,EAAiB,EACjB,IAAIG,EAAiB,KAAOP,EAAY,IAAMA,EAAY,IAC1D,GAAI9B,EAAYwB,GACd,IAAK,IAAInK,EAAI,EAAGA,EAAImK,EAAS9Q,MAAM,CAAE2G,IACnC,EACUgL,EAAiBpB,EAD1Ba,EAAYN,CAAQ,CAACnK,EAAE,CAC4BA,GACjD6K,GAAkBP,EACjBG,EACAF,EACAC,EACArB,EACAZ,QAEH,GAAK,YAA8C,MAA7CvI,CAAAA,EAzMX,QADqB4K,EA0MQT,IAzMC,UAAa,OAAOS,EAAsB,KAIjE,YAAe,MAHtBA,CAAAA,EACE,GAA0BA,CAAa,CAACrD,EAAsB,EAC9DqD,CAAa,CAAC,aAAa,EACgBA,EAAgB,IAqMxB,EACnC,IACET,EAAWnK,EAAEhE,IAAI,CAACmO,GAAWnK,EAAI,EACjC,CAAC,CAACyK,EAAYN,EAASc,IAAI,EAAC,EAAGC,IAAI,EAGnC,EACUF,EAAiBpB,EAD1Ba,EAAYA,EAAUrR,KAAK,CACwB4G,KACjD6K,GAAkBP,EACjBG,EACAF,EACAC,EACArB,EACAZ,QAEH,GAAI,WAAaY,EAAM,CAC1B,GAAI,YAAe,OAAOgB,EAAS1H,IAAI,CACrC,OAAO6H,EACLa,SA5HiBC,CAAQ,EAC/B,OAAQA,EAASC,MAAM,EACrB,IAAK,YACH,OAAOD,EAAShS,KAAK,KAClB,WACH,MAAMgS,EAASE,MAAM,SAErB,OACG,UAAa,OAAOF,EAASC,MAAM,CAChCD,EAAS3I,IAAI,CAACwH,EAAQA,GACrB,GAAUoB,MAAM,CAAG,UACpBD,EAAS3I,IAAI,CACX,SAAU8I,CAAc,EACtB,YAAcH,EAASC,MAAM,EAC1B,GAAUA,MAAM,CAAG,YACnBD,EAAShS,KAAK,CAAGmS,CAAc,CACpC,EACA,SAAUC,CAAK,EACb,YAAcJ,EAASC,MAAM,EAC1B,GAAUA,MAAM,CAAG,WAAcD,EAASE,MAAM,CAAGE,CAAK,CAC7D,EACF,EACJJ,EAASC,MAAM,EAEf,IAAK,YACH,OAAOD,EAAShS,KAAK,KAClB,WACH,MAAMgS,EAASE,MAAM,CAE7B,CACA,MAAMF,CACR,EA6FwBjB,GAChBI,EACAC,EACAC,EACAlC,EAGJ,OAAMnF,MACJ,kDACG,qBAHLmH,CAAAA,EAAQkB,OAAOtB,EAAQ,EAIf,qBAAuB7S,OAAO8F,IAAI,CAAC+M,GAAU7Q,IAAI,CAAC,MAAQ,IAC1DiR,CAAI,EACR,4EAEN,CACA,OAAOM,CACT,EAKeV,EAAUlN,EAAQ,GAAI,GAAI,SAAUyO,CAAK,EACpD,OAAOtB,EAAKpO,IAAI,CAACiF,EAASyK,EAAOrB,IACnC,GACOpN,CACT,CACA,SAAS0O,EAAgBC,CAAO,EAC9B,GAAI,KAAOA,EAAQC,OAAO,CAAE,CAC1B,IAAIC,EAAOF,EAAQG,OAAO,CAE1BD,CADAA,EAAOA,GAAK,EACPrJ,IAAI,CACP,SAAUuJ,CAAY,EAChB,KAAMJ,EAAQC,OAAO,EAAI,KAAOD,EAAQC,OAAO,GACjD,GAASA,OAAO,CAAG,EAAKD,EAAQG,OAAO,CAAGC,CAAY,CAC1D,EACA,SAAUR,CAAK,EACT,KAAMI,EAAQC,OAAO,EAAI,KAAOD,EAAQC,OAAO,GACjD,GAASA,OAAO,CAAG,EAAKD,EAAQG,OAAO,CAAGP,CAAK,CACnD,GAEF,KAAOI,EAAQC,OAAO,EAAK,GAASA,OAAO,CAAG,EAAKD,EAAQG,OAAO,CAAGD,CAAI,CAC3E,CACA,GAAI,IAAMF,EAAQC,OAAO,CAAE,OAAOD,EAAQG,OAAO,CAAChJ,OAAO,OACnD6I,EAAQG,OAAO,CAEvB,SAASE,EAAcC,CAAW,CAAEC,CAAO,EACzC,OAAOvD,EAAqBC,CAAC,CAACoD,aAAa,CAACC,EAAaC,EAC3D,CACA,IAAIC,EACF,YAAe,OAAOC,YAClBA,YACA,SAAUb,CAAK,EAiBN,GACL,UAAa,OAAOc,SACpB,YAAe,OAAOA,QAAQvK,IAAI,CAClC,CACAuK,QAAQvK,IAAI,CAAC,oBAAqByJ,GAClC,MACF,CACAe,QAAQf,KAAK,CAACA,EAChB,EACN,SAASgB,IAAQ,CACjBrV,EAAQsV,QAAQ,CAAG,CACjBhT,IAAKyQ,EACLwC,QAAS,SAAUvC,CAAQ,CAAEwC,CAAW,CAAEC,CAAc,EACtD1C,EACEC,EACA,WACEwC,EAAYvK,KAAK,CAAC,IAAI,CAAEF,UAC1B,EACA0K,EAEJ,EACAvC,MAAO,SAAUF,CAAQ,EACvB,IAAItN,EAAI,EAIR,OAHAqN,EAAYC,EAAU,WACpBtN,GACF,GACOA,CACT,EACAgQ,QAAS,SAAU1C,CAAQ,EACzB,OACED,EAAYC,EAAU,SAAUuB,CAAK,EACnC,OAAOA,CACT,IAAM,EAAE,EAGZoB,KAAM,SAAU3C,CAAQ,EACtB,GAAI,CAACV,EAAeU,GAClB,MAAM/G,MACJ,yEAEJ,OAAO+G,CACT,CACF,EACAhT,EAAQ2Q,SAAS,CAAGA,EACpB3Q,EAAQ4V,QAAQ,CAAGrG,EACnBvP,EAAQ6V,QAAQ,CAAGpG,EACnBzP,EAAQgR,aAAa,CAAGA,EACxBhR,EAAQ8V,UAAU,CAAGtG,EACrBxP,EAAQ+V,QAAQ,CAAGlG,EACnB7P,EAAQgW,+DAA+D,CACrEvE,EACFzR,EAAQiW,kBAAkB,CAAG,CAC3B3L,UAAW,KACXxJ,EAAG,SAAUuE,CAAI,EACf,OAAOoM,EAAqBC,CAAC,CAACwE,YAAY,CAAC7Q,EAC7C,CACF,EACArF,EAAQmW,KAAK,CAAG,SAAUtM,CAAE,EAC1B,OAAO,WACL,OAAOA,EAAGoB,KAAK,CAAC,KAAMF,UACxB,CACF,EACA/K,EAAQoW,YAAY,CAAG,SAAU1D,CAAO,CAAE2D,CAAM,CAAErD,CAAQ,EACxD,GAAI,MAASN,EACX,MAAMzG,MACJ,wDAA0DyG,EAAU,KAExE,IAAI9B,EAAQlF,EAAO,CAAC,EAAGgH,EAAQ9B,KAAK,EAClC/N,EAAM6P,EAAQ7P,GAAG,CACjBsP,EAAQ,KAAK,EACf,GAAI,MAAQkE,EACV,IAAKC,KAAa,KAAK,IAAMD,EAAOhE,GAAG,EAAKF,CAAAA,EAAQ,KAAK,GACzD,KAAK,IAAMkE,EAAOxT,GAAG,EAAKA,CAAAA,EAAM,GAAKwT,EAAOxT,GAAG,EAC/CwT,EACE,EAAgBxR,IAAI,CAACwR,EAAQC,IAC3B,QAAUA,GACV,WAAaA,GACb,aAAeA,GACd,SAAUA,GAAY,KAAK,IAAMD,EAAOhE,GAAG,GAC3CzB,CAAAA,CAAK,CAAC0F,EAAS,CAAGD,CAAM,CAACC,EAAS,EACzC,IAAIA,EAAWvL,UAAU7I,MAAM,CAAG,EAClC,GAAI,IAAMoU,EAAU1F,EAAMoC,QAAQ,CAAGA,OAChC,GAAI,EAAIsD,EAAU,CACrB,IAAK,IAAIC,EAAa/Q,MAAM8Q,GAAWzN,EAAI,EAAGA,EAAIyN,EAAUzN,IAC1D0N,CAAU,CAAC1N,EAAE,CAAGkC,SAAS,CAAClC,EAAI,EAAE,CAClC+H,EAAMoC,QAAQ,CAAGuD,CACnB,CACA,OAAOxE,EAAaW,EAAQV,IAAI,CAAEnP,EAAK,KAAK,EAAG,KAAK,EAAGsP,EAAOvB,EAChE,EACA5Q,EAAQwW,aAAa,CAAG,SAAUC,CAAY,EAc5C,MALAA,CARAA,EAAe,CACbrE,SAAUzC,EACV+G,cAAeD,EACfE,eAAgBF,EAChBG,aAAc,EACdC,SAAU,KACVC,SAAU,IACZ,GACaD,QAAQ,CAAGJ,EACxBA,EAAaK,QAAQ,CAAG,CACtB1E,SAAU1C,EACVqH,SAAUN,CACZ,EACOA,CACT,EACAzW,EAAQgX,aAAa,CAAG,SAAUhF,CAAI,CAAEqE,CAAM,CAAErD,CAAQ,EACtD,IAAIsD,EACF1F,EAAQ,CAAC,EACT/N,EAAM,KACR,GAAI,MAAQwT,EACV,IAAKC,KAAa,KAAK,IAAMD,EAAOxT,GAAG,EAAKA,CAAAA,EAAM,GAAKwT,EAAOxT,GAAG,EAAGwT,EAClE1V,EAAekE,IAAI,CAACwR,EAAQC,IAC1B,QAAUA,GACV,WAAaA,GACb,aAAeA,GACd1F,CAAAA,CAAK,CAAC0F,EAAS,CAAGD,CAAM,CAACC,EAAS,EACzC,IAAIW,EAAiBlM,UAAU7I,MAAM,CAAG,EACxC,GAAI,IAAM+U,EAAgBrG,EAAMoC,QAAQ,CAAGA,OACtC,GAAI,EAAIiE,EAAgB,CAC3B,IAAK,IAAIV,EAAa/Q,MAAMyR,GAAiBpO,EAAI,EAAGA,EAAIoO,EAAgBpO,IACtE0N,CAAU,CAAC1N,EAAE,CAAGkC,SAAS,CAAClC,EAAI,EAAE,CAClC+H,EAAMoC,QAAQ,CAAGuD,CACnB,CACA,GAAIvE,GAAQA,EAAKkF,YAAY,CAC3B,IAAKZ,KAAcW,EAAiBjF,EAAKkF,YAAY,CACnD,KAAK,IAAMtG,CAAK,CAAC0F,EAAS,EACvB1F,CAAAA,CAAK,CAAC0F,EAAS,CAAGW,CAAc,CAACX,EAAS,EACjD,OAAOvE,EAAaC,EAAMnP,EAAK,KAAK,EAAG,KAAK,EAAG,KAAM+N,EACvD,EACA5Q,EAAQmX,SAAS,CAAG,WAClB,MAAO,CAAEC,QAAS,IAAK,CACzB,EACApX,EAAQqX,2BAA2B,CAAG,SAAUjG,CAAQ,EACtD,OAAOK,EAAqBC,CAAC,CAAC4F,cAAc,CAAClG,EAC/C,EACApR,EAAQuX,0BAA0B,CAAG,SAAUxC,CAAW,CAAEC,CAAO,EACjE,OAAOF,EAAcC,EAAaC,EACpC,EACAhV,EAAQwX,UAAU,CAAG,SAAUC,CAAM,EACnC,MAAO,CAAErF,SAAUxC,EAAwB6H,OAAQA,CAAO,CAC5D,EACAzX,EAAQsS,cAAc,CAAGA,EACzBtS,EAAQ0X,IAAI,CAAG,SAAU/C,CAAI,EAC3B,MAAO,CACLvC,SAAUpC,EACV4D,SAAU,CAAEc,QAAS,GAAIE,QAASD,CAAK,EACvChB,MAAOa,CACT,CACF,EACAxU,EAAQ2X,IAAI,CAAG,SAAU3F,CAAI,CAAE4F,CAAO,EACpC,MAAO,CACLxF,SAAUrC,EACViC,KAAMA,EACN4F,QAAS,KAAK,IAAMA,EAAU,KAAOA,CACvC,CACF,EACA5X,EAAQ6X,eAAe,CAAG,SAAUC,CAAK,EACvC,IAAIC,EAAiBtG,EAAqBG,CAAC,CACzCoG,EAAoB,CAAC,CACvBvG,CAAAA,EAAqBG,CAAC,CAAGoG,EACzB,GAAI,CACF,IAAIC,EAAcH,IAChBI,EAA0BzG,EAAqBI,CAAC,QACzCqG,GACPA,EAAwBF,EAAmBC,GAC7C,UAAa,OAAOA,GAClB,OAASA,GACT,YAAe,OAAOA,EAAY3M,IAAI,EACtC2M,EAAY3M,IAAI,CAAC+J,EAAMJ,EAC3B,CAAE,MAAOZ,EAAO,CACdY,EAAkBZ,EACpB,QAAU,CACR5C,EAAqBG,CAAC,CAAGmG,CAC3B,CACF,EACA/X,EAAQmY,iBAAiB,CAAGlI,EAC5BjQ,EAAQoY,qBAAqB,CAAGtI,EAChC9P,EAAQqY,uBAAuB,CAAGlI,EAClCnQ,EAAQsY,0BAA0B,CAAG,SAAUtG,CAAI,EACjD,IAAIuG,EAAyB9G,EAAqBK,CAAC,QAC1CyG,EACJ9G,EAAqBK,CAAC,CAAG,CAACE,EAAK,CAChC,KAAOuG,EAAuB5V,OAAO,CAACqP,IACtCuG,EAAuB9Q,IAAI,CAACuK,EAClC,EACAhS,EAAQwY,wBAAwB,CAAG,SAAUC,CAAY,EACvD,IAAIC,EAAajH,EAAqBE,CAAC,CACvC,OAAO+G,EAAaA,EAAWC,eAAe,CAACF,GAAgBA,GACjE,EACAzY,EAAQ4Y,iBAAiB,CAAG,SAAUzE,CAAM,EAG1C,KADAA,CADAA,EAASlI,MAAMkI,EAAM,EACd/B,QAAQ,CAAGlC,EACZiE,CACR,EACAnU,EAAQ6Y,wBAAwB,CAAG,WACjC,OAAOpH,EAAqBC,CAAC,CAACoH,eAAe,EAC/C,EACA9Y,EAAQ+Y,2BAA2B,CAAG,SAAUC,CAAQ,CAAE5B,CAAO,CAAEtD,CAAI,EACrE,OAAOrC,EAAqBC,CAAC,CAACuH,kBAAkB,CAACD,EAAU5B,EAAStD,EACtE,EACA9T,EAAQkZ,GAAG,CAAG,SAAUC,CAAM,EAC5B,OAAO1H,EAAqBC,CAAC,CAACwH,GAAG,CAACC,EACpC,EACAnZ,EAAQoZ,cAAc,CAAG,SAAUC,CAAM,CAAEC,CAAY,CAAEC,CAAS,EAChE,OAAO9H,EAAqBC,CAAC,CAAC0H,cAAc,CAACC,EAAQC,EAAcC,EACrE,EACAvZ,EAAQwZ,WAAW,CAAG,SAAUpI,CAAQ,CAAEqI,CAAI,EAC5C,OAAOhI,EAAqBC,CAAC,CAAC8H,WAAW,CAACpI,EAAUqI,EACtD,EACAzZ,EAAQ0Z,UAAU,CAAG,SAAUC,CAAO,EACpC,OAAOlI,EAAqBC,CAAC,CAACgI,UAAU,CAACC,EAC3C,EACA3Z,EAAQ4Z,aAAa,CAAG,WAAa,EACrC5Z,EAAQ6Z,gBAAgB,CAAG,SAAU5X,CAAK,CAAE6X,CAAY,EACtD,OAAOrI,EAAqBC,CAAC,CAACmI,gBAAgB,CAAC5X,EAAO6X,EACxD,EACA9Z,EAAQ+Z,SAAS,CAAG,SAAU1P,CAAM,CAAE2P,CAAU,CAAEC,CAAM,EACtD,IAAIvB,EAAajH,EAAqBC,CAAC,CACvC,GAAI,YAAe,OAAOuI,EACxB,MAAMhO,MACJ,kEAEJ,OAAOyM,EAAWqB,SAAS,CAAC1P,EAAQ2P,EACtC,EACAha,EAAQka,KAAK,CAAG,WACd,OAAOzI,EAAqBC,CAAC,CAACwI,KAAK,EACrC,EACAla,EAAQma,mBAAmB,CAAG,SAAU9H,CAAG,CAAEhI,CAAM,CAAEoP,CAAI,EACvD,OAAOhI,EAAqBC,CAAC,CAACyI,mBAAmB,CAAC9H,EAAKhI,EAAQoP,EACjE,EACAzZ,EAAQoa,kBAAkB,CAAG,SAAU/P,CAAM,CAAEoP,CAAI,EACjD,OAAOhI,EAAqBC,CAAC,CAAC0I,kBAAkB,CAAC/P,EAAQoP,EAC3D,EACAzZ,EAAQqa,eAAe,CAAG,SAAUhQ,CAAM,CAAEoP,CAAI,EAC9C,OAAOhI,EAAqBC,CAAC,CAAC2I,eAAe,CAAChQ,EAAQoP,EACxD,EACAzZ,EAAQsa,OAAO,CAAG,SAAUjQ,CAAM,CAAEoP,CAAI,EACtC,OAAOhI,EAAqBC,CAAC,CAAC4I,OAAO,CAACjQ,EAAQoP,EAChD,EACAzZ,EAAQ8U,aAAa,CAAGA,EACxB9U,EAAQua,UAAU,CAAG,SAAUvF,CAAO,CAAEwF,CAAU,CAAEC,CAAI,EACtD,OAAOhJ,EAAqBC,CAAC,CAAC6I,UAAU,CAACvF,EAASwF,EAAYC,EAChE,EACAza,EAAQ0a,MAAM,CAAG,SAAUZ,CAAY,EACrC,OAAOrI,EAAqBC,CAAC,CAACgJ,MAAM,CAACZ,EACvC,EACA9Z,EAAQ2a,QAAQ,CAAG,SAAUrB,CAAY,EACvC,OAAO7H,EAAqBC,CAAC,CAACiJ,QAAQ,CAACrB,EACzC,EACAtZ,EAAQ4a,oBAAoB,CAAG,SAC7BC,CAAS,CACTC,CAAW,CACXC,CAAiB,EAEjB,OAAOtJ,EAAqBC,CAAC,CAACkJ,oBAAoB,CAChDC,EACAC,EACAC,EAEJ,EACA/a,EAAQgb,aAAa,CAAG,WACtB,OAAOvJ,EAAqBC,CAAC,CAACsJ,aAAa,EAC7C,EACAhb,EAAQib,OAAO,CAAG,uC,sEC/jBhBlb,CAAAA,EAAOC,OAAO,CAAG,EAAjB,6D,6CCHF,CAAC,KAAK,aAAa,IAAIsI,EAAE,CAAC,IAAIA,IAA0FA,EAAEtI,OAAO,CAA/F,SAAcsI,CAAC,EAAwB,IAAtB,IAAIC,EAAE,KAAK9C,EAAE6C,EAAEpG,MAAM,CAAOuD,GAAG8C,EAAEA,GAAAA,EAAKD,EAAE4S,UAAU,CAAC,EAAEzV,GAAG,OAAO8C,IAAI,CAAC,CAAe,CAAC,EAAMA,EAAE,CAAC,EAAE,SAASJ,EAAoB1C,CAAC,EAAE,IAAIiD,EAAEH,CAAC,CAAC9C,EAAE,CAAC,GAAGiD,KAAIS,IAAJT,EAAe,OAAOA,EAAE1I,OAAO,CAAC,IAAI4D,EAAE2E,CAAC,CAAC9C,EAAE,CAAC,CAACzF,QAAQ,CAAC,CAAC,EAAM6I,EAAE,GAAK,GAAG,CAACP,CAAC,CAAC7C,EAAE,CAAC7B,EAAEA,EAAE5D,OAAO,CAACmI,GAAqBU,EAAE,EAAK,QAAQ,CAAIA,GAAE,OAAON,CAAC,CAAC9C,EAAE,CAAC,OAAO7B,EAAE5D,OAAO,CAA6CmI,EAAoBC,EAAE,CAACC,UAAU,IAAI,IAAI5C,EAAE0C,EAAoB,IAAKpI,CAAAA,EAAOC,OAAO,CAACyF,CAAC,I,GCC3d0V,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,KAAiBnS,IAAjBmS,EACH,OAAOA,EAAatb,OAAO,CAG5B,IAAID,EAASob,CAAwB,CAACE,EAAS,CAAG,CAGjDrb,QAAS,CAAC,CACX,EAMA,OAHAub,CAAmB,CAACF,EAAS,CAACtb,EAAQA,EAAOC,OAAO,CAAEob,GAG/Crb,EAAOC,OAAO,CCpBtBob,EAAoB1V,CAAC,CAAG,IACvB,IAAI8V,EAASzb,GAAUA,EAAO0b,UAAU,CACvC,IAAO1b,EAAO,OAAU,CACxB,IAAOA,EAER,OADAqb,EAAoBM,CAAC,CAACF,EAAQ,CAAE9S,EAAG8S,CAAO,GACnCA,CACR,ECNAJ,EAAoBM,CAAC,CAAG,CAAC1b,EAAS2b,KACjC,IAAI,IAAI9Y,KAAO8Y,EACXP,EAAoB3S,CAAC,CAACkT,EAAY9Y,IAAQ,CAACuY,EAAoB3S,CAAC,CAACzI,EAAS6C,IAC5E1C,OAAOC,cAAc,CAACJ,EAAS6C,EAAK,CAAEyB,WAAY,GAAMD,IAAKsX,CAAU,CAAC9Y,EAAI,EAG/E,ECPAuY,EAAoB3S,CAAC,CAAG,CAACmT,EAAKC,IAAU1b,OAAOO,SAAS,CAACC,cAAc,CAACkE,IAAI,CAAC+W,EAAKC,GCClFT,EAAoB7S,CAAC,CAAG,IACF,aAAlB,OAAOpD,QAA0BA,OAAO2W,WAAW,EACrD3b,OAAOC,cAAc,CAACJ,EAASmF,OAAO2W,WAAW,CAAE,CAAE7Z,MAAO,QAAS,GAEtE9B,OAAOC,cAAc,CAACJ,EAAS,aAAc,CAAEiC,MAAO,EAAK,EAC5D,E,uKCYwB8Z,E,wRCYjB,OAAeC,EAqBpBlX,YAAY,CAAEmX,SAAAA,CAAQ,CAAEN,WAAAA,CAAU,CAA4B,CAAE,CAC9D,IAAI,CAACM,QAAQ,CAAGA,EAChB,IAAI,CAACN,UAAU,CAAGA,CACpB,CACF,CCtDO,IAAMO,EAAgB,cAgBhBC,EAAiB,CAjBJ,MAKmB,yBACF,uBAOJ,mBADrC,+BAWD,OCvBYC,EACX,OAAO/X,IACLF,CAAS,CACT0X,CAAqB,CACrBQ,CAAiB,CACZ,CACL,IAAMpa,EAAQqa,QAAQjY,GAAG,CAACF,EAAQ0X,EAAMQ,SACxC,YAAI,OAAOpa,EACFA,EAAMsa,IAAI,CAACpY,GAGblC,CACT,CAEA,OAAOW,IACLuB,CAAS,CACT0X,CAAqB,CACrB5Z,CAAU,CACVoa,CAAa,CACJ,CACT,OAAOC,QAAQ1Z,GAAG,CAACuB,EAAQ0X,EAAM5Z,EAAOoa,EAC1C,CAEA,OAAO1W,IAAsBxB,CAAS,CAAE0X,CAAqB,CAAW,CACtE,OAAOS,QAAQ3W,GAAG,CAACxB,EAAQ0X,EAC7B,CAEA,OAAOW,eACLrY,CAAS,CACT0X,CAAqB,CACZ,CACT,OAAOS,QAAQE,cAAc,CAACrY,EAAQ0X,EACxC,CACF,CC1BO,MAAMY,UAA6BxQ,MACxCnH,aAAc,CACZ,KAAK,CACH,qGAEJ,CAEA,OAAc4X,UAAW,CACvB,MAAM,IAAID,CACZ,CACF,CAUO,MAAME,UAAuBC,QAGlC9X,YAAYiD,CAA4B,CAAE,CAGxC,KAAK,GAEL,IAAI,CAACA,OAAO,CAAG,IAAI8U,MAAM9U,EAAS,CAChC1D,IAAIF,CAAM,CAAE0X,CAAI,CAAEQ,CAAQ,EAIxB,GAAI,iBAAOR,EACT,OAAOO,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,GAG1C,IAAMS,EAAajB,EAAKpY,WAAW,GAK7BsZ,EAAW5c,OAAO8F,IAAI,CAAC8B,GAASiV,IAAI,CACxC,GAAOvU,EAAEhF,WAAW,KAAOqZ,GAI7B,GAAI,KAAoB,IAAbC,EAGX,OAAOX,EAAe/X,GAAG,CAACF,EAAQ4Y,EAAUV,EAC9C,EACAzZ,IAAIuB,CAAM,CAAE0X,CAAI,CAAE5Z,CAAK,CAAEoa,CAAQ,EAC/B,GAAI,iBAAOR,EACT,OAAOO,EAAexZ,GAAG,CAACuB,EAAQ0X,EAAM5Z,EAAOoa,GAGjD,IAAMS,EAAajB,EAAKpY,WAAW,GAK7BsZ,EAAW5c,OAAO8F,IAAI,CAAC8B,GAASiV,IAAI,CACxC,GAAOvU,EAAEhF,WAAW,KAAOqZ,GAI7B,OAAOV,EAAexZ,GAAG,CAACuB,EAAQ4Y,GAAYlB,EAAM5Z,EAAOoa,EAC7D,EACA1W,IAAIxB,CAAM,CAAE0X,CAAI,EACd,GAAI,iBAAOA,EAAmB,OAAOO,EAAezW,GAAG,CAACxB,EAAQ0X,GAEhE,IAAMiB,EAAajB,EAAKpY,WAAW,GAK7BsZ,EAAW5c,OAAO8F,IAAI,CAAC8B,GAASiV,IAAI,CACxC,GAAOvU,EAAEhF,WAAW,KAAOqZ,UAI7B,KAAwB,IAAbC,GAGJX,EAAezW,GAAG,CAACxB,EAAQ4Y,EACpC,EACAP,eAAerY,CAAM,CAAE0X,CAAI,EACzB,GAAI,iBAAOA,EACT,OAAOO,EAAeI,cAAc,CAACrY,EAAQ0X,GAE/C,IAAMiB,EAAajB,EAAKpY,WAAW,GAK7BsZ,EAAW5c,OAAO8F,IAAI,CAAC8B,GAASiV,IAAI,CACxC,GAAOvU,EAAEhF,WAAW,KAAOqZ,UAI7B,KAAwB,IAAbC,GAGJX,EAAeI,cAAc,CAACrY,EAAQ4Y,EAC/C,CACF,EACF,CAMA,OAAcE,KAAKlV,CAAgB,CAAmB,CACpD,OAAO,IAAI8U,MAAuB9U,EAAS,CACzC1D,IAAIF,CAAM,CAAE0X,CAAI,CAAEQ,CAAQ,EACxB,OAAQR,GACN,IAAK,SACL,IAAK,SACL,IAAK,MACH,OAAOY,EAAqBC,QAAQ,SAEpC,OAAON,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,EACF,CASA,MAAcpa,CAAwB,CAAU,QAC9C,MAAU8D,OAAO,CAAC9D,GAAeA,EAAME,IAAI,CAAC,MAErCF,CACT,CAQA,OAAcyC,KAAKqD,CAAsC,CAAW,QAClE,aAAuB6U,QAAgB7U,EAEhC,IAAI4U,EAAe5U,EAC5B,CAEOE,OAAOlG,CAAY,CAAEE,CAAa,CAAQ,CAC/C,IAAMib,EAAW,IAAI,CAACnV,OAAO,CAAChG,EAAK,CACX,UAApB,OAAOmb,EACT,IAAI,CAACnV,OAAO,CAAChG,EAAK,CAAG,CAACmb,EAAUjb,EAAM,CAC7BuD,MAAMO,OAAO,CAACmX,GACvBA,EAASzV,IAAI,CAACxF,GAEd,IAAI,CAAC8F,OAAO,CAAChG,EAAK,CAAGE,CAEzB,CAEO2D,OAAO7D,CAAY,CAAQ,CAChC,OAAO,IAAI,CAACgG,OAAO,CAAChG,EAAK,CAGpBsC,IAAItC,CAAY,CAAiB,CACtC,IAAME,EAAQ,IAAI,CAAC8F,OAAO,CAAChG,EAAK,QAChC,KAAqB,IAAVE,EAA8B,IAAI,CAACkb,KAAK,CAAClb,GAE7C,IACT,CAEO0D,IAAI5D,CAAY,CAAW,CAChC,OAAO,KAA8B,IAAvB,IAAI,CAACgG,OAAO,CAAChG,EAAK,CAG3Ba,IAAIb,CAAY,CAAEE,CAAa,CAAQ,CAC5C,IAAI,CAAC8F,OAAO,CAAChG,EAAK,CAAGE,CACvB,CAEOsT,QACL6H,CAAkE,CAClEC,CAAa,CACP,CACN,IAAK,GAAM,CAACtb,EAAME,EAAM,GAAI,IAAI,CAACqb,OAAO,GACtCF,EAAWvY,IAAI,CAACwY,EAASpb,EAAOF,EAAM,IAAI,CAE9C,CAEA,CAAQub,SAA6C,CACnD,IAAK,IAAMza,KAAO1C,OAAO8F,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMhG,EAAOc,EAAIY,WAAW,GAGtBxB,EAAQ,IAAI,CAACoC,GAAG,CAACtC,EAEvB,MAAM,CAACA,EAAME,EAAM,CAEvB,CAEA,CAAQgE,MAAgC,CACtC,IAAK,IAAMpD,KAAO1C,OAAO8F,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAC3C,IAAMhG,EAAOc,EAAIY,WAAW,EAC5B,OAAM1B,CACR,CACF,CAEA,CAAQuE,QAAkC,CACxC,IAAK,IAAMzD,KAAO1C,OAAO8F,IAAI,CAAC,IAAI,CAAC8B,OAAO,EAAG,CAG3C,IAAM9F,EAAQ,IAAI,CAACoC,GAAG,CAACxB,EAEvB,OAAMZ,CACR,CACF,CAEO,CAACkD,OAAOC,QAAQ,CAAC,EAAsC,CAC5D,OAAO,IAAI,CAACkY,OAAO,EACrB,CACF,C,0DCtOA,IAAM,EAA+Brd,QAAQ,8DCAvC,EAA+BA,QAAQ,kECatC,OAAMsd,UAAoCtR,MAC/CnH,aAAc,CACZ,KAAK,CACH,mJAEJ,CAEA,OAAc4X,UAAW,CACvB,MAAM,IAAIa,CACZ,CACF,CAcO,MAAMC,EACX,OAAcP,KAAKQ,CAAuB,CAA0B,CAClE,OAAO,IAAIZ,MAAMY,EAAgB,CAC/BpZ,IAAIF,CAAM,CAAE0X,CAAI,CAAEQ,CAAQ,EACxB,OAAQR,GACN,IAAK,QACL,IAAK,SACL,IAAK,MACH,OAAO0B,EAA4Bb,QAAQ,SAE3C,OAAON,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,EACF,CACF,CAEA,IAAMqB,EAA8BvY,OAAOe,GAAG,CAAC,wBAmBxC,SAASyX,EACd5V,CAAgB,CAChB6V,CAA+B,EAE/B,IAAMC,EAAuBC,SApB7BL,CAAwB,EAExB,IAAMM,EAAyC,CAA2B,CACxEL,EACD,QACD,GAAkBlY,MAAMO,OAAO,CAACgY,IAAaA,IAAAA,EAAS7b,MAAM,CAIrD6b,EAHE,EAAE,EAc0CH,GACrD,GAAIC,IAAAA,EAAqB3b,MAAM,CAC7B,MAAO,GAMT,IAAM8b,EAAa,IAAIxZ,EAAAA,eAAeA,CAACuD,GACjCkW,EAAkBD,EAAWzY,MAAM,GAGzC,IAAK,IAAMlD,KAAUwb,EACnBG,EAAWpb,GAAG,CAACP,GAIjB,IAAK,IAAMA,KAAU4b,EACnBD,EAAWpb,GAAG,CAACP,GAGjB,MAAO,EACT,CAMO,MAAM6b,EACX,OAAcC,KACZV,CAAuB,CACvBW,CAA6C,CAC5B,CACjB,IAAMC,EAAkB,IAAI7Z,EAAAA,eAAeA,CAAC,IAAIoY,SAChD,IAAK,IAAMva,KAAUob,EAAQlY,MAAM,GACjC8Y,EAAgBzb,GAAG,CAACP,GAGtB,IAAIic,EAAmC,EAAE,CACnCC,EAAkB,IAAIC,IACtBC,EAAwB,KAE5B,IAAMC,EAAYC,EAAAA,gBAAgBA,CAACC,QAAQ,GAO3C,GANIF,GACFA,CAAAA,EAAUG,kBAAkB,CAAG,EAAG,EAIpCP,EAAiBQ,EADkBvZ,MAAM,GACb3D,MAAM,CAAC,GAAO2c,EAAgB5Y,GAAG,CAAC7E,EAAEiB,IAAI,GAChEqc,EAAiB,CACnB,IAAMW,EAA8B,EAAE,CACtC,IAAK,IAAM1c,KAAUic,EAAgB,CACnC,IAAMU,EAAc,IAAIxa,EAAAA,eAAeA,CAAC,IAAIoY,SAC5CoC,EAAYpc,GAAG,CAACP,GAChB0c,EAAkBtX,IAAI,CAACuX,EAAY3Y,QAAQ,GAC7C,CAEA+X,EAAgBW,EAClB,CACF,EAEME,EAAiB,IAAIpC,MAAMwB,EAAiB,CAChDha,IAAIF,CAAM,CAAE0X,CAAI,CAAEQ,CAAQ,EACxB,OAAQR,GAEN,KAAK6B,EACH,OAAOY,CAIT,KAAK,SACH,OAAO,SAAU,GAAGhZ,CAAiC,EACnDiZ,EAAgB3P,GAAG,CACjB,iBAAOtJ,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACvD,IAAI,EAEtD,GAAI,CAEF,OADAoC,EAAOyB,MAAM,IAAIN,GACV2Z,CACT,QAAU,CACRR,GACF,CACF,CACF,KAAK,MACH,OAAO,SAAU,GAAGnZ,CAAmB,EACrCiZ,EAAgB3P,GAAG,CACjB,iBAAOtJ,CAAI,CAAC,EAAE,CAAgBA,CAAI,CAAC,EAAE,CAAGA,CAAI,CAAC,EAAE,CAACvD,IAAI,EAEtD,GAAI,CAEF,OADAoC,EAAOvB,GAAG,IAAI0C,GACP2Z,CACT,QAAU,CACRR,GACF,CACF,CAEF,SACE,OAAOrC,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,GAEA,OAAO4C,CACT,CACF,CAwCA,SAASC,EAA6BC,CAAyB,EAE7D,GAAI,WAZGC,CAWcC,EAAAA,EAAAA,uBAAAA,EAAwBF,GAXzBG,KAAK,CAcvB,MAAM,IAAI/B,CAEd,CCnMO,IAAMgC,EAA6B,QAsEpCC,EAAuB,CAI3BC,OAAQ,SAKRC,sBAAuB,MAIvBC,oBAAqB,MAIrBC,cAAe,iBAIfC,QAAS,WAITC,QAAS,WAITC,WAAY,aAIZC,WAAY,aAIZC,UAAW,aAIXC,gBAAiB,oBAIjBC,gBAAiB,oBAIjBC,aAAc,iBAIdC,aAAc,gBAChB,EAKuB,EACrB,GAAGb,CAAoB,CACvBc,MAAO,CACLC,aAAc,CACZf,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CACnC,CACDY,WAAY,CACVhB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDU,cAAe,CAEbjB,EAAqBK,OAAO,CAC5BL,EAAqBM,OAAO,CAC7B,CACDY,WAAY,CACVlB,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACrC,CACDS,QAAS,CACPnB,EAAqBE,qBAAqB,CAC1CF,EAAqBI,aAAa,CAClCJ,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBC,MAAM,CAC3BD,EAAqBQ,UAAU,CAC/BR,EAAqBO,UAAU,CAChC,CACDa,SAAU,CAERpB,EAAqBE,qBAAqB,CAC1CF,EAAqBG,mBAAmB,CACxCH,EAAqBU,eAAe,CACpCV,EAAqBI,aAAa,CACnC,CAEL,GCvMA,IAAM,EAA+B3f,QAAQ,qCCU7C,4BAAK4gB,CAAc,E,ygBAAdA,C,EAAAA,GAAAA,CAAAA,GAeL,wBAAKC,CAAkB,E,iIAAlBA,C,EAAAA,GAAAA,CAAAA,GAKL,wBAAKC,CAAc,E,uMAAdA,C,EAAAA,GAAAA,CAAAA,GAOL,wBAAKC,CAAkB,E,y6CAAlBA,C,EAAAA,GAAkBA,CAAAA,GAmCvB,wBAAKC,CAAe,E,+CAAfA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAU,E,gOAAVA,C,EAAAA,GAAAA,CAAAA,GAQL,wBAAKC,CAAa,E,mLAAbA,C,EAAAA,GAAaA,CAAAA,GAOlB,wBAAKC,CAAU,E,4CAAVA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAQ,E,sCAARA,C,EAAAA,GAAQA,CAAAA,GAIb,wBAAKC,CAAyB,E,uDAAzBA,C,EAAAA,GAAAA,CAAAA,GAIL,wBAAKC,CAAmB,E,mHAAnBA,C,EAAAA,GAAAA,CAAAA,GAKL,wBAAKC,CAAc,E,sCAAdA,C,EAAAA,GAAAA,CAAAA,GCXE,IAAMC,EAA+B,qBAKTtc,OAJO,uBAKJA,OAAOsc,EC3FtC,OAAMC,EAaX5c,YACE6c,CAA2C,CAC3CC,CAA6D,CAC7DnE,CAA+B,CAC/BG,CAA+B,CAC/B,C,IAOoBH,EAJpB,IAAMoE,EACJF,GACAG,SD4CJF,CAAgD,CAChDD,CAA+B,EAK/B,IAAM5Z,EAAU4U,EAAejY,IAAI,CAACkd,EAAI7Z,OAAO,EAS/C,MAAO,CAAE8Z,qBANoBE,EADC1d,GAAG,CHjFQ,4BGkFMsd,EAAaI,aAAa,CAM1CC,wBAJCja,EAAQpC,GAAG,CHlF3C,sCGsFuD,CACzD,EC5DgCic,EAAKD,GAAcE,oBAAoB,CAE7DI,EAAc,MAAAxE,CAAAA,EAAAA,EAAQpZ,GAAG,CAACod,EAA4BA,EAAAA,KAAAA,EAAxChE,EAA2Cxb,KAAK,CAEpE,IAAI,CAACigB,SAAS,CAAGrgB,CAAAA,CACf,EAACggB,GACCI,GACAN,GACCM,IAAgBN,EAAaI,aAAa,EAM/C,IAAI,CAACI,cAAc,CAAGR,MAAAA,EAAAA,KAAAA,EAAAA,EAAcI,aAAa,CACjD,IAAI,CAACK,eAAe,CAAGxE,CACzB,CAEAyE,QAAS,CACP,GAAI,CAAC,IAAI,CAACF,cAAc,CACtB,MAAM,qBAEL,CAFK,MACJ,0EADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,GAGF,IAAI,CAACC,eAAe,CAACxf,GAAG,CAAC,CACvBb,KAAM0f,EACNxf,MAAO,IAAI,CAACkgB,cAAc,CAC1B3gB,SAAU,GACVC,SAAmD,OACnDF,OAAQ4T,CAAAA,EACRlU,KAAM,GACR,EACF,CAEAqhB,SAAU,CAIR,IAAI,CAACF,eAAe,CAACxf,GAAG,CAAC,CACvBb,KAAM0f,EACNxf,MAAO,GACPT,SAAU,GACVC,SAAmD,OACnDF,OAAQ4T,CAAAA,EACRlU,KAAM,IACNC,QAAS,IAAIC,KAAK,EACpB,EACF,CACF,CCHA,SAASohB,EACPX,CAA0B,CAC1BY,CAAiD,EAEjD,GACE,4BAA6BZ,EAAI7Z,OAAO,EACxC,iBAAO6Z,EAAI7Z,OAAO,CAAC,0BAA0B,CAC7C,CACA,IAAM0a,EAAiBb,EAAI7Z,OAAO,CAAC,0BAA0B,CACvDvB,EAAkB,IAAIoW,QAE5B,IAAK,IAAMva,KAAUwE,SCjDUC,CAAqB,EACtD,IAEIC,EACAC,EACAC,EACAC,EACAC,EANAC,EAAiB,EAAE,CACnBC,EAAM,EAOV,SAASC,IACP,KAAOD,EAAMP,EAAc5E,MAAM,EAAI,KAAKqF,IAAI,CAACT,EAAcU,MAAM,CAACH,KAClEA,GAAO,EAET,OAAOA,EAAMP,EAAc5E,MAAM,CASnC,KAAOmF,EAAMP,EAAc5E,MAAM,EAAE,CAIjC,IAHA6E,EAAQM,EACRF,EAAwB,GAEjBG,KAEL,GAAIN,MADJA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,EACb,CAQd,IANAJ,EAAYI,EACZA,GAAO,EAEPC,IACAJ,EAAYG,EAELA,EAAMP,EAAc5E,MAAM,EAjB9B8E,MAFPA,CAAAA,EAAKF,EAAcU,MAAM,CAACH,EAAG,GAERL,MAAAA,GAAcA,MAAAA,GAkB7BK,GAAO,CAILA,CAAAA,EAAMP,EAAc5E,MAAM,EAAI4E,MAAAA,EAAcU,MAAM,CAACH,IAErDF,EAAwB,GAExBE,EAAMH,EACNE,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOE,IACnDF,EAAQM,GAIRA,EAAMJ,EAAY,CAEtB,MACEI,GAAO,EAIP,EAACF,GAAyBE,GAAOP,EAAc5E,MAAM,GACvDkF,EAAeK,IAAI,CAACX,EAAcY,SAAS,CAACX,EAAOD,EAAc5E,MAAM,EAE3E,CAEA,OAAOkF,CACT,EDf4Cqb,GACtCjc,EAAgByB,MAAM,CAAC,aAAc5F,GAMvC,IAAK,IAAMA,KAAUgc,IAHO7Z,EAAAA,eAAeA,CAACgC,GAGPjB,MAAM,GACzCid,EAAgB5f,GAAG,CAACP,EAExB,CACF,C,iEEvGO,OAAMqgB,UAAuBzW,MAClCnH,YAAY6d,CAAe,CAAEza,CAAsB,CAAE,CACnD,KAAK,CACH,cAAcya,CAAAA,EAAQC,QAAQ,CAAC,KAAOD,EAAUA,EAAU,GAAE,EAAE,6BAC9Dza,GAEF,IAAI,CAACnG,IAAI,CAAG,gBACd,CACF,CCLO,eAAe8gB,EACpBC,CAA4B,CAC5B1R,CAA0B,EAE1B,GAAI,CAAC0R,EACH,OAAO1R,IAIT,IAAM2R,EAAyBC,EAAuBF,GACtD,GAAI,CACF,OAAO,MAAM1R,GACf,QAAU,CAER,IAAM6R,EAAiBC,SA0BzBC,CAAuB,CACvBC,CAAuB,EAEvB,IAAMC,EAAW,IAAI7E,IAAI2E,EAAKG,eAAe,EACvCC,EAAuB,IAAI/E,IAAI2E,EAAKK,uBAAuB,EACjE,MAAO,CACLF,gBAAiBF,EAAKE,eAAe,CAAC1hB,MAAM,CAAC,GAAS,CAACyhB,EAAS1d,GAAG,CAAC8d,IACpEC,mBAAoBvjB,OAAOoD,WAAW,CACpCpD,OAAOmd,OAAO,CAAC8F,EAAKM,kBAAkB,EAAE9hB,MAAM,CAC5C,CAAC,CAACiB,EAAI,GAAK,CAAEA,CAAAA,KAAOsgB,EAAKO,kBAAkB,IAG/CF,wBAAyBJ,EAAKI,uBAAuB,CAAC5hB,MAAM,CAC1D,GAAa,CAAC2hB,EAAqB5d,GAAG,CAACge,GAE3C,CACF,EAzCMZ,EACAC,EAAuBF,GAEzB,OAAMc,EAAmBd,EAAOG,EAClC,CACF,CASA,SAASD,EAAuBF,CAAgB,EAC9C,MAAO,CACLQ,gBAAiBR,EAAMQ,eAAe,CAAG,IAAIR,EAAMQ,eAAe,CAAC,CAAG,EAAE,CACxEI,mBAAoB,CAAE,GAAGZ,EAAMY,kBAAkB,EACjDF,wBAAyBV,EAAMU,uBAAuB,CAClD,IAAIV,EAAMU,uBAAuB,CAAC,CAClC,EAAE,CAEV,CAqBA,eAAeI,EACblF,CAAoB,CACpB,CACE4E,gBAAAA,CAAe,CACfI,mBAAAA,CAAkB,CAClBF,wBAAAA,CAAuB,CACL,E,IAGlB9E,EADF,OAAOnT,QAAQnH,GAAG,CAAC,C,MACjBsa,CAAAA,EAAAA,EAAUmF,gBAAgB,SAA1BnF,EAA4BoF,aAAa,CAACR,MACvCnjB,OAAOmG,MAAM,CAACod,MACdF,EACJ,CACH,CCxEA,IAAMO,EAA2C,qBAEhD,CAFgD,MAC/C,8EAD+C,qB,MAAA,O,WAAA,G,aAAA,EAEjD,EAEA,OAAMC,EAGJ1B,SAAgB,CACd,MAAMyB,CACR,CAEAnF,UAA8B,CAG9B,CAEAjT,KAAY,CACV,MAAMoY,CACR,CAEAE,MAAa,CACX,MAAMF,CACR,CAEAG,WAAkB,CAChB,MAAMH,CACR,CAEA,OAAOxH,KAAQ1S,CAAK,CAAK,CACvB,OAAOA,CACT,CACF,CAEA,IAAMsa,EACJ,oBAAOpI,YAA8B,WAAoBqI,iBAAiB,CCpCtE,EAA+BnkB,QAAQ,mECoBtC,OAAMokB,EASXvf,YAAY,CAAEwf,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEC,YAAAA,CAAW,CAAoB,CAAE,C,KAF3DC,cAAc,CAAG,IAAIjG,IAG3B,IAAI,CAAC8F,SAAS,CAAGA,EACjB,IAAI,CAACC,OAAO,CAAGA,EACf,IAAI,CAACC,WAAW,CAAGA,EAEnB,IAAI,CAACE,aAAa,CAAG,GAAIC,CAAAA,GAAAA,EACzB,IAAI,CAACD,aAAa,CAAC3V,KAAK,EAC1B,CAEO6V,MAAMC,CAAe,CAAQ,CAClC,GC7BAlB,OD6BekB,GC5Bf,iBD4BeA,GC3Bf,SD2BeA,GC1Bf,mBAAOlB,EAAQrY,IAAI,CD2BZ,IAAI,CAACgZ,SAAS,EACjBQ,IAEF,IAAI,CAACR,SAAS,CACZO,EAAKE,KAAK,CAAC,GAAW,IAAI,CAACC,eAAe,CAAC,UAAW3Q,UAEnD,GAAI,mBAAOwQ,EAEhB,IAAI,CAACI,WAAW,CAACJ,QAEjB,MAAM,qBAAgE,CAAhE,MAAU,uDAAV,qB,MAAA,M,WAAA,G,aAAA,EAA+D,EAEzE,CAEQI,YAAY7T,CAAuB,CAAE,KFPfvH,CESvB,KAAI,CAACya,SAAS,EACjBQ,IAGF,IAAMI,EAAgBC,EAAAA,oBAAoBA,CAACvG,QAAQ,GAC/CsG,GACF,IAAI,CAACT,cAAc,CAAC7V,GAAG,CAACsW,GAG1B,IAAME,EAAiBC,EAAAA,qBAAqBA,CAACzG,QAAQ,GAM/C0G,EAAqBF,EACvBA,EAAeE,kBAAkB,CACjCJ,MAAAA,EAAAA,KAAAA,EAAAA,EAAe5F,KAAK,CAGnB,IAAI,CAACiG,0BAA0B,GAClC,IAAI,CAACA,0BAA0B,CAAG,IAAI,CAACC,mBAAmB,GAC1D,IAAI,CAAClB,SAAS,CAAC,IAAI,CAACiB,0BAA0B,GAQhD,IAAME,GFvCsB5b,EEuCS,UACnC,GAAI,CACF,MAAMwb,EAAAA,qBAAqBA,CAAC1Z,GAAG,CAAC,CAAE2Z,mBAAAA,CAAmB,EAAG,IACtDlU,IAEJ,CAAE,MAAOiD,EAAO,CACd,IAAI,CAAC2Q,eAAe,CAAC,WAAY3Q,EACnC,CACF,EF9CF,EACS8P,EAA6B5H,IAAI,CAAC1S,GAEpCma,EAAsBzH,IAAI,CAAC1S,IE6ChC,IAAI,CAAC6a,aAAa,CAAC9V,GAAG,CAAC6W,EACzB,CAEA,MAAcD,qBAAsB,CAElC,OADA,MAAM,IAAIja,QAAc,GAAa,IAAI,CAACgZ,OAAO,CAAE1V,IAC5C,IAAI,CAAC6W,YAAY,EAC1B,CAEA,MAAcA,cAA8B,CAC1C,GAAI,QAAI,CAAChB,aAAa,CAACrf,IAAI,CAAQ,OAEnC,IAAK,IAAM6f,KAAiB,IAAI,CAACT,cAAc,CAC7CS,EAAc5F,KAAK,CAAG,QAGxB,IAAMZ,EAAYC,EAAAA,gBAAgBA,CAACC,QAAQ,GAC3C,GAAI,CAACF,EACH,MAAM,qBAAoE,CAApE,IAAIgE,EAAe,kDAAnB,qB,MAAA,O,WAAA,G,aAAA,EAAmE,GAG3E,OAAOG,EAAuBnE,EAAW,KACvC,IAAI,CAACgG,aAAa,CAAC3d,KAAK,GACjB,IAAI,CAAC2d,aAAa,CAACzV,MAAM,IAEpC,CAEQ+V,gBAAgBW,CAAgC,CAAEtR,CAAc,CAAE,CASxE,GANAe,QAAQf,KAAK,CACXsR,YAAAA,EACI,0CACA,uDACJtR,GAEE,IAAI,CAACmQ,WAAW,CAElB,GAAI,CACF,UAAI,CAACA,WAAW,EAAhB,IAAI,CAACA,WAAW,MAAhB,IAAI,CAAenQ,EACrB,CAAE,MAAOuR,EAAc,CACrBxQ,QAAQf,KAAK,CACX,qBAKC,CALD,IAAIqO,EACF,0EACA,CACEmD,MAAOD,CACT,GAJF,qB,MAAA,O,WAAA,G,aAAA,EAKA,GAEJ,CAEJ,CACF,CAEA,SAASd,IACP,MAAM,qBAEL,CAFK,MACJ,uGADI,qB,MAAA,M,WAAA,G,aAAA,EAEN,EACF,CE9HO,ICvBMgB,EAAe,CAC1B,MACA,OACA,UACA,OACA,MACA,SACA,QACD,CCTKC,EAAiB,IACrB,IAAMC,EAAwB,CAAC,UAAU,CAIzC,GAAIC,EAASC,UAAU,CAAC,KAAM,CAC5B,IAAMC,EAAgBF,EAASxjB,KAAK,CAAC,KAErC,IAAK,IAAIoG,EAAI,EAAGA,EAAIsd,EAAcjkB,MAAM,CAAG,EAAG2G,IAAK,CACjD,IAAIud,EAAcD,EAAcrjB,KAAK,CAAC,EAAG+F,GAAG1G,IAAI,CAAC,KAE7CikB,IAEGA,EAAYxD,QAAQ,CAAC,UAAawD,EAAYxD,QAAQ,CAAC,WAC1DwD,CAAAA,EAAc,CAAC,EAAEA,EAAY,EAC3B,EAAaxD,QAAQ,CAAC,KAAa,GAAN,IAC9B,MAAM,CAAC,EAEVoD,EAAYve,IAAI,CAAC2e,GAErB,CACF,CACA,OAAOJ,CACT,E,uDC1BA,IAAMK,GAAqB,sBAEpB,OAAMC,WAA2Bra,MAGtCnH,YAAY,CAAmC,CAAE,CAC/C,KAAK,CAAC,yBAAyByhB,GAAAA,IAAAA,CADLA,WAAW,CAAXA,EAAAA,IAAAA,CAF5BC,MAAM,CAA8BH,EAIpC,CACF,CAEO,SAASI,GAAqBC,CAAY,QAC/C,UACE,OAAOA,GACPA,OAAAA,GACE,WAAYA,GACd,iBAAOA,EAAIF,MAAM,EAKZE,EAAIF,MAAM,GAAKH,EACxB,CCnBO,MAAMM,WAA8B1a,M,kBAApC,iBACW2a,IAAI,CAHU,yB,CAIhC,CCQA,MAAMC,WAAqC5a,MAGzCnH,YAAY,CAAkC,CAAE,CAC9C,KAAK,CACH,CAAC,qBAAqB,EAAEgiB,EAAW,qGAAqG,EAAEA,EAAW,qJAAqJ,CAAC,OAFnRA,UAAU,CAAVA,EAAAA,IAAAA,CAFZN,MAAM,CAHU,2BAShC,CACF,CASO,SAASO,GACdC,CAAmB,CACnBF,CAAkB,EAElB,IAAMG,EAAiB,IAAI1b,QAAW,CAAC9F,EAAGyhB,KACxCF,EAAOG,gBAAgB,CACrB,QACA,KACED,EAAO,IAAIL,GAA6BC,GAC1C,EACA,CAAE/c,KAAM,EAAK,EAEjB,GAKA,OADAkd,EAAelC,KAAK,CAACqC,IACdH,CACT,CAEA,SAASG,KAAgB,CCJzB,IAAMC,GAAc,mBAAOC,EAAAA,iBAAuB,CA2C3C,SAASC,GACdC,CAA2C,EAE3C,MAAO,CACLA,uBAAAA,EACAC,gBAAiB,EAAE,CACnBC,sBAAuBve,KAAAA,EACvBwe,0BAA2B,IAC7B,CACF,CAyBO,SAASC,GACd9E,CAAgB,CAChBoC,CAAuE,CACvE4B,CAAkB,EAElB,GAAI5B,CAAAA,CAAAA,GAEAA,UAAAA,EAAclT,IAAI,EAClBkT,mBAAAA,EAAclT,IAAI,IAYlB8Q,EAAM+E,YAAY,GAAI/E,EAAMgF,WAAW,EAE3C,GAAIhF,EAAMiF,kBAAkB,CAC1B,MAAM,qBAEL,CAFK,IAAIpB,GACR,CAAC,MAAM,EAAE7D,EAAMkF,KAAK,CAAC,8EAA8E,EAAElB,EAAW,4HAA4H,CAAC,EADzO,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAI5B,GACF,GAAIA,kBAAAA,EAAclT,IAAI,CACpBiW,GACEnF,EAAMkF,KAAK,CACXlB,EACA5B,EAAcgD,eAAe,OAE1B,GAAIhD,qBAAAA,EAAclT,IAAI,CAAyB,CACpDkT,EAAciD,UAAU,CAAG,EAG3B,IAAMzB,EAAM,qBAEX,CAFW,IAAIJ,GACd,CAAC,MAAM,EAAExD,EAAMkF,KAAK,CAAC,iDAAiD,EAAElB,EAAW,2EAA2E,CAAC,EADrJ,qB,MAAA,O,WAAA,G,aAAA,EAEZ,EAIA,OAHAhE,EAAMsF,uBAAuB,CAAGtB,EAChChE,EAAMuF,iBAAiB,CAAG3B,EAAI4B,KAAK,CAE7B5B,CACR,GAQJ,CA0BO,SAAS6B,GACdzB,CAAkB,CAClBhE,CAAgB,CAChB0F,CAAoC,EAGpC,IAAM9B,EAAM,qBAEX,CAFW,IAAIJ,GACd,CAAC,MAAM,EAAExD,EAAMkF,KAAK,CAAC,mDAAmD,EAAElB,EAAW,6EAA6E,CAAC,EADzJ,qB,MAAA,O,WAAA,G,aAAA,EAEZ,EAOA,OALA0B,EAAeL,UAAU,CAAG,EAE5BrF,EAAMsF,uBAAuB,CAAGtB,EAChChE,EAAMuF,iBAAiB,CAAG3B,EAAI4B,KAAK,CAE7B5B,CACR,CAmGO,SAAS+B,GACdT,CAAa,CACblB,CAAkB,CAClB4B,CAAqB,CACrBF,CAAoC,EAEpC,IAAMN,EAAkBM,EAAeN,eAAe,OAClDA,GACEA,OAAAA,EAAgBP,yBAAyB,GAC3CO,EAAgBR,qBAAqB,CAAGZ,EACxCoB,EAAgBP,yBAAyB,CAAGe,EACV,KAA9BF,EAAeG,UAAU,EAG3BT,CAAAA,EAAgBU,iBAAiB,CAAG,EAAG,GAI7CC,SA3EAb,CAAa,CACblB,CAAkB,CAClB0B,CAAoC,EAIpC,IAAMnU,EAAQyU,GAFC,CAAC,MAAM,EAAEd,EAAM,iEAAiE,EAAElB,EAAW,CAAC,CAAC,EAI9G0B,EAAeO,UAAU,CAACC,KAAK,CAAC3U,GAEhC,IAAM6T,EAAkBM,EAAeN,eAAe,CAClDA,GACFA,EAAgBT,eAAe,CAAChgB,IAAI,CAAC,CAGnC6gB,MAAOJ,EAAgBV,sBAAsB,CACzC,QAAYc,KAAK,CACjBnf,KAAAA,EACJ2d,WAAAA,CACF,EAEJ,EAsDsCkB,EAAOlB,EAAY0B,GACjDM,GACJ,CAAC,MAAM,EAAEd,EAAM,iEAAiE,EAAElB,EAAW,CAAC,CAAC,CAEnG,CAsBO,SAASmB,GACdD,CAAa,CACblB,CAAkB,CAClBoB,CAA4C,EAE5Ce,CAmIF,WACE,GAAI,CAAC5B,GACH,MAAM,qBAEL,CAFK,MACJ,oIADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAEJ,KAxIMa,GACFA,EAAgBT,eAAe,CAAChgB,IAAI,CAAC,CAGnC6gB,MAAOJ,EAAgBV,sBAAsB,CACzC,QAAYc,KAAK,CACjBnf,KAAAA,EACJ2d,WAAAA,CACF,GAGFQ,EAAAA,iBAAuB,CAAC4B,GAAqBlB,EAAOlB,GACtD,CAEA,SAASoC,GAAqBlB,CAAa,CAAElB,CAAkB,EAC7D,MACE,CAAC,MAAM,EAAEkB,EAAM,iEAAiE,EAAElB,EAAW,kKAAE,CAAC,CA4BpG,GAAIqC,CAAgE,IAAhEA,SAX6BhV,CAAc,EAC7C,OACEA,EAAOnQ,QAAQ,CACb,oEAEFmQ,EAAOnQ,QAAQ,CACb,gEAGN,EAE4BklB,GAAqB,MAAO,QACtD,MAAM,qBAEL,CAFK,MACJ,0FADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAKF,SAASJ,GAAgCnG,CAAe,EACtD,IAAMtO,EAAQ,qBAAkB,CAAlB,MAAUsO,GAAV,qB,MAAA,O,WAAA,G,aAAA,EAAiB,GAE/B,OADEtO,EAAcmS,MAAM,CAJW,6BAK1BnS,CACT,CC3ZO,SAAS+U,GAAcrM,CAAkB,EAG9C,GAAI,CAACA,EAASsM,IAAI,CAChB,MAAO,CAACtM,EAAUA,EAAS,CAG7B,GAAM,CAACuM,EAAOC,EAAM,CAAGxM,EAASsM,IAAI,CAACG,GAAG,GAElCC,EAAU,IAAIC,SAASJ,EAAO,CAClCpV,OAAQ6I,EAAS7I,MAAM,CACvByV,WAAY5M,EAAS4M,UAAU,CAC/B5hB,QAASgV,EAAShV,OAAO,GAG3B5H,OAAOC,cAAc,CAACqpB,EAAS,MAAO,CACpCxnB,MAAO8a,EAAS6M,GAAG,GAGrB,IAAMC,EAAU,IAAIH,SAASH,EAAO,CAClCrV,OAAQ6I,EAAS7I,MAAM,CACvByV,WAAY5M,EAAS4M,UAAU,CAC/B5hB,QAASgV,EAAShV,OAAO,GAO3B,OAJA5H,OAAOC,cAAc,CAACypB,EAAS,MAAO,CACpC5nB,MAAO8a,EAAS6M,GAAG,GAGd,CAACH,EAASI,EAAQ,CD8iBF,OACvB,8CAA6C,EAEtB,OACvB,8CAA6C,EAExB,OAAW,4CAA2C,EE9iBtE,6BAAWC,CAAe,E,2HAAfA,C,MAkJX,yBAAWC,CAAoB,E,qGAApBA,C,MC1LX,SAASC,GAAoBhC,CAAa,EAC/C,OAAOA,EAAMtkB,OAAO,CAAC,MAAO,KAAO,GACrC,CCJO,SAASumB,GAAUhpB,CAAY,EACpC,IAAMipB,EAAYjpB,EAAK0B,OAAO,CAAC,KACzBwnB,EAAalpB,EAAK0B,OAAO,CAAC,KAC1BynB,EAAWD,EAAa,IAAOD,CAAAA,EAAY,GAAKC,EAAaD,CAAQ,SAE3E,GAAgBA,EAAY,GACnB,CACLjE,SAAUhlB,EAAKyG,SAAS,CAAC,EAAG0iB,EAAWD,EAAaD,GACpDG,MAAOD,EACHnpB,EAAKyG,SAAS,CAACyiB,EAAYD,EAAY,GAAKA,EAAY/gB,KAAAA,GACxD,GACJmhB,KAAMJ,EAAY,GAAKjpB,EAAK6B,KAAK,CAAConB,GAAa,EACjD,EAGK,CAAEjE,SAAUhlB,EAAMopB,MAAO,GAAIC,KAAM,EAAG,CAC/C,CCfO,SAASC,GAActpB,CAAY,CAAEupB,CAAe,EACzD,GAAI,CAACvpB,EAAKilB,UAAU,CAAC,MAAQ,CAACsE,EAC5B,OAAOvpB,EAGT,GAAM,CAAEglB,SAAAA,CAAQ,CAAEoE,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAGL,GAAUhpB,GAC5C,MAAO,GAAGupB,EAASvE,EAAWoE,EAAQC,CACxC,CCNO,SAASG,GAAcxpB,CAAY,CAAEypB,CAAe,EACzD,GAAI,CAACzpB,EAAKilB,UAAU,CAAC,MAAQ,CAACwE,EAC5B,OAAOzpB,EAGT,GAAM,CAAEglB,SAAAA,CAAQ,CAAEoE,MAAAA,CAAK,CAAEC,KAAAA,CAAI,CAAE,CAAGL,GAAUhpB,GAC5C,MAAO,GAAGglB,EAAWyE,EAASL,EAAQC,CACxC,CCLO,SAASK,GAAc1pB,CAAY,CAAEupB,CAAc,EACxD,GAAI,iBAAOvpB,EACT,MAAO,GAGT,GAAM,CAAEglB,SAAAA,CAAQ,CAAE,CAAGgE,GAAUhpB,GAC/B,OAAOglB,IAAauE,GAAUvE,EAASC,UAAU,CAACsE,EAAS,IAC7D,CCZU,IAAII,WAAW,CAAC,GAAI,IAAK,IAAK,IAAK,IAAI,EAEvC,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAI,EAItC,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,GAAI,IAAK,GAAG,EAE9C,IAAIA,WAAW,CAAC,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAG,EAE9C,IAAIA,WAAW,CAAC,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAAG,EAEtC,IAAIA,WAAW,CAC5B,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,GAAI,GAAI,GAAI,IAAK,IAAK,IAAK,IAAK,GAC5D,ECMW,IAAIC,YCba1lB,OAAOe,GAAG,CAAC,2BCD5C,IAAMiQ,GAAQ,IAAI2U,QAWX,SAASC,GACd9E,CAAgB,CAChB+E,CAA2B,MAYvBC,EATJ,GAAI,CAACD,EAAS,MAAO,CAAE/E,SAAAA,CAAS,EAGhC,IAAIiF,EAAoB/U,GAAM9R,GAAG,CAAC2mB,GAC7BE,IACHA,EAAoBF,EAAQ1oB,GAAG,CAAC,GAAY6oB,EAAO1nB,WAAW,IAC9D0S,GAAMvT,GAAG,CAACooB,EAASE,IAOrB,IAAME,EAAWnF,EAASxjB,KAAK,CAAC,IAAK,GAIrC,GAAI,CAAC2oB,CAAQ,CAAC,EAAE,CAAE,MAAO,CAAEnF,SAAAA,CAAS,EAGpC,IAAMoF,EAAUD,CAAQ,CAAC,EAAE,CAAC3nB,WAAW,GAIjCkP,EAAQuY,EAAkBvoB,OAAO,CAAC0oB,UACxC,EAAY,EAAU,CAAEpF,SAAAA,CAAS,GAGjCgF,EAAiBD,CAAO,CAACrY,EAAM,CAKxB,CAAEsT,SAFTA,EAAWA,EAASnjB,KAAK,CAACmoB,EAAe/oB,MAAM,CAAG,IAAM,IAErC+oB,eAAAA,CAAe,EACpC,CCvCA,IAAMK,GACJ,2FAEF,SAASC,GAAS3B,CAAiB,CAAE4B,CAAmB,EACtD,OAAO,IAAIC,IACTnX,OAAOsV,GAAKlmB,OAAO,CAAC4nB,GAA0B,aAC9CE,GAAQlX,OAAOkX,GAAM9nB,OAAO,CAAC4nB,GAA0B,aAE3D,CAEA,IAAMI,GAAWvmB,OAAO,kBAEjB,OAAMwmB,GAeX7mB,YACE8mB,CAAmB,CACnBC,CAAmC,CACnCC,CAAc,CACd,CACA,IAAIN,EACAtjB,CAGF,CAAuB,UAAvB,OAAQ2jB,GAA2B,aAAcA,GACjD,iBAAOA,GAEPL,EAAOK,EACP3jB,EAAU4jB,GAAQ,CAAC,GAEnB5jB,EAAU4jB,GAAQD,GAAc,CAAC,EAGnC,IAAI,CAACH,GAAS,CAAG,CACf9B,IAAK2B,GAASK,EAAOJ,GAAQtjB,EAAQsjB,IAAI,EACzCtjB,QAASA,EACT6jB,SAAU,EACZ,EAEA,IAAI,CAACC,OAAO,EACd,CAEQA,SAAU,C,IAcV,IAKJ,EACA,IAnBF,IAAMC,EAAOC,SCvBfjG,CAAgB,CAChB/d,CAAgB,MAE0BA,EAyCxBpC,EAzClB,GAAM,CAAEimB,SAAAA,CAAQ,CAAEI,KAAAA,CAAI,CAAEC,cAAAA,CAAa,CAAE,CAAGlkB,MAAAA,CAAAA,EAAAA,EAAQmkB,UAAU,EAAlBnkB,EAAsB,CAAC,EAC3D+jB,EAAyB,CAC7BhG,SAAAA,EACAmG,cAAenG,MAAAA,EAAmBA,EAASrD,QAAQ,CAAC,KAAOwJ,CAC7D,EAEIL,GAAYpB,GAAcsB,EAAKhG,QAAQ,CAAE8F,KAC3CE,EAAKhG,QAAQ,CAAGqG,SCrDarrB,CAAY,CAAEupB,CAAc,EAa3D,GAAI,CAACG,GAAc1pB,EAAMupB,GACvB,OAAOvpB,EAIT,IAAMsrB,EAAgBtrB,EAAK6B,KAAK,CAAC0nB,EAAOtoB,MAAM,SAG9C,EAAkBgkB,UAAU,CAAC,KACpBqG,EAKF,IAAIA,CACb,EDyBqCN,EAAKhG,QAAQ,CAAE8F,GAChDE,EAAKF,QAAQ,CAAGA,GAElB,IAAIS,EAAuBP,EAAKhG,QAAQ,CAExC,GACEgG,EAAKhG,QAAQ,CAACC,UAAU,CAAC,iBACzB+F,EAAKhG,QAAQ,CAACrD,QAAQ,CAAC,SACvB,CACA,IAAM6J,EAAQR,EAAKhG,QAAQ,CACxBviB,OAAO,CAAC,mBAAoB,IAC5BA,OAAO,CAAC,UAAW,IACnBjB,KAAK,CAAC,KAEHiqB,EAAUD,CAAK,CAAC,EAAE,CACxBR,EAAKS,OAAO,CAAGA,EACfF,EACEC,UAAAA,CAAK,CAAC,EAAE,CAAe,IAAIA,EAAM3pB,KAAK,CAAC,GAAGX,IAAI,CAAC,KAAS,IAIhC,KAAtB+F,EAAQykB,SAAS,EACnBV,CAAAA,EAAKhG,QAAQ,CAAGuG,CAAmB,CAEvC,CAIA,GAAIL,EAAM,CACR,IAAIrmB,EAASoC,EAAQ0kB,YAAY,CAC7B1kB,EAAQ0kB,YAAY,CAACZ,OAAO,CAACC,EAAKhG,QAAQ,EAC1C8E,GAAoBkB,EAAKhG,QAAQ,CAAEkG,EAAKnB,OAAO,CAEnDiB,CAAAA,EAAKd,MAAM,CAAGrlB,EAAOmlB,cAAc,CACnCgB,EAAKhG,QAAQ,CAAGngB,MAAAA,CAAAA,EAAAA,EAAOmgB,QAAQ,EAAfngB,EAAmBmmB,EAAKhG,QAAQ,CAE5C,CAACngB,EAAOmlB,cAAc,EAAIgB,EAAKS,OAAO,EAKpC5mB,CAJJA,EAASoC,EAAQ0kB,YAAY,CACzB1kB,EAAQ0kB,YAAY,CAACZ,OAAO,CAACQ,GAC7BzB,GAAoByB,EAAsBL,EAAKnB,OAAO,GAE/CC,cAAc,EACvBgB,CAAAA,EAAKd,MAAM,CAAGrlB,EAAOmlB,cAAc,CAGzC,CACA,OAAOgB,CACT,EDlCqC,IAAI,CAACP,GAAS,CAAC9B,GAAG,CAAC3D,QAAQ,CAAE,CAC5DoG,WAAY,IAAI,CAACX,GAAS,CAACxjB,OAAO,CAACmkB,UAAU,CAC7CM,UAAW,CAACxX,QAAQ0X,GAAG,CAACC,kCAAkC,CAC1DF,aAAc,IAAI,CAAClB,GAAS,CAACxjB,OAAO,CAAC0kB,YAAY,GAG7CG,EAAWC,SGzEnBrlB,CAAoC,CACpCI,CAA6B,EAI7B,IAAIglB,EACJ,GAAIhlB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAASklB,IAAI,GAAI,CAACznB,MAAMO,OAAO,CAACgC,EAAQklB,IAAI,EAC9CF,EAAWhlB,EAAQklB,IAAI,CAAC5mB,QAAQ,GAAG5D,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,MAC9C,IAAIkF,EAAOolB,QAAQ,CAEnB,OADLA,EAAWplB,EAAOolB,QAAQ,CAG5B,OAAOA,EAAStpB,WAAW,EAC7B,EH6DM,IAAI,CAACioB,GAAS,CAAC9B,GAAG,CAClB,IAAI,CAAC8B,GAAS,CAACxjB,OAAO,CAACH,OAAO,CAEhC,KAAI,CAAC2jB,GAAS,CAACwB,YAAY,CAAG,IAAI,CAACxB,GAAS,CAACxjB,OAAO,CAAC0kB,YAAY,CAC7D,IAAI,CAAClB,GAAS,CAACxjB,OAAO,CAAC0kB,YAAY,CAACO,kBAAkB,CAACJ,GACvDI,SIrFNC,CAAqC,CACrCL,CAAiB,CACjB9B,CAAuB,EAEvB,GAAKmC,EAML,IAAK,IAAMC,KAJPpC,GACFA,CAAAA,EAAiBA,EAAexnB,WAAW,EAAC,EAG3B2pB,GAAa,C,IAEPC,EAIrBA,EAHF,GACEN,IAFqB,OAAAM,CAAAA,EAAAA,EAAK/rB,MAAM,SAAX+rB,EAAa5qB,KAAK,CAAC,IAAK,EAAE,CAAC,EAAE,CAACgB,WAAW,EAAC,GAG/DwnB,IAAmBoC,EAAKC,aAAa,CAAC7pB,WAAW,WACjD4pB,CAAAA,EAAAA,EAAKrC,OAAO,SAAZqC,EAAcE,IAAI,CAAC,GAAYpC,EAAO1nB,WAAW,KAAOwnB,EAAc,EAEtE,OAAOoC,CAEX,CACF,EJgE2B,MACjB,OAAI,CAAC3B,GAAS,CAACxjB,OAAO,CAACmkB,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCqB,OAAO,CAChDT,GAGN,IAAMO,EACJ,cAAI,CAAC5B,GAAS,CAACwB,YAAY,SAA3B,EAA6BI,aAAa,UAC1C,OAAI,CAAC5B,GAAS,CAACxjB,OAAO,CAACmkB,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCmB,aAAa,CAExD,KAAI,CAAC5B,GAAS,CAAC9B,GAAG,CAAC3D,QAAQ,CAAGgG,EAAKhG,QAAQ,CAC3C,IAAI,CAACyF,GAAS,CAAC4B,aAAa,CAAGA,EAC/B,IAAI,CAAC5B,GAAS,CAACK,QAAQ,CAAGE,EAAKF,QAAQ,EAAI,GAC3C,IAAI,CAACL,GAAS,CAACgB,OAAO,CAAGT,EAAKS,OAAO,CACrC,IAAI,CAAChB,GAAS,CAACP,MAAM,CAAGc,EAAKd,MAAM,EAAImC,EACvC,IAAI,CAAC5B,GAAS,CAACU,aAAa,CAAGH,EAAKG,aAAa,CAG3CqB,gBAAiB,KK9FYxB,MACjChG,EL8FF,OK9FEA,EAAWyH,SCHfzsB,CAAY,CACZkqB,CAAuB,CACvBmC,CAAsB,CACtBK,CAAsB,EAItB,GAAI,CAACxC,GAAUA,IAAWmC,EAAe,OAAOrsB,EAEhD,IAAM2sB,EAAQ3sB,EAAKwC,WAAW,SAI9B,CAAKkqB,IACChD,GAAciD,EAAO,SACrBjD,GAAciD,EAAO,IAAIzC,EAAO1nB,WAAW,KADNxC,EAKpCspB,GAActpB,EAAM,IAAIkqB,EACjC,EDhBIc,CAFmCA,EL+FL,CAC5BF,SAAU,IAAI,CAACL,GAAS,CAACK,QAAQ,CACjCW,QAAS,IAAI,CAAChB,GAAS,CAACgB,OAAO,CAC/BY,cAAe,IAAK,CAAC5B,GAAS,CAACxjB,OAAO,CAAC2lB,WAAW,CAE9C1kB,KAAAA,EADA,IAAI,CAACuiB,GAAS,CAAC4B,aAAa,CAEhCnC,OAAQ,IAAI,CAACO,GAAS,CAACP,MAAM,CAC7BlF,SAAU,IAAI,CAACyF,GAAS,CAAC9B,GAAG,CAAC3D,QAAQ,CACrCmG,cAAe,IAAI,CAACV,GAAS,CAACU,aAAa,GKrGxCnG,QAAQ,CACbgG,EAAKd,MAAM,CACXc,EAAKS,OAAO,CAAGvjB,KAAAA,EAAY8iB,EAAKqB,aAAa,CAC7CrB,EAAK0B,YAAY,EAGf1B,CAAAA,EAAKS,OAAO,EAAI,CAACT,EAAKG,aAAa,GACrCnG,CAAAA,EAAW+D,GAAoB/D,EAAQ,EAGrCgG,EAAKS,OAAO,EACdzG,CAAAA,EAAWwE,GACTF,GAActE,EAAU,eAAegG,EAAKS,OAAO,EACnDT,MAAAA,EAAKhG,QAAQ,CAAW,aAAe,QAAO,EAIlDA,EAAWsE,GAActE,EAAUgG,EAAKF,QAAQ,EACzC,CAACE,EAAKS,OAAO,EAAIT,EAAKG,aAAa,CACtC,EAAUxJ,QAAQ,CAAC,KAEjBqD,EADAwE,GAAcxE,EAAU,KAE1B+D,GAAoB/D,ELiFxB,CAEQ6H,cAAe,CACrB,OAAO,IAAI,CAACpC,GAAS,CAAC9B,GAAG,CAACmE,MAAM,CAGlC,IAAWrB,SAAU,CACnB,OAAO,IAAI,CAAChB,GAAS,CAACgB,OAAO,CAG/B,IAAWA,QAAQA,CAA2B,CAAE,CAC9C,IAAI,CAAChB,GAAS,CAACgB,OAAO,CAAGA,CAC3B,CAEA,IAAWvB,QAAS,CAClB,OAAO,IAAI,CAACO,GAAS,CAACP,MAAM,EAAI,EAClC,CAEA,IAAWA,OAAOA,CAAc,CAAE,C,IAG7B,IAFH,GACE,CAAC,IAAI,CAACO,GAAS,CAACP,MAAM,EACtB,QAAC,OAAI,CAACO,GAAS,CAACxjB,OAAO,CAACmkB,UAAU,eAAjC,KAAmCF,IAAI,SAAvC,EAAyCnB,OAAO,CAAChnB,QAAQ,CAACmnB,EAAM,EAEjE,MAAM,qBAEL,CAFK,UACJ,CAAC,8CAA8C,EAAEA,EAAO,CAAC,CAAC,EADtD,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAGF,KAAI,CAACO,GAAS,CAACP,MAAM,CAAGA,CAC1B,CAEA,IAAImC,eAAgB,CAClB,OAAO,IAAI,CAAC5B,GAAS,CAAC4B,aAAa,CAGrC,IAAIJ,cAAe,CACjB,OAAO,IAAI,CAACxB,GAAS,CAACwB,YAAY,CAGpC,IAAIc,cAAe,CACjB,OAAO,IAAI,CAACtC,GAAS,CAAC9B,GAAG,CAACoE,YAAY,CAGxC,IAAIf,MAAO,CACT,OAAO,IAAI,CAACvB,GAAS,CAAC9B,GAAG,CAACqD,IAAI,CAGhC,IAAIA,KAAKhrB,CAAa,CAAE,CACtB,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACqD,IAAI,CAAGhrB,CAC5B,CAEA,IAAI8qB,UAAW,CACb,OAAO,IAAI,CAACrB,GAAS,CAAC9B,GAAG,CAACmD,QAAQ,CAGpC,IAAIA,SAAS9qB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACmD,QAAQ,CAAG9qB,CAChC,CAEA,IAAIgsB,MAAO,CACT,OAAO,IAAI,CAACvC,GAAS,CAAC9B,GAAG,CAACqE,IAAI,CAGhC,IAAIA,KAAKhsB,CAAa,CAAE,CACtB,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACqE,IAAI,CAAGhsB,CAC5B,CAEA,IAAIisB,UAAW,CACb,OAAO,IAAI,CAACxC,GAAS,CAAC9B,GAAG,CAACsE,QAAQ,CAGpC,IAAIA,SAASjsB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACsE,QAAQ,CAAGjsB,CAChC,CAEA,IAAIksB,MAAO,CACT,IAAMlI,EAAW,IAAI,CAACwH,cAAc,GAC9BM,EAAS,IAAI,CAACD,YAAY,GAChC,MAAO,CAAC,EAAE,IAAI,CAACI,QAAQ,CAAC,EAAE,EAAE,IAAI,CAACjB,IAAI,CAAC,EAAEhH,EAAS,EAAE8H,EAAO,EAAE,IAAI,CAACzD,IAAI,CAAC,CAAC,CAGzE,IAAI6D,KAAKvE,CAAW,CAAE,CACpB,IAAI,CAAC8B,GAAS,CAAC9B,GAAG,CAAG2B,GAAS3B,GAC9B,IAAI,CAACoC,OAAO,EACd,CAEA,IAAIoC,QAAS,CACX,OAAO,IAAI,CAAC1C,GAAS,CAAC9B,GAAG,CAACwE,MAAM,CAGlC,IAAInI,UAAW,CACb,OAAO,IAAI,CAACyF,GAAS,CAAC9B,GAAG,CAAC3D,QAAQ,CAGpC,IAAIA,SAAShkB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAAC3D,QAAQ,CAAGhkB,CAChC,CAEA,IAAIqoB,MAAO,CACT,OAAO,IAAI,CAACoB,GAAS,CAAC9B,GAAG,CAACU,IAAI,CAGhC,IAAIA,KAAKroB,CAAa,CAAE,CACtB,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACU,IAAI,CAAGroB,CAC5B,CAEA,IAAI8rB,QAAS,CACX,OAAO,IAAI,CAACrC,GAAS,CAAC9B,GAAG,CAACmE,MAAM,CAGlC,IAAIA,OAAO9rB,CAAa,CAAE,CACxB,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACmE,MAAM,CAAG9rB,CAC9B,CAEA,IAAIosB,UAAW,CACb,OAAO,IAAI,CAAC3C,GAAS,CAAC9B,GAAG,CAACyE,QAAQ,CAGpC,IAAIA,SAASpsB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAACyE,QAAQ,CAAGpsB,CAChC,CAEA,IAAIqsB,UAAW,CACb,OAAO,IAAI,CAAC5C,GAAS,CAAC9B,GAAG,CAAC0E,QAAQ,CAGpC,IAAIA,SAASrsB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAAC9B,GAAG,CAAC0E,QAAQ,CAAGrsB,CAChC,CAEA,IAAI8pB,UAAW,CACb,OAAO,IAAI,CAACL,GAAS,CAACK,QAAQ,CAGhC,IAAIA,SAAS9pB,CAAa,CAAE,CAC1B,IAAI,CAACypB,GAAS,CAACK,QAAQ,CAAG9pB,EAAMikB,UAAU,CAAC,KAAOjkB,EAAQ,CAAC,CAAC,EAAEA,EAAM,CAAC,CAGvEoE,UAAW,CACT,OAAO,IAAI,CAAC8nB,IAAI,CAGlBI,QAAS,CACP,OAAO,IAAI,CAACJ,IAAI,CAGlB,CAAChpB,OAAOe,GAAG,CAAC,+BAA+B,EAAG,CAC5C,MAAO,CACLioB,KAAM,IAAI,CAACA,IAAI,CACfC,OAAQ,IAAI,CAACA,MAAM,CACnBF,SAAU,IAAI,CAACA,QAAQ,CACvBI,SAAU,IAAI,CAACA,QAAQ,CACvBD,SAAU,IAAI,CAACA,QAAQ,CACvBpB,KAAM,IAAI,CAACA,IAAI,CACfF,SAAU,IAAI,CAACA,QAAQ,CACvBkB,KAAM,IAAI,CAACA,IAAI,CACfhI,SAAU,IAAI,CAACA,QAAQ,CACvB8H,OAAQ,IAAI,CAACA,MAAM,CACnBC,aAAc,IAAI,CAACA,YAAY,CAC/B1D,KAAM,IAAI,CAACA,IAAI,CAEnB,CAEAkE,OAAQ,CACN,OAAO,IAAI7C,GAAQrX,OAAO,IAAI,EAAG,IAAI,CAACoX,GAAS,CAACxjB,OAAO,CACzD,CACF,COpRyB/C,OAAO,oBAOCspB,QAuC9BtpB,OAAOe,GAAG,CAAC,+BCrBP,IAAMwoB,GAAoB,IAI7BC,aAAaC,EAEjB,ECDaC,GAAoB1pB,OAAOe,GAAG,CAAC,cA4E5C,SAAS4oB,GACPpQ,CAAoB,CACpBqQ,CAAqC,E,IAIjCrQ,EADCA,GACD,OAAAA,CAAAA,EAAAA,EAAUsQ,iBAAiB,IAA3BtQ,EAA6BuQ,KAAK,GAGnC,SAAUpC,GAAG,CAACqC,gBAAgB,EAC7B/Z,MAAAA,QAAQ0X,GAAG,CAACsC,sBAAsB,GACpCzQ,EAAU0Q,kBAAkB,GAY9B1Q,EAAU2Q,YAAY,GAAK,EAAE,CAE7B3Q,EAAU2Q,YAAY,CAAC5nB,IAAI,CAAC,CAC1B,GAAGsnB,CAAG,CACNO,IAAKC,YAAYC,UAAU,CAAGD,YAAY1nB,GAAG,GAC7C4nB,IAAK/Q,EAAUgR,WAAW,EAAI,CAChC,GACF,ChD5HA,GAAM,CAAE7C,IAAAA,EAAG,CAAE8C,OAAAA,EAAM,CAAE,CAAG5T,CAAAA,MAAAA,CAAAA,EAAAA,UAAS,EAATA,KAAAA,EAAAA,EAAY5G,OAAO,GAAI,CAAC,EAE1Cya,GACJ/C,IACA,CAACA,GAAIgD,QAAQ,EACZhD,CAAAA,GAAIiD,WAAW,EAAKH,CAAAA,MAAAA,GAAAA,KAAAA,EAAAA,GAAQI,KAAK,GAAI,CAAClD,GAAImD,EAAE,EAAInD,SAAAA,GAAIoD,IAAI,EAErDC,GAAe,CACnBC,EACAC,EACA1sB,EACAiP,KAEA,IAAM5L,EAAQopB,EAAIzoB,SAAS,CAAC,EAAGiL,GAASjP,EAClC4rB,EAAMa,EAAIzoB,SAAS,CAACiL,EAAQyd,EAAMluB,MAAM,EACxCmuB,EAAYf,EAAI3sB,OAAO,CAACytB,GAC9B,MAAO,CAACC,EACJtpB,EAAQmpB,GAAaZ,EAAKc,EAAO1sB,EAAS2sB,GAC1CtpB,EAAQuoB,CACd,EAEMgB,GAAY,CAACC,EAAcH,EAAe1sB,EAAU6sB,CAAI,GAC5D,GACO,IACL,IAAMrtB,EAAS,GAAK0oB,EACdjZ,EAAQzP,EAAOP,OAAO,CAACytB,EAAOG,EAAKruB,MAAM,EAC/C,MAAO,CAACyQ,EACJ4d,EAAOL,GAAahtB,EAAQktB,EAAO1sB,EAASiP,GAASyd,EACrDG,EAAOrtB,EAASktB,CACtB,EAPqB9b,OAWVkc,GAAOF,GAAU,UAAW,WAAY,mBAClCA,GAAU,UAAW,WAAY,mBAC9BA,GAAU,UAAW,YAClBA,GAAU,UAAW,YACvBA,GAAU,UAAW,YACtBA,GAAU,UAAW,YACdA,GAAU,UAAW,YAC7BA,GAAU,WAAY,YACpC,IAAMG,GAAMH,GAAU,WAAY,YAC5BI,GAAQJ,GAAU,WAAY,YAC9BK,GAASL,GAAU,WAAY,YACxBA,GAAU,WAAY,YACnC,IAAMM,GAAUN,GAAU,WAAY,YACvBA,GAAU,yBAA0B,YACtCA,GAAU,WAAY,YACnC,IAAMO,GAAQP,GAAU,WAAY,YACvBA,GAAU,WAAY,YACnBA,GAAU,WAAY,YACxBA,GAAU,WAAY,YACpBA,GAAU,WAAY,YACrBA,GAAU,WAAY,YACxBA,GAAU,WAAY,YACnBA,GAAU,WAAY,YACzBA,GAAU,WAAY,YACrBA,GAAU,WAAY,YiDvErCO,GAAML,GAAK,MACVC,GAAID,GAAK,MACVG,GAAOH,GAAK,MAEZK,GAAML,GAAK,MACVE,GAAMF,GAAK,MACXI,GAAQJ,GAAK,MAqEA,IC/Ef,MAOL1rB,YAAYgsB,CAAe,CAAEC,CAAoC,CAAE,CACjE,IAAI,CAAC5a,KAAK,CAAG,IAAI5T,IACjB,IAAI,CAACyuB,KAAK,CAAG,IAAIzuB,IACjB,IAAI,CAAC0uB,SAAS,CAAG,EACjB,IAAI,CAACH,OAAO,CAAGA,EACf,IAAI,CAACC,aAAa,CAAGA,GAAmB,KAAK,EAC/C,CAEAnuB,IAAIC,CAAmB,CAAEZ,CAAS,CAAQ,CACxC,GAAI,CAACY,GAAO,CAACZ,EAAO,OAEpB,IAAMoD,EAAO,IAAI,CAAC0rB,aAAa,CAAC9uB,GAEhC,GAAIoD,EAAO,IAAI,CAACyrB,OAAO,CAAE,CACvB1b,QAAQ8b,IAAI,CAAC,oCACb,MACF,CAEI,IAAI,CAAC/a,KAAK,CAACxQ,GAAG,CAAC9C,IACjB,KAAI,CAACouB,SAAS,EAAI,IAAI,CAACD,KAAK,CAAC3sB,GAAG,CAACxB,IAAQ,GAG3C,IAAI,CAACsT,KAAK,CAACvT,GAAG,CAACC,EAAKZ,GACpB,IAAI,CAAC+uB,KAAK,CAACpuB,GAAG,CAACC,EAAKwC,GACpB,IAAI,CAAC4rB,SAAS,EAAI5rB,EAElB,IAAI,CAAC8rB,KAAK,CAACtuB,EACb,CAEA8C,IAAI9C,CAAmB,CAAW,OAChC,EAAKA,IAEL,IAAI,CAACsuB,KAAK,CAACtuB,GACJhB,CAAAA,CAAQ,IAAI,CAACsU,KAAK,CAAC9R,GAAG,CAACxB,GAChC,CAEAwB,IAAIxB,CAAmB,CAAiB,CACtC,GAAI,CAACA,EAAK,OAEV,IAAMZ,EAAQ,IAAI,CAACkU,KAAK,CAAC9R,GAAG,CAACxB,GAC7B,GAAIZ,KAAUkH,IAAVlH,EAKJ,OADA,IAAI,CAACkvB,KAAK,CAACtuB,GACJZ,CACT,CAEQkvB,MAAMtuB,CAAW,CAAQ,CAC/B,IAAMZ,EAAQ,IAAI,CAACkU,KAAK,CAAC9R,GAAG,CAACxB,EACfsG,MAAAA,IAAVlH,IACF,IAAI,CAACkU,KAAK,CAACvQ,MAAM,CAAC/C,GAClB,IAAI,CAACsT,KAAK,CAACvT,GAAG,CAACC,EAAKZ,GACpB,IAAI,CAACmvB,gBAAgB,GAEzB,CAEQA,kBAAyB,CAC/B,KAAO,IAAI,CAACH,SAAS,CAAG,IAAI,CAACH,OAAO,EAAI,IAAI,CAAC3a,KAAK,CAAC9Q,IAAI,CAAG,GACxD,IAAI,CAACgsB,sBAAsB,EAE/B,CAEQA,wBAA+B,CACrC,IAAMC,EAAS,IAAI,CAACnb,KAAK,CAAClQ,IAAI,GAAG6N,IAAI,GAAG7R,KAAK,CAC7C,GAAIqvB,KAAWnoB,IAAXmoB,EAAsB,CACxB,IAAMC,EAAU,IAAI,CAACP,KAAK,CAAC3sB,GAAG,CAACitB,IAAW,CAC1C,KAAI,CAACL,SAAS,EAAIM,EAClB,IAAI,CAACpb,KAAK,CAACvQ,MAAM,CAAC0rB,GAClB,IAAI,CAACN,KAAK,CAACprB,MAAM,CAAC0rB,EACpB,CACF,CAEAE,OAAQ,CACN,IAAI,CAACrb,KAAK,CAACnQ,KAAK,GAChB,IAAI,CAACgrB,KAAK,CAAChrB,KAAK,GAChB,IAAI,CAACirB,SAAS,CAAG,CACnB,CAEAhrB,MAAO,CACL,MAAO,IAAI,IAAI,CAACkQ,KAAK,CAAClQ,IAAI,GAAG,CAG/BwrB,OAAO5uB,CAAW,CAAQ,CACpB,IAAI,CAACsT,KAAK,CAACxQ,GAAG,CAAC9C,KACjB,IAAI,CAACouB,SAAS,EAAI,IAAI,CAACD,KAAK,CAAC3sB,GAAG,CAACxB,IAAQ,EACzC,IAAI,CAACsT,KAAK,CAACvQ,MAAM,CAAC/C,GAClB,IAAI,CAACmuB,KAAK,CAACprB,MAAM,CAAC/C,GAEtB,CAEAmD,OAAc,CACZ,IAAI,CAACmQ,KAAK,CAACnQ,KAAK,GAChB,IAAI,CAACgrB,KAAK,CAAChrB,KAAK,GAChB,IAAI,CAACirB,SAAS,CAAG,CACnB,CAEA,IAAI5rB,MAAe,CACjB,OAAO,IAAI,CAAC8Q,KAAK,CAAC9Q,IAAI,CAGxB,IAAIqsB,aAAsB,CACxB,OAAO,IAAI,CAACT,SAAS,CAEzB,EDhC2C,IAAQ,GAAWhvB,EAAMC,MAAM,EAA1E,IE3EMyvB,GAA0B,CAAC,OAAQ,UAAU,CAEnD,SAASC,KACP,OAAO,IAAIlI,SAAS,KAAM,CAAExV,OAAQ,GAAI,EAC1C,C,0CCFA,IAAM2d,GAAgB,IAAIrT,IAAIre,OAAOmG,MAAM,CANN,CACnCwrB,UAAW,IACXC,UAAW,IACXC,aAAc,GAChB,IAiBO,SAASC,GACd5d,CAAc,EAEd,GACE,iBAAOA,GACPA,OAAAA,GACA,CAAE,YAAYA,CAAI,GAClB,iBAAOA,EAAMmS,MAAM,CAEnB,MAAO,GAET,GAAM,CAACgE,EAAQ0H,EAAW,CAAG7d,EAAMmS,MAAM,CAAC/jB,KAAK,CAAC,KAEhD,MACE+nB,6BAAAA,GACAqH,GAAclsB,GAAG,CAAC7B,OAAOouB,GAE7B,CCtCO,6BAAKC,CAAkB,E,kIAAlBA,C,MCoBL,SAASC,GAAgB/d,CAAc,EAC5C,GACE,iBAAOA,GACPA,OAAAA,GACA,CAAE,YAAYA,CAAI,GAClB,iBAAOA,EAAMmS,MAAM,CAEnB,MAAO,GAGT,IAAMA,EAASnS,EAAMmS,MAAM,CAAC/jB,KAAK,CAAC,KAC5B,CAAC4vB,EAAWrgB,EAAK,CAAGwU,EACpB8L,EAAc9L,EAAO1jB,KAAK,CAAC,EAAG,IAAIX,IAAI,CAAC,KAGvCowB,EAAazuB,OAFJ0iB,EAAOgM,EAAE,CAAC,KAIzB,MACEH,kBAAAA,GACCrgB,CAAAA,YAAAA,GAAsBA,SAAAA,CAAc,GACrC,iBAAOsgB,GACP,CAAC/oB,MAAMgpB,IACPA,KAAcJ,EAElB,CC1CO,SAASM,GACdC,CAAoB,CACpB1K,CAAa,MAOTrF,EAJJ,IAAIgQ,SCsBqCte,CAAc,EAEvD,GCjBmB,UAAf,ODiBoBA,GCjBOqS,ODiBPrS,GCjByB,WDiBzBA,GCbjBqS,qCAAAA,EAAIF,MAAM,ECJV4L,GFoBe/d,IEpBW4d,GFoBX5d,IAMlBoS,GAAqBpS,GATO,OAAOA,EAAMmS,MAAM,EDxBpBkM,IAK/B,GACE,iBAAOA,GACPA,OAAAA,GACA,iBAAO,EAAqB/P,OAAO,CAGnC,IADAA,EAAU,EAAqBA,OAAO,CAClC,iBAAO,EAAqB2F,KAAK,CAAe,CAClD,IAAMsK,EAA6B,EAAqBtK,KAAK,CACvDuK,EAAaD,EAAmBjwB,OAAO,CAAC,MAC9C,GAAIkwB,EAAa,GAAI,CACnB,IAAMxe,EAAQ,qBAIb,CAJa,MACZ,CAAC,MAAM,EAAE2T,EAAM;;gBAET,EAAErF,EAAQ,CAAC,EAHL,qB,MAAA,O,WAAA,G,aAAA,EAId,EACAtO,CAAAA,EAAMiU,KAAK,CACT,UAAYjU,EAAMsO,OAAO,CAAGiQ,EAAmB9vB,KAAK,CAAC+vB,GACvDzd,QAAQf,KAAK,CAACA,GACd,MACF,CACF,MACgC,UAAvB,OAAOqe,GAChB/P,CAAAA,EAAU+P,CAAU,EAGtB,GAAI/P,EAAS,CACXvN,QAAQf,KAAK,CAAC,CAAC,MAAM,EAAE2T,EAAM;;kBAEf,EAAErF,EAAQ,CAAC,EACzB,MACF,CAEAvN,QAAQf,KAAK,CACX,CAAC,MAAM,EAAE2T,EAAM,wOAAwO,CAAC,EAE1P5S,QAAQf,KAAK,CAACqe,GAEhB,C,2DIuGO,IAAMI,GAAmBxL,EAAAA,aAAmB,CACjD,MAEWyL,GAAsBzL,EAAAA,aAAmB,CAK5C,MAEG0L,GAA4B1L,EAAAA,aAAmB,CAKzD,MAEU2L,GAAkB3L,EAAAA,aAAmB,CAAkB,MASvD4L,GAAqB5L,EAAAA,aAAmB,CAAc,IAAI9I,IC3KhE,OAAM2U,GAOXruB,aAAc,CACZ,IAAI,CAACoO,KAAK,CAAG,EACb,IAAI,CAACkgB,cAAc,CAAG,EAAE,CACxB,IAAI,CAAC1oB,SAAS,CAAG,EAAE,CACnB,IAAI,CAAC2oB,WAAW,CAAG,GACnB,IAAI,CAACC,WAAW,CAAG,EACrB,CAEQC,qBAAsB,CACvB,IAAI,CAACF,WAAW,GACnB,IAAI,CAACA,WAAW,CAAG,GACnBle,QAAQqe,QAAQ,CAAC,KAEf,GADA,IAAI,CAACH,WAAW,CAAG,GACf,QAAI,CAACngB,KAAK,CAAQ,CACpB,IAAK,IAAIrK,EAAI,EAAGA,EAAI,IAAI,CAACuqB,cAAc,CAAClxB,MAAM,CAAE2G,IAC9C,IAAI,CAACuqB,cAAc,CAACvqB,EAAE,EAExB,KAAI,CAACuqB,cAAc,CAAClxB,MAAM,CAAG,CAC/B,CACF,IAEG,IAAI,CAACoxB,WAAW,GACnB,IAAI,CAACA,WAAW,CAAG,GACnBlnB,WAAW,KAET,GADA,IAAI,CAACknB,WAAW,CAAG,GACf,QAAI,CAACpgB,KAAK,CAAQ,CACpB,IAAK,IAAIrK,EAAI,EAAGA,EAAI,IAAI,CAAC6B,SAAS,CAACxI,MAAM,CAAE2G,IACzC,IAAI,CAAC6B,SAAS,CAAC7B,EAAE,EAEnB,KAAI,CAAC6B,SAAS,CAACxI,MAAM,CAAG,CAC1B,CACF,EAAG,GAEP,CAMAuxB,YAAa,CACX,OAAO,IAAIloB,QAAc,IACvB,IAAI,CAAC6nB,cAAc,CAAC3rB,IAAI,CAACoH,GACN,IAAf,IAAI,CAACqE,KAAK,EACZ,IAAI,CAACqgB,mBAAmB,EAE5B,EACF,CAOAG,YAAa,CACX,OAAO,IAAInoB,QAAc,IACvB,IAAI,CAACb,SAAS,CAACjD,IAAI,CAACoH,GACD,IAAf,IAAI,CAACqE,KAAK,EACZ,IAAI,CAACqgB,mBAAmB,EAE5B,EACF,CAEAI,WAAY,CACV,IAAI,CAACzgB,KAAK,EACZ,CAEA0gB,SAAU,CAOR,IAAI,CAAC1gB,KAAK,GACS,IAAf,IAAI,CAACA,KAAK,EACZ,IAAI,CAACqgB,mBAAmB,EAE5B,CACF,CCvFA,IAAMM,GAA+B,6BAE9B,SAASC,GAA6B3vB,CAAc,CAAE0X,CAAY,SACvE,GAAiCtU,IAAI,CAACsU,GAC7B,IAAK1X,EAAO,IAAG0X,EAAK,IAEtB,IAAK1X,EAAO,IAAGgC,KAAKC,SAAS,CAACyV,GAAM,IAC7C,CAUO,IAAMkY,GAAsB,IAAIvV,IAAI,CACzC,iBACA,gBACA,uBACA,WACA,UACA,iBAIA,OACA,QACA,UAIA,SAGA,cAIA,SACA,WACA,aACD,EC9CKwV,GAAsC,CAAE5c,QAAS,IAAK,EAGtDjB,GACJ,mBAAOmR,EAAAA,KAAW,CACdA,EAAAA,KAAW,CACX,GAAgCzd,EAKhCoqB,GAAiB9e,QAAQ0X,GAAG,CAACqH,iBAAiB,CAChD9e,QAAQf,KAAK,CACbe,QAAQ8b,IAAI,CA0BT,SAASiD,GACdC,CAAoC,EAEpC,OAAO,SAAyB,GAAG9uB,CAAU,EAkBzC2uB,GAjBcG,KAAc9uB,GAmBhC,CACF,CA9C+B6Q,GAE7B,IACE,GAAI,CACF8d,GAAeD,GAAS5c,OAAO,CACjC,QAAU,CACR4c,GAAS5c,OAAO,CAAG,IACrB,CACF,GC2KF,IAAMid,GAAe,IAAIvJ,QAkJzB,SAASwJ,GAA0BC,CAAwB,EACzD,IAAMC,EAAeH,GAAahwB,GAAG,CAACkwB,GACtC,GAAIC,EACF,OAAOA,EAMT,IAAM7Q,EAAUpY,QAAQsD,OAAO,CAAC0lB,GAYhC,OAXAF,GAAazxB,GAAG,CAAC2xB,EAAkB5Q,GAEnCxjB,OAAO8F,IAAI,CAACsuB,GAAkBhf,OAAO,CAAC,IAChCwe,GAAoBpuB,GAAG,CAACkW,IAIxB8H,CAAAA,CAAe,CAAC9H,EAAK,CAAG0Y,CAAgB,CAAC1Y,EAAK,CAEpD,GAEO8H,CACT,CA6FA,SAAS8Q,GACPzM,CAAyB,CACzBlB,CAAkB,EAElB,IAAM0D,EAASxC,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,qBAIN,CAJM,MACL,CAAC,EAAEwC,EAAO,KAAK,EAAE1D,EAAW,0HAAE,CAAC,EAD1B,qB,MAAA,O,WAAA,G,aAAA,EAIP,EACF,CAjB0BqN,GACxBM,IAIAN,GAcF,SACEnM,CAAyB,CACzBlB,CAAkB,CAClB4N,CAAgC,EAEhC,IAAMlK,EAASxC,EAAQ,CAAC,OAAO,EAAEA,EAAM,EAAE,CAAC,CAAG,cAC7C,OAAO,qBAON,CAPM,MACL,CAAC,EAAEwC,EAAO,KAAK,EAAE1D,EAId,iLAAE6N,SAK4BC,CAAyB,EAC5D,OAAQA,EAAW1yB,MAAM,EACvB,KAAK,EACH,MAAM,qBAEL,CAFK,IAAIwgB,EACR,uFADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,EACF,MAAK,EACH,MAAO,CAAC,EAAE,EAAEkS,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,MAC1B,EACH,MAAO,CAAC,EAAE,EAAEA,CAAU,CAAC,EAAE,CAAC,SAAS,EAAEA,CAAU,CAAC,EAAE,CAAC,EAAE,CAAC,SAC/C,CACP,IAAIrO,EAAc,GAClB,IAAK,IAAI1d,EAAI,EAAGA,EAAI+rB,EAAW1yB,MAAM,CAAG,EAAG2G,IACzC0d,GAAe,CAAC,EAAE,EAAEqO,CAAU,CAAC/rB,EAAE,CAAC,IAAI,CAAC,CAGzC,OADA0d,EAAe,CAAC,QAAQ,EAAEqO,CAAU,CAACA,EAAW1yB,MAAM,CAAG,EAAE,CAAC,EAAE,CAAC,CAGnE,CACF,EAxBqCwyB,GAAmB,gEAAE,CAJvB,EAD1B,qB,MAAA,O,WAAA,G,aAAA,EAOP,EACF,GC5dE,EACI,oEAGSvrB,OCwEF0rB,GACX/vB,YACE,CAAoC,CACpC,CAAiC,CACjC,C,KAFgBuP,KAAK,CAALA,E,KACAtM,OAAO,CAAPA,CACf,CACL,CAuFO,MAAM+sB,WAA4B9Y,E,eAoBhB+Y,aAAa,CAAGA,CAAaA,aAexC,CACV9Y,SAAAA,CAAQ,CACRN,WAAAA,CAAU,CACVqZ,iBAAAA,CAAgB,CAChBC,iBAAAA,CAAgB,CACW,CAAE,CAe7B,GAdA,KAAK,CAAC,CAAEhZ,SAAAA,EAAUN,WAAAA,CAAW,GAnC9B,KACewJ,oBAAoB,CAAGA,EAAAA,oBAAoBA,CAI1D,KACexG,gBAAgB,CAAGA,EAAAA,gBAAgBA,CAKlD,KACeuW,WAAW,CAAGA,EAO7B,KACeC,kBAAkB,CAAGA,GAAAA,kBAAkBA,CAiBrD,IAAI,CAACH,gBAAgB,CAAGA,EACxB,IAAI,CAACC,gBAAgB,CAAGA,EAIxB,IAAI,CAACG,OAAO,CAAGC,SdtNjBC,CAA0B,EAI1B,IAAMF,EAAkDtP,EAAayP,MAAM,CACzE,CAACC,EAAKC,IAAY,EAChB,GAAGD,CAAG,CAGN,CAACC,EAAO,CAAEH,CAAQ,CAACG,EAAO,EAAI7D,EAChC,GACA,CAAC,GAKG8D,EAAc,IAAIlX,IAAIsH,EAAalkB,MAAM,CAAC,GAAY0zB,CAAQ,CAACG,EAAO,GAM5E,IAAK,IAAMA,KALK9D,GAAwB/vB,MAAM,CAC5C,GAAY,CAAC8zB,EAAY/vB,GAAG,CAAC8vB,IAID,CAI5B,GAAIA,SAAAA,EAAmB,CACjBH,EAASK,GAAG,GAEdP,EAAQQ,IAAI,CAAGN,EAASK,GAAG,CAG3BD,EAAY9mB,GAAG,CAAC,SAElB,QACF,CAGA,GAAI6mB,YAAAA,EAAsB,CAIxB,IAAMI,EAAuB,CAAC,aAAcH,EAAY,EAInDA,EAAY/vB,GAAG,CAAC,SAAW+vB,EAAY/vB,GAAG,CAAC,QAC9CkwB,EAAMpuB,IAAI,CAAC,QAKb,IAAMM,EAAU,CAAE+tB,MAAOD,EAAME,IAAI,GAAG5zB,IAAI,CAAC,KAAM,CAIjDizB,CAAAA,EAAQY,OAAO,CAAG,IAAM,IAAItM,SAAS,KAAM,CAAExV,OAAQ,IAAKnM,QAAAA,CAAQ,GAGlE2tB,EAAY9mB,GAAG,CAAC,WAEhB,QACF,CAEA,MAAM,qBAEL,CAFK,MACJ,CAAC,0EAA0E,EAAE6mB,EAAO,CAAC,EADjF,qB,MAAA,O,WAAA,G,aAAA,EAEN,EACF,CAEA,OAAOL,CACT,EcgJwCnZ,GAGpC,IAAI,CAACga,mBAAmB,CAAGA,GAAoBha,GAG/C,IAAI,CAACia,OAAO,CAAG,IAAI,CAACja,QAAQ,CAACia,OAAO,CAChC,eAAI,CAACjB,gBAAgB,CAAe,CACtC,GAAI,sBAAI,CAACiB,OAAO,CACd,MAAM,qBAEL,CAFK,MACJ,CAAC,gDAAgD,EAAEva,EAAWsK,QAAQ,CAAC,wHAAwH,CAAC,EAD5L,qB,MAAA,O,WAAA,G,aAAA,EAEN,GACK,GAAI,CAACkQ,SCnOhBC,CAA8C,EAE9C,MACEA,iBAAAA,EAAIF,OAAO,EACXE,UAAAA,EAAIF,OAAO,EACXE,CAAmB,IAAnBA,EAAIjO,UAAU,EACbiO,KAAmBjtB,IAAnBitB,EAAIjO,UAAU,EAAkBiO,EAAIjO,UAAU,CAAG,GAClD,mBAAOiO,EAAIC,oBAAoB,ED4NE,IAAI,CAACpa,QAAQ,GAAK,IAAI,CAACA,QAAQ,CAAC,GAAM,CACnE,MAAM,qBAEL,CAFK,MACJ,CAAC,uFAAuF,EAAEN,EAAWsK,QAAQ,CAAC,yGAAyG,CAAC,EADpN,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAEA,KAAI,CAACiQ,OAAO,CAAG,OAEnB,CAkCF,CAQA,QAAgBT,CAAc,CAAqB,QAEjD,E3CpQkBzxB,QAAQ,C2CoQRyxB,GAGX,IAAI,CAACL,OAAO,CAACK,EAAO,CAHO,IAAM,IAAI/L,SAAS,KAAM,CAAExV,OAAQ,GAAI,EAI3E,CAEA,MAAcoiB,GACZC,CAA0B,CAC1BC,CAAwB,CACxB9X,CAAoB,CAIpBU,CAA0B,CAC1BqX,CAAsB,CACtBC,CAAoB,CACpB5sB,CAAoC,CACpC,KAE2BA,EA0SzB4U,EAcmC8J,EtC7fvCmO,EAEOA,MsCsNDC,EApBJ,IAAMxH,EAAqB1Q,EAAU0Q,kBAAkB,CACjDyH,EAAmB,CAAC,QAAC/sB,CAAAA,EAAAA,EAAQgtB,UAAU,CAACC,YAAY,SAA/BjtB,EAAiCktB,SAAS,GAGrEC,SjB0qBuB/uB,CAAwB,EAEjD,GA37BO,CAA+D,IAA/D,UAAuC,CAAC2mB,GAAkB,CA27B3C,OAItB,IAAM9R,EAAWma,SmBp8BeC,CAA2B,EAC3D,IAAMC,EAAkB9P,EAAAA,KAAW,CAEjC,GAA+B,EAAE,EAGnC,OAAO,SACL+P,CAA2B,CAC3BnvB,CAAqB,MAajB0hB,EACA0N,EAZJ,GAAIpvB,GAAWA,EAAQ8e,MAAM,CAQ3B,OAAOmQ,EAAcE,EAAUnvB,GAKjC,GAAI,iBAAOmvB,GAA0BnvB,EAI9B,CAKL,IAAMwuB,EACJ,iBAAOW,GAAyBA,aAAoB5L,IAChD,IAAIgD,QAAQ4I,EAAUnvB,GACtBmvB,EACN,GACE,UAAS5B,MAAM,EAAciB,SAAAA,EAAQjB,MAAM,EAC3CiB,EAAQa,SAAS,CAMjB,OAAOJ,EAAcE,EAAUnvB,GAEjCovB,EAhEGnxB,KAAKC,SAAS,CAAC,CACpBswB,EAAQjB,MAAM,CACdjwB,MAAMd,IAAI,CAACgyB,EAAQ3uB,OAAO,CAACuV,OAAO,IAClCoZ,EAAQc,IAAI,CACZd,EAAQe,QAAQ,CAChBf,EAAQgB,WAAW,CACnBhB,EAAQiB,QAAQ,CAChBjB,EAAQkB,cAAc,CACtBlB,EAAQmB,SAAS,CAClB,EAwDGjO,EAAM8M,EAAQ9M,GAAG,MAtBjB0N,EApDiB,+CAqDjB1N,EAAMyN,EAwBR,IAAMS,EAAeV,EAAgBxN,GACrC,IAAK,IAAI/gB,EAAI,EAAGkvB,EAAID,EAAa51B,MAAM,CAAE2G,EAAIkvB,EAAGlvB,GAAK,EAAG,CACtD,GAAM,CAAChG,EAAK8gB,EAAQ,CAAGmU,CAAY,CAACjvB,EAAE,CACtC,GAAIhG,IAAQy0B,EACV,OAAO3T,EAAQrY,IAAI,CAAC,KAClB,IAAM0sB,EAAWF,CAAY,CAACjvB,EAAE,CAAC,EAAE,CACnC,GAAI,CAACmvB,EAAU,MAAM,qBAAwC,CAAxC,IAAItV,EAAe,sBAAnB,qB,MAAA,O,WAAA,G,aAAA,EAAuC,GAM5D,GAAM,CAAC+G,EAASI,EAAQ,CAAGT,GAAc4O,GAEzC,OADAF,CAAY,CAACjvB,EAAE,CAAC,EAAE,CAAGghB,EACdJ,CACT,EAEJ,CAIA,IAAM9F,EAAUwT,EAAcE,EAAUnvB,GAClC+vB,EAAoB,CAACX,EAAU3T,EAAS,KAAK,CAGnD,OAFAmU,EAAarwB,IAAI,CAACwwB,GAEXtU,EAAQrY,IAAI,CAAC,IAKlB,GAAM,CAACme,EAASI,EAAQ,CAAGT,GAAc4O,GAEzC,OADAC,CAAK,CAAC,EAAE,CAAGpO,EACJJ,CACT,EACF,CACF,EnB+2BqC1N,WAAWmc,KAAK,CAGnDnc,CAAAA,WAAWmc,KAAK,CAAGC,SAn1BnBC,CAAoB,CACpB,CAAEzZ,iBAAAA,CAAgB,CAAEwG,qBAAAA,CAAoB,CAAmB,EAI3D,IAAMkT,EAAU,MACdzM,EACAnR,SAYeA,EAIKA,MAdhBmP,EACJ,GAAI,CAEFA,CADAA,EAAM,IAAI6B,IAAIG,aAAiB6C,QAAU7C,EAAMhC,GAAG,CAAGgC,EAAK,EACtD0C,QAAQ,CAAG,GACf1E,EAAIyE,QAAQ,CAAG,EACjB,CAAE,KAAM,CAENzE,EAAMzgB,KAAAA,CACR,CACA,IAAMmvB,EAAW1O,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAKuE,IAAI,GAAI,GACxBsH,EAAShb,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,MAAAA,CAAAA,EAAAA,EAAMgb,MAAM,SAAZhb,EAAc8d,WAAW,EAAC,GAAK,MAIxCC,EAAa,CAAC/d,MAAAA,EAAAA,KAAAA,EAAAA,MAAAA,CAAAA,EAAAA,EAAM3G,IAAI,SAAX,EAAqB2kB,QAAQ,IAAK,GAC/CC,EAAWvjB,MAAAA,QAAQ0X,GAAG,CAAC8L,wBAAwB,CAK/CC,EAAiCJ,EACnCrvB,KAAAA,EACAomB,YAAYC,UAAU,CAAGD,YAAY1nB,GAAG,GAEtC6W,EAAYC,EAAiBC,QAAQ,GACrCsG,EAAgBC,EAAqBvG,QAAQ,GAG/Cia,EACF3T,GAAiBA,cAAAA,EAAclT,IAAI,CAC/BkT,EAAc2T,WAAW,CACzB,KACFA,GACFA,EAAYlF,SAAS,GAGvB,IAAM7tB,EAASgzB,CAAAA,EAAAA,EAAAA,SAAAA,IAAYC,KAAK,CAC9BP,EAAaxX,EAAmBgY,aAAa,CAAG7X,EAAc+W,KAAK,CACnE,CACEQ,SAAAA,EACAO,KAAMC,EAAAA,QAAQA,CAACC,MAAM,CACrBC,SAAU,CAAC,QAAS3D,EAAQ6C,EAAS,CAAC12B,MAAM,CAACC,SAASM,IAAI,CAAC,KAC3DgB,WAAY,CACV,WAAYm1B,EACZ,cAAe7C,EACf,gBAAiB7L,MAAAA,EAAAA,KAAAA,EAAAA,EAAKmD,QAAQ,CAC9B,gBAAiBnD,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAKqE,IAAI,GAAI9kB,KAAAA,CAChC,CACF,EACA,cAqKIkwB,MAjFEC,EAiPAhC,EAsMAiC,EA7eAC,EA5BJ,GAAIhB,GAOA,CAAC9Z,GAMDA,EAAU+a,WAAW,CAZvB,OAAOrB,EAAYxM,EAAOnR,GAgB5B,IAAMif,EACJ9N,GACA,iBAAOA,GACP,iBAAO,EAAmB6J,MAAM,CAE5B4D,EAAiB,GAGdp3B,CADQwY,MAAAA,EAAAA,KAAAA,EAAD,CAAe,CAACkf,EAAM,GACnBD,CAAAA,EAAiB,CAAc,CAACC,EAAM,CAAG,IAAG,EAIzDC,EAAe,I,IACLnf,EACVA,EAEE,EAHN,OAAO,KAA+B,IAAxBA,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,MAAAA,CAAAA,EAAAA,EAAM3G,IAAI,SAAV2G,CAAY,CAACkf,EAAM,EAC7Blf,MAAAA,EAAAA,KAAAA,EAAAA,MAAAA,CAAAA,EAAAA,EAAM3G,IAAI,SAAV2G,CAAY,CAACkf,EAAM,CACnBD,EAAAA,MACE,KAAe5lB,IAAI,SAAnB,CAAqB,CAAC6lB,EAAM,CAC5BxwB,KAAAA,CACR,EAGI0wB,EAAyBD,EAAa,cACpCE,EAAiBC,SAjLFD,CAAW,CAAEvT,CAAmB,EAC3D,IAAMyT,EAAsB,EAAE,CACxBC,EAGD,EAAE,CAEP,IAAK,IAAIpxB,EAAI,EAAGA,EAAIixB,EAAK53B,MAAM,CAAE2G,IAAK,CACpC,IAAM4a,EAAMqW,CAAI,CAACjxB,EAAE,CAanB,GAXI,iBAAO4a,EACTwW,EAAYxyB,IAAI,CAAC,CAAEgc,IAAAA,EAAKtP,OAAQ,gCAAiC,GACxDsP,EAAIvhB,MAAM,CxCvDgB,IwCwDnC+3B,EAAYxyB,IAAI,CAAC,CACfgc,IAAAA,EACAtP,OAAQ,4BACV,GAEA6lB,EAAUvyB,IAAI,CAACgc,GAGbuW,EAAU93B,MAAM,CxCjEgB,IwCiEa,CAC/CkT,QAAQ8b,IAAI,CACV,CAAC,oCAAoC,EAAE3K,EAAY,eAAe,CAAC,CACnEuT,EAAKh3B,KAAK,CAAC+F,GAAG1G,IAAI,CAAC,OAErB,KACF,CACF,CAEA,GAAI83B,EAAY/3B,MAAM,CAAG,EAGvB,IAAK,GAAM,CAAEuhB,IAAAA,CAAG,CAAEtP,OAAAA,CAAM,CAAE,GAF1BiB,QAAQ8b,IAAI,CAAC,CAAC,gCAAgC,EAAE3K,EAAY,EAAE,CAAC,EAEjC0T,GAC5B7kB,QAAQ8kB,GAAG,CAAC,CAAC,MAAM,EAAEzW,EAAI,EAAE,EAAEtP,EAAO,CAAC,EAGzC,OAAO6lB,CACT,EA4IUJ,EAAa,SAAW,EAAE,CAC1B,CAAC,MAAM,EAAEhO,EAAMvlB,QAAQ,GAAG,CAAC,EAGvB8zB,EACJjV,GACCA,CAAAA,UAAAA,EAAclT,IAAI,EACjBkT,cAAAA,EAAclT,IAAI,EAClBkT,kBAAAA,EAAclT,IAAI,EAClBkT,qBAAAA,EAAclT,IAAI,EAChBkT,EACA/b,KAAAA,EAEN,GAAIgxB,GACE30B,MAAMO,OAAO,CAAC+zB,GAAO,CAEvB,IAAMM,EACJD,EAAgBL,IAAI,EAAKK,CAAAA,EAAgBL,IAAI,CAAG,EAAE,EACpD,IAAK,IAAMrW,KAAOqW,EACXM,EAAcp2B,QAAQ,CAACyf,IAC1B2W,EAAc3yB,IAAI,CAACgc,EAGzB,CAGF,IAAMgT,EACJ,GAAkBvR,mBAAAA,EAAclT,IAAI,CAEhCkT,EAAcuR,YAAY,CAD1B,EAAE,CAKF4D,EACJnV,GAAiBA,mBAAAA,EAAclT,IAAI,CAC/B,iBACA0M,EAAU4b,UAAU,CAEpBC,EAAiB,CAAC,CAAC7b,EAAU8b,iBAAiB,CAEhDC,EAA0BpB,EAAe,SACzCqB,EAAc,EAImB,WAAnC,OAAOD,GACP,KAAkC,IAA3BZ,GAKJY,CAAAA,gBAAAA,GACCZ,IAAAA,GAEDY,aAAAA,GACEZ,CAAAA,EAAyB,GAAKA,CAA2B,IAA3BA,CAA+B,KAGhEP,EAAe,CAAC,kBAAkB,EAAEmB,EAAwB,mBAAmB,EAAEZ,EAAuB,gCAAgC,CAAC,CACzIY,EAA0BtxB,KAAAA,EAC1B0wB,EAAyB1wB,KAAAA,GAI7B,IAAMwxB,EAEJF,aAAAA,GACAA,aAAAA,GAGAJ,mBAAAA,GACAA,kBAAAA,EAOIO,EACJ,CAACP,GACD,CAACI,GACD,CAACZ,GACDnb,EAAUmJ,YAAY,CAKM,gBAA5B4S,GACA,KAAkC,IAA3BZ,EAEPA,EAAyB,GAKzB3U,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAelT,IAAI,IAAK,SACvB2oB,CAAAA,GAA+BC,CAA2B,GAE3Df,CAAAA,EAAyB,GAIzBY,CAAAA,aAAAA,GACAA,aAAAA,CAAqC,GAErCC,CAAAA,EAAc,CAAC,OAAO,EAAED,EAAwB,CAAC,EAGnDjB,EAAkBqB,SA3TxBC,CAAsB,CACtB9S,CAAa,EAEb,GAAI,CACF,IAAI+S,EAEJ,GAAID,CAAkB,IAAlBA,EACFC,ExCXwB,gBwCYnB,GACL,iBAAOD,GACP,CAACvxB,MAAMuxB,IACPA,EAAgB,GAEhBC,EAAuBD,OAClB,GAAI,KAAyB,IAAlBA,EAChB,MAAM,qBAEL,CAFK,MACJ,CAAC,0BAA0B,EAAEA,EAAc,MAAM,EAAE9S,EAAM,yCAAyC,CAAC,EAD/F,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,OAAO+S,CACT,CAAE,MAAOrU,EAAU,CAEjB,GAAIA,aAAeza,OAASya,EAAI/D,OAAO,CAAC3e,QAAQ,CAAC,sBAC/C,MAAM0iB,EAER,MACF,CACF,EAiSUmT,EACAnb,EAAUsJ,KAAK,EAGjB,IAAM/iB,EAAWo0B,EAAe,WAC1B2B,EACJ,kBAAO/1B,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAUZ,GAAG,EAChBY,EACA,IAAI2X,QAAQ3X,GAAY,CAAC,GAEzBg2B,EACJD,EAAY32B,GAAG,CAAC,kBAAoB22B,EAAY32B,GAAG,CAAC,UAEhD62B,EAAsB,CAAC,CAAC,MAAO,OAAO,CAACl3B,QAAQ,CACnDq1B,CAAAA,MAAAA,CAAAA,EAAAA,EAAe,SAAQ,EAAR,OAAfA,EAA0B51B,WAAW,EAAC,GAAK,OAavC03B,EAEJd,KAAsBlxB,GAAtBkxB,GAECI,CAAAA,KAA2BtxB,GAA3BsxB,GAGCA,YAAAA,CAAoC,GAEtCZ,KAA0B1wB,GAA1B0wB,EACIuB,EAGHD,GAGC,CAACzc,EAAU2c,cAAc,EAC1B,CAACJ,GAAwBC,CAAkB,GAC1Cf,GACAA,IAAAA,EAAgBhS,UAAU,CAE9B,GACEgT,GACAjW,KAAkB/b,IAAlB+b,GACAA,cAAAA,EAAclT,IAAI,CAQlB,OAJI6mB,IACFA,EAAYjF,OAAO,GACnBiF,EAAc,MAET9R,GACL7B,EAAcoW,YAAY,CAC1B,WAIJ,OAAQjB,GACN,IAAK,iBACHK,EAAc,8BACd,KAEF,KAAK,gBACH,GACED,gBAAAA,GACC,KAA2B,IAApBjB,GAAmCA,EAAkB,EAE7D,MAAM,qBAEL,CAFK,MACJ,CAAC,uCAAuC,EAAElB,EAAS,gDAAgD,CAAC,EADhG,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEFoC,EAAc,6BACd,KAEF,KAAK,aACH,GAAID,aAAAA,EACF,MAAM,qBAEL,CAFK,MACJ,CAAC,oCAAoC,EAAEnC,EAAS,6CAA6C,CAAC,EAD1F,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAEF,KAEF,KAAK,cAED,MAAkC,IAA3BuB,GACPA,IAAAA,CAA2B,IAE3Ba,EAAc,2BACdlB,ExChagB,WwCyatB,CA0BA,GAxBI,KAA2B,IAApBA,EACLa,kBAAAA,GAA2CE,EAGpCF,qBAAAA,GACTb,EAAkB,EAClBkB,EAAc,iCACLH,GACTf,EAAkB,EAClBkB,EAAc,gBACLU,GACT5B,EAAkB,EAClBkB,EAAc,kBAGdA,EAAc,aACdlB,EAAkBW,EACdA,EAAgBhS,UAAU,CxC5bZ,awC6alBqR,ExC7akB,WwC8alBkB,EAAc,8BAiBNA,GACVA,CAAAA,EAAc,CAAC,YAAY,EAAElB,EAAgB,CAAC,EAM9C,CAAE9a,CAAAA,EAAUoJ,WAAW,EAAI0R,IAAAA,CAAoB,GAE/C,CAAC4B,GAIDjB,GACAX,EAAkBW,EAAgBhS,UAAU,CAC5C,CAGA,GAAIqR,IAAAA,EAAuB,CACzB,GAAItU,GAAiBA,cAAAA,EAAclT,IAAI,CAKrC,OAJI6mB,IACFA,EAAYjF,OAAO,GACnBiF,EAAc,MAET9R,GACL7B,EAAcoW,YAAY,CAC1B,WAGF1T,GACElJ,EACAwG,EACA,CAAC,oBAAoB,EAAE0G,EAAM,CAAC,EAAElN,EAAUsJ,KAAK,CAAC,CAAC,CAGvD,CAIImS,GAAmBN,IAA2BL,GAChDW,CAAAA,EAAgBhS,UAAU,CAAGqR,CAAc,CAE/C,CAEA,IAAM+B,EACJ,iBAAO/B,GAAgCA,EAAkB,EAGrD,CAAE3V,iBAAAA,CAAgB,CAAE,CAAGnF,EAEvB8c,EACJtW,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAelT,IAAI,IAAK,WAAakT,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAelT,IAAI,IAAK,QACzDkT,EACA/b,KAAAA,EAEN,GACE0a,GACC0X,CAAAA,GACCC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAwBC,wBAAwB,GAElD,GAAI,CACFnE,EAAW,MAAMzT,EAAiB6X,gBAAgB,CAChDpD,EACAoB,EAAkB9N,EAAwBnR,EAE9C,CAAE,MAAOiM,EAAK,CACZtR,QAAQf,KAAK,CAAC,mCAAoCuX,EACpD,CAGF,IAAM+P,EAAWjd,EAAUgR,WAAW,EAAI,CAC1ChR,CAAAA,EAAUgR,WAAW,CAAGiM,EAAW,EAEnC,IAAIC,EAAe,IAAMrwB,QAAQsD,OAAO,GAElCgtB,EAAkB,MACtBC,EACAvC,KAEA,IAAMwC,EAAqB,CACzB,QACA,cACA,UACA,YACA,YACA,SACA,OACA,WACA,WACA,iBACA,SACA,YAGID,EAAU,EAAE,CAAG,CAAC,SAAS,CAC9B,CAED,GAAIpC,EAAgB,CAClB,IAAMsC,EAAoBpQ,EACpBqQ,EAA0B,CAC9B5S,KAAM,EAAkB6S,OAAO,EAAIF,EAAS3S,IAAI,EAGlD,IAAK,IAAMsQ,KAASoC,EAElBE,CAAU,CAACtC,EAAM,CAAGqC,CAAQ,CAACrC,EAAM,CAErC/N,EAAQ,IAAI6C,QAAQuN,EAASpS,GAAG,CAAEqS,EACpC,MAAO,GAAIxhB,EAAM,CACf,GAAM,CAAEyhB,QAAAA,CAAO,CAAE7S,KAAAA,CAAI,CAAErC,OAAAA,CAAM,CAAE,GAAGmV,EAAY,CAC5C1hB,EACFA,EAAO,CACL,GAAG0hB,CAAU,CACb9S,KAAM6S,GAAW7S,EACjBrC,OAAQ8U,EAAU3yB,KAAAA,EAAY6d,CAChC,CACF,CAGA,IAAMoV,EAAa,CACjB,GAAG3hB,CAAI,CACP3G,KAAM,C,GAAK2G,MAAAA,EAAAA,KAAAA,EAAAA,EAAM3G,IAAI,CAAEuoB,UAAW,SAAUV,SAAAA,CAAS,CACvD,EAEA,OAAOvD,EAAYxM,EAAOwQ,GACvB9wB,IAAI,CAAC,MAAOsrB,IAeX,GAdI,CAACkF,GAAWlD,GACd9J,GAAiBpQ,EAAW,CAC1B3X,MAAO6xB,EACPhP,IAAK0O,EACLoC,YAAanB,GAAuBmB,EACpC4B,YACE9C,IAAAA,GAAyBD,EACrB,OACA,OACND,aAAAA,EACAplB,OAAQ0iB,EAAI1iB,MAAM,CAClBuhB,OAAQ2G,EAAW3G,MAAM,EAAI,KAC/B,GAGAmB,MAAAA,EAAI1iB,MAAM,EACV2P,GACAyT,GACCiE,CAAAA,GACCC,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAwBC,wBAAwB,GAClD,CACA,IAAMV,EACJvB,GxCnlBY,WALA,QwC0lBRA,EAEN,GAAItU,GAAiBA,cAAAA,EAAclT,IAAI,CAAkB,CAGvD,IAAMuqB,EAAa,MAAM3F,EAAI4F,WAAW,GAElCC,EAAc,CAClB10B,QAAS5H,OAAOoD,WAAW,CAACqzB,EAAI7uB,OAAO,CAACuV,OAAO,IAC/C+L,KAAMqT,OAAOh4B,IAAI,CAAC63B,GAAYl2B,QAAQ,CAAC,UACvC6N,OAAQ0iB,EAAI1iB,MAAM,CAClB0V,IAAKgN,EAAIhN,GAAG,EAkBd,OAZA,MAAM/F,EAAiBjhB,GAAG,CACxB00B,EACA,CACE2B,KAAMnP,GAAgB6S,KAAK,CAC3BC,KAAMH,EACNtU,WAAY4S,CACd,EACA,CAAET,WAAY,GAAMhC,SAAAA,EAAUqD,SAAAA,EAAU7B,KAAAA,CAAK,GAE/C,MAAM8B,IAGC,IAAIlS,SAAS6S,EAAY,CAC9Bx0B,QAAS6uB,EAAI7uB,OAAO,CACpBmM,OAAQ0iB,EAAI1iB,MAAM,CAClByV,WAAYiN,EAAIjN,UAAU,EAE9B,CAAO,CAML,GAAM,CAACF,EAASI,EAAQ,CAAGT,GAAcwN,GAuCzC,OAlCAnN,EACG+S,WAAW,GACXlxB,IAAI,CAAC,MAAOkxB,I,IAUXhB,EATA,IAAMe,EAAaG,OAAOh4B,IAAI,CAAC83B,GAEzBC,EAAc,CAClB10B,QAAS5H,OAAOoD,WAAW,CAACkmB,EAAQ1hB,OAAO,CAACuV,OAAO,IACnD+L,KAAMkT,EAAWl2B,QAAQ,CAAC,UAC1B6N,OAAQuV,EAAQvV,MAAM,CACtB0V,IAAKH,EAAQG,GAAG,CAGlB4R,OAAAA,GAAAA,MAAAA,CAAAA,EAAAA,EAAwBC,wBAAwB,GAAhDD,EAAkD54B,GAAG,CACnD00B,EACAmF,GAGElB,GACF,MAAM1X,EAAiBjhB,GAAG,CACxB00B,EACA,CACE2B,KAAMnP,GAAgB6S,KAAK,CAC3BC,KAAMH,EACNtU,WAAY4S,CACd,EACA,CAAET,WAAY,GAAMhC,SAAAA,EAAUqD,SAAAA,EAAU7B,KAAAA,CAAK,EAGnD,GACC/U,KAAK,CAAC,GACL3P,QAAQ8b,IAAI,CAAC,4BAA6BtF,EAAOvX,IAElDwoB,OAAO,CAACjB,GAEJ/R,CACT,CACF,CAMA,OAFA,MAAM+R,IAEChF,CACT,GACC7R,KAAK,CAAC,IAEL,MADA6W,IACMvnB,CACR,EACJ,EAGIyoB,EAAyB,GACzBC,EAAoB,GAExB,GAAIzF,GAAYzT,EAAkB,CAChC,IAAImZ,EAYJ,GATExB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAwByB,YAAY,GACpCzB,EAAuBC,wBAAwB,GAE/CuB,EACExB,EAAuBC,wBAAwB,CAACp3B,GAAG,CAACizB,GAEtDyF,EAAoB,IAGlBxB,GAAyB,CAACyB,EAAiB,CAC7CpB,EAAe,MAAM/X,EAAiBqZ,IAAI,CAAC5F,GAC3C,IAAMW,EAAQvZ,EAAUmD,oBAAoB,CACxC,KACA,MAAMgC,EAAiBxf,GAAG,CAACizB,EAAU,CACnC2B,KAAMlP,GAAqB4S,KAAK,CAChCxU,WAAYqR,EACZlB,SAAAA,EACAqD,SAAAA,EACA7B,KAAAA,EACAqD,SAAU1G,CACZ,GAkBJ,GAhBI0E,GAIEjW,GAAiBA,cAAAA,EAAclT,IAAI,EACrC,MDjsBL,IAAIzG,QAAQ,GAAOojB,aAAapmB,ICqsB3B0vB,EACF,MAAM2D,IAGNrC,EAAsB,yCAGpBtB,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAOh2B,KAAK,GAAIg2B,EAAMh2B,KAAK,CAACg3B,IAAI,GAAKnP,GAAgB6S,KAAK,EAG5D,GAAIje,EAAU0e,YAAY,EAAInF,EAAM6D,OAAO,CACzCgB,EAAyB,OACpB,CACL,GAAI7E,EAAM6D,OAAO,GACfpd,EAAUgF,kBAAkB,GAAK,CAAC,EAC9B,CAAChF,EAAUgF,kBAAkB,CAAC4T,EAAS,EAAE,CAC3C,IAAM+F,EAAoBxB,EAAgB,IACvCvwB,IAAI,CAAC,MAAO0sB,GAAc,EACzB3O,KAAM,MAAM2O,EAASwE,WAAW,GAChCz0B,QAASiwB,EAASjwB,OAAO,CACzBmM,OAAQ8jB,EAAS9jB,MAAM,CACvByV,WAAYqO,EAASrO,UAAU,CACjC,GACCkT,OAAO,CAAC,KACPne,EAAUgF,kBAAkB,GAAK,CAAC,EAClC,OAAOhF,EAAUgF,kBAAkB,CAAC4T,GAAY,GAAG,GAKvD+F,EAAkBtY,KAAK,CAAC3P,QAAQf,KAAK,EAErCqK,EAAUgF,kBAAkB,CAAC4T,EAAS,CAAG+F,CAC3C,CAGFL,EAAkB/E,EAAMh2B,KAAK,CAAC26B,IAAI,EAGxC,CAEA,GAAII,EAAiB,CACfpE,GACF9J,GAAiBpQ,EAAW,CAC1B3X,MAAO6xB,EACPhP,IAAK0O,EACLoC,YAAAA,EACA4B,YAAaS,EAAoB,MAAQ,MACzCzD,aAAAA,EACAplB,OAAQ8oB,EAAgB9oB,MAAM,EAAI,IAClCuhB,OAAQhb,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAMgb,MAAM,GAAI,KAC1B,GAGF,IAAMuC,EAAW,IAAItO,SACnBgT,OAAOh4B,IAAI,CAACs4B,EAAgB3T,IAAI,CAAE,UAClC,CACEthB,QAASi1B,EAAgBj1B,OAAO,CAChCmM,OAAQ8oB,EAAgB9oB,MAAM,GAQlC,OAJA/T,OAAOC,cAAc,CAAC43B,EAAU,MAAO,CACrC/1B,MAAO+6B,EAAgBpT,GAAG,GAGrBoO,CACT,CACF,CAEA,GAAItZ,EAAU0Q,kBAAkB,EAAI3U,GAAQ,iBAAOA,EAAmB,CACpE,GAAM,CAAEtE,MAAAA,CAAK,CAAE,CAAGsE,EAKlB,GAAItE,aAAAA,EAAsB,CAExB,GAAI+O,GAAiBA,cAAAA,EAAclT,IAAI,CAKrC,OAJI6mB,IACFA,EAAYjF,OAAO,GACnBiF,EAAc,MAET9R,GACL7B,EAAcoW,YAAY,CAC1B,WAGF1T,GACElJ,EACAwG,EACA,CAAC,eAAe,EAAE0G,EAAM,CAAC,EAAElN,EAAUsJ,KAAK,CAAC,CAAC,CAGlD,CAEA,IAAMsV,EAAgB,SAAU7iB,EAC1B,CAAE3G,KAAAA,EAAO,CAAC,CAAC,CAAE,CAAG2G,EACtB,GACE,iBAAO3G,EAAKqU,UAAU,EACtBgS,GACArmB,EAAKqU,UAAU,CAAGgS,EAAgBhS,UAAU,CAC5C,CACA,GAAIrU,IAAAA,EAAKqU,UAAU,CAAQ,CAEzB,GAAIjD,GAAiBA,cAAAA,EAAclT,IAAI,CACrC,OAAO+U,GACL7B,EAAcoW,YAAY,CAC1B,WAGF1T,GACElJ,EACAwG,EACA,CAAC,oBAAoB,EAAE0G,EAAM,CAAC,EAAElN,EAAUsJ,KAAK,CAAC,CAAC,CAGvD,CAEKtJ,EAAUoJ,WAAW,EAAIhU,IAAAA,EAAKqU,UAAU,EAC3CgS,CAAAA,EAAgBhS,UAAU,CAAGrU,EAAKqU,UAAU,CAEhD,CACImV,GAAe,OAAO7iB,EAAK3G,IAAI,CAMrC,GAAIwjB,CAAAA,IAAYwF,EA+Dd,OAAOjB,EAAgB,GAAOtC,EA/DQ,EACtC,IAAMgE,EAAuBjG,CAC7B5Y,CAAAA,EAAUgF,kBAAkB,GAAK,CAAC,EAClC,IAAI2Z,EACF3e,EAAUgF,kBAAkB,CAAC6Z,EAAqB,CAEpD,GAAIF,EAAmB,CACrB,IAAMG,EAKF,MAAMH,EACV,OAAO,IAAI3T,SAAS8T,EAAkBnU,IAAI,CAAE,CAC1CthB,QAASy1B,EAAkBz1B,OAAO,CAClCmM,OAAQspB,EAAkBtpB,MAAM,CAChCyV,WAAY6T,EAAkB7T,UAAU,EAE5C,CAUA,IAAM8T,EAAkB5B,EAAgB,GAAMtC,GAK3CjuB,IAAI,CAAC8d,IA4BR,MAJAiU,CAtBAA,EAAoBI,EACjBnyB,IAAI,CAAC,MAAOoyB,IACX,IAAM1F,EAAW0F,CAAS,CAAC,EAAE,CAC7B,MAAO,CACLrU,KAAM,MAAM2O,EAASwE,WAAW,GAChCz0B,QAASiwB,EAASjwB,OAAO,CACzBmM,OAAQ8jB,EAAS9jB,MAAM,CACvByV,WAAYqO,EAASrO,UAAU,CAEnC,GACCkT,OAAO,CAAC,K,IAGFne,EAAD,OAACA,CAAAA,EAAAA,EAAUgF,kBAAkB,SAA5BhF,CAA8B,CAAC6e,EAAqB,GAIzD,OAAO7e,EAAUgF,kBAAkB,CAAC6Z,EAAqB,EAC1D,EAIexY,KAAK,CAAC,KAAO,GAE/BrG,EAAUgF,kBAAkB,CAAC6Z,EAAqB,CAAGF,EAE9CI,EAAgBnyB,IAAI,CAAC,GAAeoyB,CAAS,CAAC,EAAE,CACzD,CAGF,GAGF,GAAI7E,EACF,GAAI,CACF,OAAO,MAAM/yB,CACf,QAAU,CACJ+yB,GACFA,EAAYjF,OAAO,EAEvB,CAEF,OAAO9tB,CACT,EAWA,OALAuyB,EAAQsF,aAAa,CAAG,GACxBtF,EAAQuF,oBAAoB,CAAG,IAAMjf,EACrC0Z,EAAQwF,kBAAkB,CAAGzF,EAC3Brc,UAAsC,CAAC8S,GAAkB,CAAG,GAEvDwJ,CACT,EAY0Ctb,EAAU7U,EACpD,EiBprBe,CACTyW,iBAAkB,IAAI,CAACA,gBAAgB,CACvCwG,qBAAsB,IAAI,CAACA,oBAAoB,GAGjD,IAAM2Y,EAA2C,CAC/CC,OAAQj0B,EAAQi0B,MAAM,CAClBC,SF3ORzJ,CAAwB,CACxB7V,CAAoB,EAEpB,IAAMwG,EAAgBC,EAAAA,oBAAoBA,CAACvG,QAAQ,GACnD,GAAIsG,EACF,OAAQA,EAAclT,IAAI,EACxB,IAAK,YACL,IAAK,gBACL,IAAK,mBACH,OAAOisB,SAmDb1J,CAAwB,CACxB7V,CAAoB,CACpB8J,CAA8B,EAE9B,IAAM0V,EAAiBxf,EAAUyf,mBAAmB,CACpD,GAAID,EAAgB,CAClB,IAAIE,EAAwB,GAC5B,IAAK,IAAMv7B,KAAO0xB,EAChB,GAAI2J,EAAev4B,GAAG,CAAC9C,GAAM,CAC3Bu7B,EAAwB,GACxB,KACF,CAGF,GAAIA,QAEF,cAAI5V,EAAexW,IAAI,CAEdqsB,SAyCb9J,CAAwB,CACxBvM,CAAa,CACbQ,CAAoC,EAEpC,IAAMgM,EAAeH,GAAahwB,GAAG,CAACkwB,GACtC,GAAIC,EACF,OAAOA,EAGT,IAAM7Q,EAAUoD,GACdyB,EAAe8S,YAAY,CAC3B,YAiCF,OA/BAjH,GAAazxB,GAAG,CAAC2xB,EAAkB5Q,GAEnCxjB,OAAO8F,IAAI,CAACsuB,GAAkBhf,OAAO,CAAC,IAChCwe,GAAoBpuB,GAAG,CAACkW,IAI1B1b,OAAOC,cAAc,CAACujB,EAAS9H,EAAM,CACnCxX,MACE,IAAMyiB,EAAagN,GAA6B,SAAUjY,GACpDxH,EAAQogB,GAAwBzM,EAAOlB,GAC7C2B,GACET,EACAlB,EACAzS,EACAmU,EAEJ,EACA5lB,IAAI07B,CAAQ,EACVn+B,OAAOC,cAAc,CAACujB,EAAS9H,EAAM,CACnC5Z,MAAOq8B,EACPC,SAAU,GACVj6B,WAAY,EACd,EACF,EACAA,WAAY,GACZk6B,aAAc,EAChB,EAEJ,GAEO7a,CACT,EArFU4Q,EACA7V,EAAUsJ,KAAK,CACfQ,GAOGiW,SA+EXlK,CAAwB,CACxB2J,CAAmC,CACnCxf,CAAoB,CACpB8J,CAAwD,EAExD,IAAMgM,EAAeH,GAAahwB,GAAG,CAACkwB,GACtC,GAAIC,EACF,OAAOA,EAGT,IAAMkK,EAAsB,CAAE,GAAGnK,CAAgB,EAK3C5Q,EAAUpY,QAAQsD,OAAO,CAAC6vB,GA6EhC,OA5EArK,GAAazxB,GAAG,CAAC2xB,EAAkB5Q,GAEnCxjB,OAAO8F,IAAI,CAACsuB,GAAkBhf,OAAO,CAAC,IAChCwe,GAAoBpuB,GAAG,CAACkW,KAItBqiB,EAAev4B,GAAG,CAACkW,IACrB1b,OAAOC,cAAc,CAACs+B,EAAqB7iB,EAAM,CAC/CxX,MACE,IAAMyiB,EAAagN,GAA6B,SAAUjY,EAOtD2M,CAAwB,kBAAxBA,EAAexW,IAAI,CAErBiW,GACEvJ,EAAUsJ,KAAK,CACflB,EACA0B,EAAeN,eAAe,EAIhCK,GACEzB,EACApI,EACA8J,EAGN,EACAlkB,WAAY,EACd,GACAnE,OAAOC,cAAc,CAACujB,EAAS9H,EAAM,CACnCxX,MACE,IAAMyiB,EAAagN,GAA6B,SAAUjY,EAOtD2M,CAAwB,kBAAxBA,EAAexW,IAAI,CAErBiW,GACEvJ,EAAUsJ,KAAK,CACflB,EACA0B,EAAeN,eAAe,EAIhCK,GACEzB,EACApI,EACA8J,EAGN,EACA5lB,IAAI07B,CAAQ,EACVn+B,OAAOC,cAAc,CAACujB,EAAS9H,EAAM,CACnC5Z,MAAOq8B,EACPC,SAAU,GACVj6B,WAAY,EACd,EACF,EACAA,WAAY,GACZk6B,aAAc,EAChB,IAEE7a,CAAe,CAAC9H,EAAK,CAAG0Y,CAAgB,CAAC1Y,EAAK,CAGtD,GAEO8H,CACT,EA3KQ4Q,EACA2J,EACAxf,EACA8J,EAGN,CAGA,OAAO8L,GAA0BC,EACnC,EA1FqCA,EAAkB7V,EAAWwG,EAG9D,CAEF,OAiGSoP,GAjGiBC,EAC5B,EE6NYoK,SGrTVtU,CAAqB,EAErB,IAAM0T,EAA4C,CAAC,EAEnD,IAAK,GAAM,CAACl7B,EAAKZ,EAAM,GAAI9B,OAAOmd,OAAO,CAAC+M,GACnB,SAAVpoB,GACX87B,CAAAA,CAAM,CAACl7B,EAAI,CAAGZ,CAAI,EAGpB,OAAO87B,CACT,EH2SmCj0B,EAAQi0B,MAAM,EACrCrf,GAEFvV,KAAAA,CACN,EAEIqf,EAAwC,KAG5C,GAAI,CACF,GAAI4G,EAAoB,CACtB,IAAMwP,EAAqB,IAAI,CAAC3iB,QAAQ,CAACkM,UAAU,CAC7C0W,EAIJD,CAAuB,IAAvBA,GAAgCA,KAAuBz1B,IAAvBy1B,EzDvSZ,WyDyShBA,EAEN,GAAI/H,EAAkB,KA6ChBiI,EA1BJ,IAAMC,EAAwB,IAAIC,gBAC9BC,EAA6B,GAC3BpG,EAAc,IAAI1F,GACpBjL,EAAkBX,GAA2Bpe,KAAAA,GAE3C+1B,EACH1W,EAAiB,CAChBxW,KAAM,YACNsN,MAAO,SAGP6f,WAAY,CAAC,EACb1I,aAAcA,EACd6E,aAAcyD,EAAsB/X,MAAM,CAC1C+B,WAAYgW,EACZlG,YAAAA,EAGA3Q,gBAAAA,EACAC,WAAY0W,EACZO,OzDlVgB,WyDmVhBC,MzDnVgB,WyDoVhBvF,KAAM,IAAIrD,EAAa,CACvB6I,yBAA0B,IAC5B,EAGF,GAAI,CACFR,EAAoB,IAAI,CAAC3Z,oBAAoB,CAACxZ,GAAG,CAC/CuzB,EACA3I,EACAG,EACAoH,EAEJ,CAAE,MAAOpX,EAAK,CACRqY,EAAsB/X,MAAM,CAACuY,OAAO,CAGtCN,EAA6B,GAE7B9pB,CAAAA,QAAQ0X,GAAG,CAACqC,gBAAgB,EAC5B/Z,QAAQ0X,GAAG,CAAC2S,sBAAsB,GAElC/M,GAA0C/L,EAAKhI,EAAUsJ,KAAK,CAElE,CA0BA,GAxB+B,UAA7B,OAAO8W,GACPA,OAAAA,GACA,mBAAO,EAA2BxzB,IAAI,EAIpCwzB,EAA8CxzB,IAAI,CAClD,KAAO,EACP,IACMyzB,EAAsB/X,MAAM,CAACuY,OAAO,CAGtCN,EAA6B,GACpB9pB,QAAQ0X,GAAG,CAACqC,gBAAgB,EACrCuD,GACE/L,EACAhI,EAAUsJ,KAAK,CAGrB,GAGJ,MAAM6Q,EAAYnF,UAAU,GAExBuL,EAA4B,CAG9B,IAAMQ,GtCjUhB9I,EsCiUsDzO,EtC/T/C,MAAAyO,CAAAA,EAAAA,EAAclP,eAAe,CAAC,EAAE,SAAhCkP,EAAkC7P,UAAU,EsCgUzC,GAAI2Y,EACF,MAAM,qBAEL,CAFK,IAAInZ,GACR,CAAC,MAAM,EAAE5H,EAAUsJ,KAAK,CAAC,mDAAmD,EAAEyX,EAAc,6EAA6E,CAAC,EADtK,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAKA,OAHArqB,QAAQf,KAAK,CACX,+HAEI,qBAEL,CAFK,IAAIiS,GACR,CAAC,MAAM,EAAE5H,EAAUsJ,KAAK,CAAC,yIAAyI,CAAC,EAD/J,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAEJ,CAKA,IAAM0X,EAAkB,IAAIV,gBAC5B9W,EAAkBX,GAA2Bpe,KAAAA,GAE7C,IAAMw2B,EAA4CnX,EAAiB,CACjExW,KAAM,YACNsN,MAAO,SACP6f,WAAY,CAAC,EACb1I,aAAcA,EACd6E,aAAcoE,EAAgB1Y,MAAM,CACpC+B,WAAY2W,EACZ7G,YAAa,KACb3Q,gBAAAA,EACAC,WAAY0W,EACZO,OzDvakB,WyDwalBC,MzDxakB,WyDyalBvF,KAAM,IAAIrD,EAAa,CACvB6I,yBAA0B,IAC5B,EAEIM,EAAkB,GAsDtB,GArDAhJ,EAAM,MAAM,IAAIrrB,QAAQ,CAACsD,EAASqY,KAChCwH,GAAkB,UAChB,GAAI,CACF,IAAM5oB,EAAS,MAAO,IAAI,CAACqf,oBAAoB,CAACxZ,GAAG,CACjDg0B,EACApJ,EACAG,EACAoH,GAEF,GAAI8B,EAEF,OACK,GAAI,CAAE95B,CAAAA,aAAkB4jB,QAAO,EAAI,CAExC7a,EAAQ/I,GACR,MACF,CAEA85B,EAAkB,GAElB,IAAIC,EAAc,GAClB/5B,EAAO02B,WAAW,GAAGlxB,IAAI,CAAC,IACnBu0B,IACHA,EAAc,GAEdhxB,EACE,IAAI6a,SAASL,EAAM,CACjBthB,QAASjC,EAAOiC,OAAO,CACvBmM,OAAQpO,EAAOoO,MAAM,CACrByV,WAAY7jB,EAAO6jB,UAAU,IAIrC,EAAGzC,GACHwH,GAAkB,KACXmR,IACHA,EAAc,GACdH,EAAgB1W,KAAK,GACrB9B,EAAO4Y,GAAqBphB,EAAUsJ,KAAK,GAE/C,EACF,CAAE,MAAOtB,EAAK,CACZQ,EAAOR,EACT,CACF,GACAgI,GAAkB,KACXkR,IACHA,EAAkB,GAClBF,EAAgB1W,KAAK,GACrB9B,EAAO4Y,GAAqBphB,EAAUsJ,KAAK,GAE/C,EACF,GACI0X,EAAgB1Y,MAAM,CAACuY,OAAO,CAEhC,MAAMO,GAAqBphB,EAAUsJ,KAAK,EAK1C0X,EAAgB1W,KAAK,EAEzB,MACER,EAAiB,CACfxW,KAAM,mBACNsN,MAAO,SACP6f,WAAY,CAAC,EACb1I,aAAcA,EACdtO,WAAY0W,EACZO,OzDnfkB,WyDoflBC,MzDpfkB,WyDqflBvF,KAAM,IAAIrD,EAAa,EAGzBG,EAAM,MAAMzR,EAAAA,oBAAoBA,CAACxZ,GAAG,CAClC6c,EACA+N,EACAG,EACAoH,EAGN,MACElH,EAAM,MAAMzR,EAAAA,oBAAoBA,CAACxZ,GAAG,CAClCyT,EACAmX,EACAG,EACAoH,EAGN,CAAE,MAAOpX,EAAK,CACZ,GAAI0L,GAAgB1L,GAAM,CACxB,IAAMkD,EDneZ,GCme0ClD,GD/dnCrS,EAAMmS,MAAM,CAAC/jB,KAAK,CAAC,KAAKK,KAAK,CAAC,EAAG,IAAIX,IAAI,CAAC,KAJb,KCoe9B,GAAI,CAACynB,EACH,MAAM,qBAAsD,CAAtD,MAAU,6CAAV,qB,MAAA,O,WAAA,G,aAAA,EAAqD,GAK7D,IAAM7hB,EAAU,IAAI6U,QAAQ,CAAEmjB,SAAUnW,CAAI,GAW5C,MAL0B,YAAtBxK,EAAapN,IAAI,EACnB2L,EAAqB5V,EAASqX,EAAaxB,cAAc,EAIpD,IAAI8L,SAAS,KAAM,CAIxBxV,OAAQsiB,EAAYwJ,QAAQ,CACxB7N,GAAmB8N,QAAQ,CAC3BC,SD5eiC7rB,CAAoB,EACjE,GAAI,CAAC+d,GAAgB/d,GACnB,MAAM,qBAAiC,CAAjC,MAAU,wBAAV,qB,MAAA,O,WAAA,G,aAAA,EAAgC,GAGxC,OAAOvQ,OAAOuQ,EAAMmS,MAAM,CAAC/jB,KAAK,CAAC,KAAK+vB,EAAE,CAAC,IAC3C,ECse6C9L,GACnC3e,QAAAA,CACF,EACF,CAAO,GAAIkqB,GAA0BvL,GAEnC,OAAO,IAAIgD,SAAS,KAAM,CAAExV,ObjiB3BpQ,OADYuQ,EAAMmS,MAAM,CAAC/jB,KAAK,CAAC,IAAI,CAAC,EAAE,CakiBQ,EAGjD,OAAMikB,CACR,CAGA,GAAI,CAAEkQ,CAAAA,aAAelN,QAAO,EAC1B,MAAM,qBAEL,CAFK,MACJ,CAAC,4CAA4C,EAAE,IAAI,CAACsL,gBAAgB,CAAC,0FAA0F,CAAC,EAD5J,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAGFlrB,CAAAA,EAAQgtB,UAAU,CAACzH,YAAY,CAAG3Q,EAAU2Q,YAAY,CAExDvlB,EAAQgtB,UAAU,CAACqJ,gBAAgB,CAAG50B,QAAQnH,GAAG,CAAC,C,MAChDsa,CAAAA,EAAAA,EAAUmF,gBAAgB,SAA1BnF,EAA4BoF,aAAa,CACvCpF,EAAU4E,eAAe,EAAI,EAAE,KAE9BnjB,OAAOmG,MAAM,CAACoY,EAAUgF,kBAAkB,EAAI,CAAC,GACnD,EAAEmZ,OAAO,CAAC,KACL1nB,QAAQ0X,GAAG,CAACuT,wBAAwB,EACtChrB,QAAQ8kB,GAAG,CACT,4CACA9a,EAAawK,GAAG,CAGtB,GAEIpB,IACF1e,EAAQgtB,UAAU,CAACsD,aAAa,CAAG,MAAA5R,CAAAA,EAAAA,EAAesR,IAAI,SAAnBtR,EAAqBrmB,IAAI,CAAC,KAC7D2H,EAAQgtB,UAAU,CAACuJ,mBAAmB,CAAG7X,EAAeL,UAAU,CAClEre,EAAQgtB,UAAU,CAACwJ,eAAe,CAAG9X,EAAe4W,MAAM,CAC1Dt1B,EAAQgtB,UAAU,CAACyJ,cAAc,CAAG/X,EAAe6W,KAAK,EAM1D,IAAMt3B,EAAU,IAAI6U,QAAQga,EAAI7uB,OAAO,QACvC,YACEqX,EAAapN,IAAI,EACjB2L,EAAqB5V,EAASqX,EAAaxB,cAAc,EAElD,IAAI8L,SAASkN,EAAIvN,IAAI,CAAE,CAC5BnV,OAAQ0iB,EAAI1iB,MAAM,CAClByV,WAAYiN,EAAIjN,UAAU,CAC1B5hB,QAAAA,CACF,GAGK6uB,CACT,CAEA,MAAa4J,OACX5e,CAAgB,CAChB9X,CAAoC,CACjB,KpD/frB8f,EoDigBE,IAAM2M,EAAU,IAAI,CAAC1nB,OAAO,CAAC+S,EAAI6T,MAAM,EAGjCgL,EAA4C,CAEhDtC,oBAAqB,KACrBuC,KAAM,IAAI,CAAC/kB,UAAU,CAAC+kB,IAAI,CAC1B5J,WAAYhtB,EAAQgtB,UAAU,CAC9BpK,QAAS5iB,EAAQ62B,aAAa,CAACjU,OAAO,CAIxC+T,CAAAA,EAAwB3J,UAAU,CAACwD,UAAU,CAAG,IAAI,CAACre,QAAQ,CAACqe,UAAU,CAExE,IAAM9D,EAA2B,CAC/BoK,WAAY,GACZZ,SInmBGa,SA/CPjf,CAAoD,MAQhDkf,EACAC,CAEAnf,CAAAA,EAAI7Z,OAAO,YAAY6U,SACzBkkB,EAAWlf,EAAI7Z,OAAO,CAAC1D,GAAG,CAAC6X,EAAczY,WAAW,KAAO,KAC3Ds9B,EAAcnf,EAAI7Z,OAAO,CAAC1D,GAAG,CAAC,kBAE9By8B,EAAW,EAAK/4B,OAAO,CAACmU,EAAczY,WAAW,GAAG,EAAe,KACnEs9B,EAAcnf,EAAI7Z,OAAO,CAAC,eAAe,EAAI,MAG/C,IAAMi5B,EAAqBn/B,CAAAA,CACzB+f,CAAAA,SAAAA,EAAI6T,MAAM,EAAesL,sCAAAA,CAAkD,EAEvEE,EAAoBp/B,CAAAA,CACxB+f,CAAAA,SAAAA,EAAI6T,MAAM,EAAesL,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAa7a,UAAU,CAAC,sBAAqB,CAAC,EAEnEgb,EAAgBr/B,CAAAA,CACpBi/B,CAAAA,KAAa33B,IAAb23B,GACE,iBAAOA,GACPlf,SAAAA,EAAI6T,MAAM,EAOd,MAAO,CACLqL,SAAAA,EACAE,mBAAAA,EACAC,kBAAAA,EACAC,cAAAA,EACAC,eATqBt/B,CAAAA,CACrBq/B,CAAAA,GAAiBF,GAAsBC,CAAgB,CASzD,CACF,EJwmBkCrf,GInmBWuf,cAAc,EJsmBjD1K,EAAe2K,S1C9nBvBV,CAAY,CACZ9W,CAGC,CACDuU,CAA+C,EAG/C,IAAMkD,EAAoB,EAAE,CAM5B,IAAK,IAAI5d,KADWsC,EAAe2a,GAEjCjd,EAAM,CAAC,EAAElE,EAA2B,EAAEkE,EAAI,CAAC,CAC3C4d,EAAQ55B,IAAI,CAACgc,GAKf,GAAImG,EAAI3D,QAAQ,CAA6B,CAC3C,IAAMxC,EAAM,CAAC,EAAElE,EAA2B,EAAEqK,EAAI3D,QAAQ,CAAC,CAAC,CAC1Dob,EAAQ55B,IAAI,CAACgc,EACf,CAEA,OAAO4d,CACT,E0ComBM,IAAI,CAAC1lB,UAAU,CAAC+kB,IAAI,CACpB9e,EAAI0f,OAAO,CAEX,GAGIliB,GpD3hBRwK,EoD6hBIhI,EAAI0f,OAAO,CpDxhBRC,SAiBPjiB,CAA4B,CAC5BsC,CAA0B,CAC1BgV,CAA0B,CAC1BhN,CAA0B,CAC1BuV,CAAkB,CAClB1I,CAA4C,CAC5CrY,CAA8C,CAC9CojB,CAAwD,CACxD7f,CAA+C,CAC/Csb,CAA4C,CAC5CxB,CAAoE,EAEpE,SAASgG,EAAuBhkB,CAAiB,EAC3CmZ,GACFA,EAAI8K,SAAS,CAAC,aAAcjkB,EAEhC,CAEA,IAAMtH,EAMF,CAAC,EAEL,MAAO,CACLnE,KAAM,UACNsN,MAAAA,EACAmX,aAAcA,GAAgB,EAAE,CAIhC7M,IAAK,CAAE3D,SAAU2D,EAAI3D,QAAQ,CAAE8H,OAAQnE,EAAImE,MAAM,EAAI,EAAG,EACxDoR,WAAAA,EACA,IAAIp3B,SAAU,CAOZ,OANKoO,EAAMpO,OAAO,EAGhBoO,CAAAA,EAAMpO,OAAO,CAAG45B,SA1KJ55B,CAAsC,EACxD,IAAM65B,EAAUjlB,EAAejY,IAAI,CAACqD,GACpC,IAAK,IAAM7C,KAAUiX,EACnBylB,EAAQh8B,MAAM,CAACV,EAAOzB,WAAW,IAGnC,OAAOkZ,EAAeM,IAAI,CAAC2kB,EAC7B,EAmKmChgB,EAAI7Z,OAAO,GAGjCoO,EAAMpO,OAAO,EAEtB,IAAI0V,SAAU,CACZ,GAAI,CAACtH,EAAMsH,OAAO,CAAE,CAGlB,IAAMokB,EAAiB,IAAIt9B,EAAAA,cAAcA,CACvCoY,EAAejY,IAAI,CAACkd,EAAI7Z,OAAO,GAGjCwa,EAAuBX,EAAKigB,GAI5B1rB,EAAMsH,OAAO,CAAGD,EAAsBP,IAAI,CAAC4kB,EAC7C,CAEA,OAAO1rB,EAAMsH,OAAO,EAEtB,IAAIA,QAAQxb,MAA+B,CACzCkU,EAAMsH,OAAO,CAAGxb,KAClB,EACA,IAAI2b,gBAAiB,CACnB,GAAI,CAACzH,EAAMyH,cAAc,CAAE,CACzB,IAAMA,EAAiBkkB,SA3L7B/5B,CAAsC,CACtCqW,CAA6C,EAE7C,IAAMX,EAAU,IAAIlZ,EAAAA,cAAcA,CAACoY,EAAejY,IAAI,CAACqD,IACvD,OAAOmW,EAA6BC,IAAI,CAACV,EAASW,EACpD,EAuLUwD,EAAI7Z,OAAO,CACXqW,GAAoBwY,CAAAA,EAAM6K,EAAyBt4B,KAAAA,CAAQ,GAG7DoZ,EAAuBX,EAAKhE,GAE5BzH,EAAMyH,cAAc,CAAGA,CACzB,CACA,OAAOzH,EAAMyH,cAAc,EAE7B,IAAImkB,yBAA0B,CAC5B,GAAI,CAAC5rB,EAAM4rB,uBAAuB,CAAE,CAClC,IAAMA,EAA0BC,SNrDtC3jB,CAAgC,EAEhC,IAAMY,EAAiB,IAAIpC,MAAMwB,EAAiB,CAChDha,IAAIF,CAAM,CAAE0X,CAAI,CAAEQ,CAAQ,EACxB,OAAQR,GACN,IAAK,SACH,OAAO,SAAU,GAAGvW,CAAiC,EAGnD,OAFA4Z,EAA6B,oBAC7B/a,EAAOyB,MAAM,IAAIN,GACV2Z,CACT,CACF,KAAK,MACH,OAAO,SAAU,GAAG3Z,CAAmB,EAGrC,OAFA4Z,EAA6B,iBAC7B/a,EAAOvB,GAAG,IAAI0C,GACP2Z,CACT,CAEF,SACE,OAAO7C,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,GACA,OAAO4C,CACT,EM8BU,IAAI,CAACrB,cAAc,CAErBzH,CAAAA,EAAM4rB,uBAAuB,CAAGA,CAClC,CACA,OAAO5rB,EAAM4rB,uBAAuB,EAEtC,IAAIE,WAAY,CAUd,OATK9rB,EAAM8rB,SAAS,EAClB9rB,CAAAA,EAAM8rB,SAAS,CAAG,IAAIvgB,EACpBC,EACAC,EACA,IAAI,CAACnE,OAAO,CACZ,IAAI,CAACG,cAAc,GAIhBzH,EAAM8rB,SAAS,EAExBT,sBAAuBA,GAAyB,KAChDvE,aAAAA,EACAxB,yBACEA,GACA,WAAoByG,0BAA0B,CAEpD,EAvHI,SoDqhBEtgB,EpDnhBFzY,KAAAA,EACAygB,EACA,CAAC,EoDmhBC6M,EACAttB,KAAAA,EpDjhBFA,KAAAA,EoDkhBEW,EAAQq4B,iBAAiB,CAACC,OAAO,CpDhhBnC,GACAj5B,KAAAA,IoDkhBMuV,EAAY2jB,SKhmBU,CAC9B3B,KAAAA,CAAI,CACJvC,oBAAAA,CAAmB,CACnBrH,WAAAA,CAAU,CACV9H,kBAAAA,CAAiB,CACjBsT,kBAAAA,CAAiB,CACjB5V,QAAAA,CAAO,CACU,MC7EgBzrB,EDqGjC,IAAM6hB,EAAmB,CACvBsM,mBANA,CAAC0H,EAAWyL,oBAAoB,EAChC,CAACzL,EAAW0L,uBAAuB,EACnC,CAAC1L,EAAW2C,WAAW,EACvB,CAAC3C,EAAWqK,cAAc,CAI1BT,KAAAA,EACAvC,oBAAAA,EACAnW,MCxGK/mB,CAD0BA,ECoB/B+mB,EAAMvlB,KAAK,CAAC,KAAK8yB,MAAM,CAAC,CAACtP,EAAUoF,EAAS1Y,EAAOyY,IAEjD,EAKA,MnD3BGC,CAAO,CAAC,EAAE,EAAYA,EAAQzI,QAAQ,CAAC,MmDgCtCyI,MAAAA,CAAO,CAAC,EAAE,EAMZ,CAACA,SAAAA,GAAsBA,UAAAA,CAAkB,GACzC1Y,IAAUyY,EAASlpB,MAAM,CAAG,EAXrB+jB,EAgBF,EAAY,IAAGoF,EArBbpF,EAsBR,KD5COC,UAAU,CAAC,KAAOjlB,EAAO,IAAIA,EDyGvC4iB,iBAGEiT,EAAWjT,gBAAgB,EAAI,WAAoB4e,kBAAkB,CACvEC,kBAAmB5L,EAAW4L,iBAAiB,CAC/CtF,aAActG,EAAWsG,YAAY,CACrC/B,eAAgBvE,EAAW6L,UAAU,CACrCrI,WAAYxD,EAAWwD,UAAU,CACjCzY,qBAAsBiV,EAAWjV,oBAAoB,CAErD4X,YAAa3C,EAAW2C,WAAW,CAEnCzK,kBAAAA,EACAsT,kBAAAA,EACA5V,QAAAA,EACAkW,sBAAuB9L,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAY8L,qBAAqB,GAAI,CAAC,EAC7DC,YAAa/L,CAAAA,MAAAA,EAAAA,KAAAA,EAAAA,EAAY+L,WAAW,GAAI,GAExCC,aAAcC,SAWUjM,CAAgC,EAC1D,GAAM,CAAExS,UAAAA,CAAS,CAAEC,QAAAA,CAAO,CAAEye,iBAAAA,CAAgB,CAAE,CAAGlM,EACjD,OAAO,IAAIzS,EAAa,CACtBC,UAAAA,EACAC,QAAAA,EACAC,YAAawe,CACf,EACF,EAlBqClM,GACjCD,iBAAkBC,EAAWC,YAAY,CAACC,SAAS,CACnDiM,IAAKnM,EAAWmM,GAAG,EAAI,EACzB,EAKA,OAFAnM,EAAWhU,KAAK,CAAGA,EAEZA,CACT,ELiiBsC2d,GAK5BzI,EAAoB,MAAM,IAAI,CAAC7C,kBAAkB,CAACxpB,GAAG,CACzD6qB,EACA,IACE,IAAI,CAACrR,oBAAoB,CAACxZ,GAAG,CAACyT,EAAc,IAC1C,IAAI,CAACT,gBAAgB,CAAChT,GAAG,CAAC+S,EAAW,UAGnC,GAAI,IAAI,CAACuX,mBAAmB,EACtBvX,EAAU0Q,kBAAkB,CAAE,CAChC,IAAM1I,EAAM,qBAEX,CAFW,IAAIJ,GACd,yEADU,qB,MAAA,O,WAAA,G,aAAA,EAEZ,EAGA,OAFA5H,EAAU0J,uBAAuB,CAAG1B,EAAI/D,OAAO,CAC/CjE,EAAU2J,iBAAiB,CAAG3B,EAAI4B,KAAK,CACjC5B,CACR,CAKF,IAAIgQ,EAAU9U,EAGd,OAAQ,IAAI,CAACsU,OAAO,EAClB,IAAK,gBAEHxX,EAAUmJ,YAAY,CAAG,GACzB,KAEF,KAAK,eAGHnJ,EAAUoJ,WAAW,CAAG,GAGxB4O,EAAU,IAAI7Z,MAAM+E,EAAKshB,IACzB,KACF,KAAK,QAGHxkB,EAAUqJ,kBAAkB,CAAG,GAC3BrJ,EAAU0Q,kBAAkB,EAC9BsH,CAAAA,EAAU,IAAI7Z,MAAM+E,EAAKuhB,GAA4B,EACvD,KACF,SAEEzM,EAAU0M,SAuNA1M,CAAoB,CAAEhY,CAAoB,EAClE,IAAM2kB,EAAkB,CACtBh/B,IACEF,CAAiC,CACjC0X,CAAqB,CACrBQ,CAAa,EAEb,OAAQR,GACN,IAAK,SACL,IAAK,eACL,IAAK,MACL,IAAK,OACL,IAAK,SACL,IAAK,WACL,IAAK,SAGH,OADAynB,GAAa5kB,EADSyG,EAAAA,oBAAoBA,CAACvG,QAAQ,GACZ,CAAC,QAAQ,EAAE/C,EAAK,CAAC,EACjDO,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAE1C,KAAK,QACH,OACElY,CAAM,CAACo/B,GAAe,EACrBp/B,CAAAA,CAAM,CAACo/B,GAAe,CAAG,IACxB,IAAI1mB,MAAM1Y,EAAOqqB,KAAK,GAAI6U,EAAe,CAE/C,SACE,OAAOjnB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,EAEMmnB,EAAsB,CAC1Bn/B,IACEF,CAAyC,CACzC0X,CAAqB,EAErB,OAAQA,GACN,IAAK,UACH,OACE1X,CAAM,CAACs/B,GAAc,EACpBt/B,CAAAA,CAAM,CAACs/B,GAAc,CAAG,IAAI5mB,MAAM1Y,EAAOm9B,OAAO,CAAE+B,EAAe,CAEtE,KAAK,UACL,IAAK,UACL,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,cACL,IAAK,WAMH,OAJAC,GAAa5kB,EADSyG,EAAAA,oBAAoBA,CAACvG,QAAQ,GACZ,CAAC,QAAQ,EAAE/C,EAAK,CAAC,EAIjDO,EAAe/X,GAAG,CAACF,EAAQ0X,EAAM1X,EAE1C,KAAK,QACH,OACEA,CAAM,CAACu/B,GAAmB,EACzBv/B,CAAAA,CAAM,CAACu/B,GAAmB,CAAG,IAC5B,IAAI7mB,MAQF1Y,EAAOqqB,KAAK,GACZgV,EAAmB,CAG3B,SAIE,OAAOpnB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAM1X,EAC5C,CACF,CAGF,EAEA,OAAO,IAAI0Y,MAAM6Z,EAAS8M,EAC5B,EA7S2C5hB,EAAKlD,EACpC,CAGA,IAAMsJ,EAAQ2b,SQ3tBkBC,CAAoB,EAE9D,IAAIC,EAAS,QACRD,EAAa5/B,QAAQ,CAAC6/B,IACzBA,CAAAA,EAAS,SAAQ,EAEnB,GAAM,EAAG,GAAGC,EAAM,CAAGF,EAAanhC,KAAK,CAACohC,GAKxC,MADiBE,CAHIF,CAAM,CAAC,EAAE,CAAGC,EAAM3hC,IAAI,CAAC0hC,EAAM,EAGpBphC,KAAK,CAAC,KAAKK,KAAK,CAAC,EAAG,IAAIX,IAAI,CAAC,IAE7D,ER+sBsD,IAAI,CAAC6yB,gBAAgB,EAEzDgP,EAASlL,CAAAA,EAAAA,EAAAA,SAAAA,IAKf,OAFAkL,EAAOC,oBAAoB,CAAC,aAAcjc,GAEnCgc,EAAOjL,KAAK,CACjBzX,EAA0B4iB,UAAU,CACpC,CACE9K,SAAU,CAAC,0BAA0B,EAAEpR,EAAM,CAAC,CAC9C7kB,WAAY,CACV,aAAc6kB,CAChB,CACF,EACA,SACE,IAAI,CAACsO,EAAE,CACLC,EACAC,EACA9X,EACAU,EACAqX,EACAC,EACA5sB,GAGR,KAMN,GAAI,CAAEkuB,CAAAA,aAAoBtO,QAAO,EAE/B,OAAO,IAAIA,SAAS,KAAM,CAAExV,OAAQ,GAAI,GAG1C,GAAI8jB,EAASjwB,OAAO,CAACpC,GAAG,CAAC,wBACvB,MAAM,qBAEL,CAFK,MACJ,sIADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAIqyB,MAAAA,EAASjwB,OAAO,CAAC1D,GAAG,CAAC,qBAEvB,MAAM,qBAEL,CAFK,MACJ,gLADI,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,OAAO2zB,CACT,CACF,CAEA,OAAelD,GASR,SAASmB,GAAoBX,CAA0B,QAG1DA,EAAAA,EAAS6O,IAAI,IACb7O,EAAS8O,GAAG,IACZ9O,EAAS+O,MAAM,IACf/O,EAASgP,KAAK,IACdhP,EAASU,OAAO,CASpB,IAAMyN,GAAgBt+B,OAAO,WACvBu+B,GAAqBv+B,OAAO,SAC5Bo+B,GAAiBp+B,OAAO,SACxBo/B,GAAqBp/B,OAAO,gBAC5Bq/B,GAAar/B,OAAO,QACpBs/B,GAAiBt/B,OAAO,YACxBu/B,GAAgBv/B,OAAO,WACvBw/B,GAAgBx/B,OAAO,WAqBvB+9B,GAA6B,CACjC7+B,IACEF,CAAyC,CACzC0X,CAAqB,CACrBQ,CAAa,EAEb,OAAQR,GACN,IAAK,UACH,OACE1X,CAAM,CAACugC,GAAc,EACpBvgC,CAAAA,CAAM,CAACugC,GAAc,CAAG/nB,EAAeM,IAAI,CAAC,IAAIL,QAAQ,CAAC,GAAE,CAEhE,KAAK,UACH,OACEzY,CAAM,CAACwgC,GAAc,EACpBxgC,CAAAA,CAAM,CAACwgC,GAAc,CAAGnnB,EAAsBP,IAAI,CACjD,IAAI1Y,EAAAA,cAAcA,CAAC,IAAIqY,QAAQ,CAAC,IAAG,CAGzC,KAAK,UACH,OACEzY,CAAM,CAACs/B,GAAc,EACpBt/B,CAAAA,CAAM,CAACs/B,GAAc,CAAG,IAAI5mB,MAC3B1Y,EAAOm9B,OAAO,CACdsD,GAA0B,CAGhC,KAAK,MAIH,OAAOvoB,EAASilB,OAAO,CAACnT,IAAI,KACzB,MACL,IAAK,KACH,MACF,KAAK,QACH,OACEhqB,CAAM,CAACu/B,GAAmB,EACzBv/B,CAAAA,CAAM,CAACu/B,GAAmB,CAAG,IAC5B,IAAI7mB,MAQF1Y,EAAOqqB,KAAK,GACZ0U,GAA0B,CAGlC,SACE,OAAO9mB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CAGF,EAEMuoB,GAA6B,CACjCvgC,IACEF,CAAiC,CACjC0X,CAAqB,CACrBQ,CAAa,EAEb,OAAQR,GAEN,IAAK,SACH,MAAO,EACT,KAAK,eACH,OACE1X,CAAM,CAACogC,GAAmB,EACzBpgC,CAAAA,CAAM,CAACogC,GAAmB,CAAG,IAAIM,eAAgB,CAEtD,KAAK,OACH,OACE1gC,CAAM,CAACqgC,GAAW,EACjBrgC,CAAAA,CAAM,CAACqgC,GAAW,CAAGM,SSl5BPlb,CAAiB,EACxC,IAAM5gB,EAAI,IAAIyiB,IAAI7B,GAIlB,OAHA5gB,EAAEikB,IAAI,CAAG,iBACTjkB,EAAE+kB,MAAM,CAAG,GACX/kB,EAAEklB,QAAQ,CAAG,OACNllB,CACT,ET44ByC7E,EAAOgqB,IAAI,EAAEA,IAAI,CAEpD,KAAK,SACL,IAAK,WACH,OACEhqB,CAAM,CAACsgC,GAAe,EACrBtgC,CAAAA,CAAM,CAACsgC,GAAe,CAAG,IAAMpoB,EAAS8R,IAAI,CAIjD,KAAK,MAIH,MACF,KAAK,QACH,OACEhqB,CAAM,CAACo/B,GAAe,EACrBp/B,CAAAA,CAAM,CAACo/B,GAAe,CAAG,IACxB,IAAI1mB,MAAM1Y,EAAOqqB,KAAK,GAAIoW,GAA0B,CAE1D,SACE,OAAOxoB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,EA0FM8mB,GAA+B,CACnC9+B,IACEF,CAAyC,CACzC0X,CAAqB,CACrBQ,CAAa,EAEb,OAAQR,GACN,IAAK,UACH,OACE1X,CAAM,CAACs/B,GAAc,EACpBt/B,CAAAA,CAAM,CAACs/B,GAAc,CAAG,IAAI5mB,MAC3B1Y,EAAOm9B,OAAO,CACdyD,GAA4B,CAGlC,KAAK,UACL,IAAK,UACL,IAAK,MACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,OACL,IAAK,cACL,IAAK,WACH,MAAM,qBAEL,CAFK,IAAIpe,GACR,CAAC,MAAM,EAAExiB,EAAOm9B,OAAO,CAACrb,QAAQ,CAAC,sFAAsF,EAAEpK,EAAK,GAAG,CAAC,EAD9H,qB,MAAA,O,WAAA,G,aAAA,EAEN,EACF,KAAK,QACH,OACE1X,CAAM,CAACu/B,GAAmB,EACzBv/B,CAAAA,CAAM,CAACu/B,GAAmB,CAAG,IAC5B,IAAI7mB,MAQF1Y,EAAOqqB,KAAK,GACZ2U,GAA4B,CAGpC,SACE,OAAO/mB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CAGF,EAEM0oB,GAA+B,CACnC1gC,IACEF,CAAiC,CACjC0X,CAAqB,CACrBQ,CAAa,EAEb,OAAQR,GACN,IAAK,SACL,IAAK,eACL,IAAK,MACL,IAAK,OACL,IAAK,SACL,IAAK,WACL,IAAK,SACH,MAAM,qBAEL,CAFK,IAAI8K,GACR,CAAC,MAAM,EAAExiB,EAAO8hB,QAAQ,CAAC,sFAAsF,EAAEpK,EAAK,GAAG,CAAC,EADtH,qB,MAAA,O,WAAA,G,aAAA,EAEN,EACF,KAAK,QACH,OACE1X,CAAM,CAACo/B,GAAe,EACrBp/B,CAAAA,CAAM,CAACo/B,GAAe,CAAG,IACxB,IAAI1mB,MAAM1Y,EAAOqqB,KAAK,GAAIuW,GAA4B,CAE5D,SACE,OAAO3oB,EAAe/X,GAAG,CAACF,EAAQ0X,EAAMQ,EAC5C,CACF,CACF,EAEA,SAASyjB,GAAqB9X,CAAa,EACzC,OAAO,qBAEN,CAFM,IAAI1B,GACT,CAAC,MAAM,EAAE0B,EAAM,wIAAwI,CAAC,EADnJ,qB,MAAA,O,WAAA,G,aAAA,EAEP,EACF,CAEO,SAASsb,GACdxgB,CAAgB,CAChBoC,CAAwC,CACxC4B,CAAkB,EAElB,GAAI5B,EAAe,CACjB,GAAIA,UAAAA,EAAclT,IAAI,CACpB,MAAM,qBAEL,CAFK,MACJ,CAAC,MAAM,EAAE8Q,EAAMkF,KAAK,CAAC,OAAO,EAAElB,EAAW,gJAAgJ,EAAEA,EAAW,qKAAqK,CAAC,EADxW,qB,MAAA,O,WAAA,G,aAAA,EAEN,GACK,GAAI5B,mBAAAA,EAAclT,IAAI,CAC3B,MAAM,qBAEL,CAFK,MACJ,CAAC,MAAM,EAAE8Q,EAAMkF,KAAK,CAAC,OAAO,EAAElB,EAAW,iLAAiL,EAAEA,EAAW,6KAA6K,CAAC,EADjZ,qB,MAAA,O,WAAA,G,aAAA,EAEN,EAEJ,CAEA,GAAIhE,EAAMiF,kBAAkB,CAC1B,MAAM,qBAEL,CAFK,IAAIpB,GACR,CAAC,MAAM,EAAE7D,EAAMkF,KAAK,CAAC,8EAA8E,EAAElB,EAAW,4HAA4H,CAAC,EADzO,qB,MAAA,O,WAAA,G,aAAA,EAEN,GAGF,GAAI5B,GACF,GAAIA,cAAAA,EAAclT,IAAI,CAAkB,CAEtC,IAAMqC,EAAQ,qBAEb,CAFa,MACZ,CAAC,MAAM,EAAEyO,EAAMkF,KAAK,CAAC,MAAM,EAAElB,EAAW,+HAA+H,CAAC,EAD5J,qB,MAAA,O,WAAA,G,aAAA,EAEd,GACA2B,GACE3F,EAAMkF,KAAK,CACXlB,EACAzS,EACA6Q,EAEJ,MAAO,GAAIA,kBAAAA,EAAclT,IAAI,CAE3BiW,GACEnF,EAAMkF,KAAK,CACXlB,EACA5B,EAAcgD,eAAe,OAE1B,GAAIhD,qBAAAA,EAAclT,IAAI,CAAyB,CAEpDkT,EAAciD,UAAU,CAAG,EAE3B,IAAMzB,EAAM,qBAEX,CAFW,IAAIJ,GACd,CAAC,MAAM,EAAExD,EAAMkF,KAAK,CAAC,mDAAmD,EAAElB,EAAW,6EAA6E,CAAC,EADzJ,qB,MAAA,O,WAAA,G,aAAA,EAEZ,EAIA,OAHAhE,EAAMsF,uBAAuB,CAAGtB,EAChChE,EAAMuF,iBAAiB,CAAG3B,EAAI4B,KAAK,CAE7B5B,CACR,EAQJ,C", "sources": ["webpack://next/external commonjs \"next/dist/server/app-render/action-async-storage.external.js\"", "webpack://next/./dist/compiled/@edge-runtime/cookies/index.js", "webpack://next/./dist/compiled/cookie/index.js", "webpack://next/./dist/compiled/p-queue/index.js", "webpack://next/./dist/compiled/react-experimental/cjs/react.production.js", "webpack://next/./dist/compiled/react-experimental/index.js", "webpack://next/./dist/compiled/string-hash/index.js", "webpack://next/webpack/bootstrap", "webpack://next/webpack/runtime/compat get default export", "webpack://next/webpack/runtime/define property getters", "webpack://next/webpack/runtime/hasOwnProperty shorthand", "webpack://next/webpack/runtime/make namespace object", "webpack://next/./dist/src/lib/picocolors.ts", "webpack://next/./dist/src/server/route-modules/route-module.ts", "webpack://next/./dist/src/client/components/app-router-headers.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/reflect.ts", "webpack://next/./dist/src/server/web/spec-extension/adapters/headers.ts", "webpack://next/external commonjs \"next/dist/server/app-render/work-async-storage.external.js\"", "webpack://next/external commonjs \"next/dist/server/app-render/work-unit-async-storage.external.js\"", "webpack://next/./dist/src/server/web/spec-extension/adapters/request-cookies.ts", "webpack://next/./dist/src/lib/constants.ts", "webpack://next/external commonjs \"next/dist/server/lib/trace/tracer\"", "webpack://next/./dist/src/server/lib/trace/constants.ts", "webpack://next/./dist/src/server/api-utils/index.ts", "webpack://next/./dist/src/server/async-storage/draft-mode-provider.ts", "webpack://next/./dist/src/server/async-storage/request-store.ts", "webpack://next/./dist/src/server/web/utils.ts", "webpack://next/./dist/src/shared/lib/invariant-error.ts", "webpack://next/./dist/src/server/after/revalidation-utils.ts", "webpack://next/./dist/src/server/app-render/async-local-storage.ts", "webpack://next/external commonjs \"next/dist/server/app-render/after-task-async-storage.external.js\"", "webpack://next/./dist/src/server/after/after-context.ts", "webpack://next/./dist/src/shared/lib/is-thenable.ts", "webpack://next/./dist/src/shared/lib/segment.ts", "webpack://next/./dist/src/server/web/http.ts", "webpack://next/./dist/src/server/lib/implicit-tags.ts", "webpack://next/./dist/src/client/components/hooks-server-context.ts", "webpack://next/./dist/src/client/components/static-generation-bailout.ts", "webpack://next/./dist/src/server/dynamic-rendering-utils.ts", "webpack://next/./dist/src/server/app-render/dynamic-rendering.ts", "webpack://next/./dist/src/server/lib/clone-response.ts", "webpack://next/./dist/src/server/response-cache/types.ts", "webpack://next/./dist/src/shared/lib/router/utils/remove-trailing-slash.ts", "webpack://next/./dist/src/shared/lib/router/utils/parse-path.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-path-prefix.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-path-suffix.ts", "webpack://next/./dist/src/shared/lib/router/utils/path-has-prefix.ts", "webpack://next/./dist/src/server/stream-utils/encodedTags.ts", "webpack://next/./dist/src/server/stream-utils/node-web-streams-helper.ts", "webpack://next/./dist/src/server/request-meta.ts", "webpack://next/./dist/src/shared/lib/i18n/normalize-locale-path.ts", "webpack://next/./dist/src/server/web/next-url.ts", "webpack://next/./dist/src/shared/lib/router/utils/get-next-pathname-info.ts", "webpack://next/./dist/src/shared/lib/router/utils/remove-path-prefix.ts", "webpack://next/./dist/src/shared/lib/get-hostname.ts", "webpack://next/./dist/src/shared/lib/i18n/detect-domain-locale.ts", "webpack://next/./dist/src/shared/lib/router/utils/format-next-pathname-info.ts", "webpack://next/./dist/src/shared/lib/router/utils/add-locale.ts", "webpack://next/./dist/src/server/web/spec-extension/request.ts", "webpack://next/./dist/src/lib/scheduler.ts", "webpack://next/./dist/src/server/lib/patch-fetch.ts", "webpack://next/./dist/src/build/output/log.ts", "webpack://next/./dist/src/server/lib/lru-cache.ts", "webpack://next/./dist/src/server/route-modules/app-route/helpers/auto-implement-methods.ts", "webpack://next/./dist/src/client/components/http-access-fallback/http-access-fallback.ts", "webpack://next/./dist/src/client/components/redirect-status-code.ts", "webpack://next/./dist/src/client/components/redirect-error.ts", "webpack://next/./dist/src/server/app-render/prospective-render-utils.ts", "webpack://next/./dist/src/server/app-render/create-error-handler.tsx", "webpack://next/./dist/src/shared/lib/lazy-dynamic/bailout-to-csr.ts", "webpack://next/./dist/src/client/components/is-next-router-error.ts", "webpack://next/./dist/src/shared/lib/app-router-context.shared-runtime.ts", "webpack://next/./dist/src/server/app-render/cache-signal.ts", "webpack://next/./dist/src/shared/lib/utils/reflect-utils.ts", "webpack://next/./dist/src/server/create-deduped-by-callsite-server-error-logger.ts", "webpack://next/./dist/src/server/request/params.ts", "webpack://next/./dist/src/client/components/redirect.ts", "webpack://next/./dist/src/server/route-modules/app-route/module.ts", "webpack://next/./dist/src/server/route-modules/app-route/helpers/is-static-gen-enabled.ts", "webpack://next/./dist/src/server/lib/dedupe-fetch.ts", "webpack://next/./dist/src/server/route-modules/app-route/helpers/parsed-url-query-to-params.ts", "webpack://next/./dist/src/server/lib/server-action-request-meta.ts", "webpack://next/./dist/src/server/async-storage/work-store.ts", "webpack://next/./dist/src/shared/lib/page-path/ensure-leading-slash.ts", "webpack://next/./dist/src/shared/lib/router/utils/app-paths.ts", "webpack://next/./dist/src/server/route-modules/app-route/helpers/get-pathname-from-absolute-path.ts", "webpack://next/./dist/src/server/route-modules/app-route/helpers/clean-url.ts"], "sourcesContent": ["module.exports = require(\"next/dist/server/app-render/action-async-storage.external.js\");", "\"use strict\";\nvar __defProp = Object.defineProperty;\nvar __getOwnPropDesc = Object.getOwnPropertyDescriptor;\nvar __getOwnPropNames = Object.getOwnPropertyNames;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __export = (target, all) => {\n  for (var name in all)\n    __defProp(target, name, { get: all[name], enumerable: true });\n};\nvar __copyProps = (to, from, except, desc) => {\n  if (from && typeof from === \"object\" || typeof from === \"function\") {\n    for (let key of __getOwnPropNames(from))\n      if (!__hasOwnProp.call(to, key) && key !== except)\n        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });\n  }\n  return to;\n};\nvar __toCommonJS = (mod) => __copyProps(__defProp({}, \"__esModule\", { value: true }), mod);\n\n// src/index.ts\nvar src_exports = {};\n__export(src_exports, {\n  RequestCookies: () => RequestCookies,\n  ResponseCookies: () => ResponseCookies,\n  parseCookie: () => parseCookie,\n  parseSetCookie: () => parseSetCookie,\n  stringifyCookie: () => stringifyCookie\n});\nmodule.exports = __toCommonJS(src_exports);\n\n// src/serialize.ts\nfunction stringifyCookie(c) {\n  var _a;\n  const attrs = [\n    \"path\" in c && c.path && `Path=${c.path}`,\n    \"expires\" in c && (c.expires || c.expires === 0) && `Expires=${(typeof c.expires === \"number\" ? new Date(c.expires) : c.expires).toUTCString()}`,\n    \"maxAge\" in c && typeof c.maxAge === \"number\" && `Max-Age=${c.maxAge}`,\n    \"domain\" in c && c.domain && `Domain=${c.domain}`,\n    \"secure\" in c && c.secure && \"Secure\",\n    \"httpOnly\" in c && c.httpOnly && \"HttpOnly\",\n    \"sameSite\" in c && c.sameSite && `SameSite=${c.sameSite}`,\n    \"partitioned\" in c && c.partitioned && \"Partitioned\",\n    \"priority\" in c && c.priority && `Priority=${c.priority}`\n  ].filter(Boolean);\n  const stringified = `${c.name}=${encodeURIComponent((_a = c.value) != null ? _a : \"\")}`;\n  return attrs.length === 0 ? stringified : `${stringified}; ${attrs.join(\"; \")}`;\n}\nfunction parseCookie(cookie) {\n  const map = /* @__PURE__ */ new Map();\n  for (const pair of cookie.split(/; */)) {\n    if (!pair)\n      continue;\n    const splitAt = pair.indexOf(\"=\");\n    if (splitAt === -1) {\n      map.set(pair, \"true\");\n      continue;\n    }\n    const [key, value] = [pair.slice(0, splitAt), pair.slice(splitAt + 1)];\n    try {\n      map.set(key, decodeURIComponent(value != null ? value : \"true\"));\n    } catch {\n    }\n  }\n  return map;\n}\nfunction parseSetCookie(setCookie) {\n  if (!setCookie) {\n    return void 0;\n  }\n  const [[name, value], ...attributes] = parseCookie(setCookie);\n  const {\n    domain,\n    expires,\n    httponly,\n    maxage,\n    path,\n    samesite,\n    secure,\n    partitioned,\n    priority\n  } = Object.fromEntries(\n    attributes.map(([key, value2]) => [\n      key.toLowerCase().replace(/-/g, \"\"),\n      value2\n    ])\n  );\n  const cookie = {\n    name,\n    value: decodeURIComponent(value),\n    domain,\n    ...expires && { expires: new Date(expires) },\n    ...httponly && { httpOnly: true },\n    ...typeof maxage === \"string\" && { maxAge: Number(maxage) },\n    path,\n    ...samesite && { sameSite: parseSameSite(samesite) },\n    ...secure && { secure: true },\n    ...priority && { priority: parsePriority(priority) },\n    ...partitioned && { partitioned: true }\n  };\n  return compact(cookie);\n}\nfunction compact(t) {\n  const newT = {};\n  for (const key in t) {\n    if (t[key]) {\n      newT[key] = t[key];\n    }\n  }\n  return newT;\n}\nvar SAME_SITE = [\"strict\", \"lax\", \"none\"];\nfunction parseSameSite(string) {\n  string = string.toLowerCase();\n  return SAME_SITE.includes(string) ? string : void 0;\n}\nvar PRIORITY = [\"low\", \"medium\", \"high\"];\nfunction parsePriority(string) {\n  string = string.toLowerCase();\n  return PRIORITY.includes(string) ? string : void 0;\n}\nfunction splitCookiesString(cookiesString) {\n  if (!cookiesString)\n    return [];\n  var cookiesStrings = [];\n  var pos = 0;\n  var start;\n  var ch;\n  var lastComma;\n  var nextStart;\n  var cookiesSeparatorFound;\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1;\n    }\n    return pos < cookiesString.length;\n  }\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos);\n    return ch !== \"=\" && ch !== \";\" && ch !== \",\";\n  }\n  while (pos < cookiesString.length) {\n    start = pos;\n    cookiesSeparatorFound = false;\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos);\n      if (ch === \",\") {\n        lastComma = pos;\n        pos += 1;\n        skipWhitespace();\n        nextStart = pos;\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1;\n        }\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === \"=\") {\n          cookiesSeparatorFound = true;\n          pos = nextStart;\n          cookiesStrings.push(cookiesString.substring(start, lastComma));\n          start = pos;\n        } else {\n          pos = lastComma + 1;\n        }\n      } else {\n        pos += 1;\n      }\n    }\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length));\n    }\n  }\n  return cookiesStrings;\n}\n\n// src/request-cookies.ts\nvar RequestCookies = class {\n  constructor(requestHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    this._headers = requestHeaders;\n    const header = requestHeaders.get(\"cookie\");\n    if (header) {\n      const parsed = parseCookie(header);\n      for (const [name, value] of parsed) {\n        this._parsed.set(name, { name, value });\n      }\n    }\n  }\n  [Symbol.iterator]() {\n    return this._parsed[Symbol.iterator]();\n  }\n  /**\n   * The amount of cookies received from the client\n   */\n  get size() {\n    return this._parsed.size;\n  }\n  get(...args) {\n    const name = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(name);\n  }\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed);\n    if (!args.length) {\n      return all.map(([_, value]) => value);\n    }\n    const name = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter(([n]) => n === name).map(([_, value]) => value);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  set(...args) {\n    const [name, value] = args.length === 1 ? [args[0].name, args[0].value] : args;\n    const map = this._parsed;\n    map.set(name, { name, value });\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value2]) => stringifyCookie(value2)).join(\"; \")\n    );\n    return this;\n  }\n  /**\n   * Delete the cookies matching the passed name or names in the request.\n   */\n  delete(names) {\n    const map = this._parsed;\n    const result = !Array.isArray(names) ? map.delete(names) : names.map((name) => map.delete(name));\n    this._headers.set(\n      \"cookie\",\n      Array.from(map).map(([_, value]) => stringifyCookie(value)).join(\"; \")\n    );\n    return result;\n  }\n  /**\n   * Delete all the cookies in the cookies in the request.\n   */\n  clear() {\n    this.delete(Array.from(this._parsed.keys()));\n    return this;\n  }\n  /**\n   * Format the cookies in the request as a string for logging\n   */\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map((v) => `${v.name}=${encodeURIComponent(v.value)}`).join(\"; \");\n  }\n};\n\n// src/response-cookies.ts\nvar ResponseCookies = class {\n  constructor(responseHeaders) {\n    /** @internal */\n    this._parsed = /* @__PURE__ */ new Map();\n    var _a, _b, _c;\n    this._headers = responseHeaders;\n    const setCookie = (_c = (_b = (_a = responseHeaders.getSetCookie) == null ? void 0 : _a.call(responseHeaders)) != null ? _b : responseHeaders.get(\"set-cookie\")) != null ? _c : [];\n    const cookieStrings = Array.isArray(setCookie) ? setCookie : splitCookiesString(setCookie);\n    for (const cookieString of cookieStrings) {\n      const parsed = parseSetCookie(cookieString);\n      if (parsed)\n        this._parsed.set(parsed.name, parsed);\n    }\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-get CookieStore#get} without the Promise.\n   */\n  get(...args) {\n    const key = typeof args[0] === \"string\" ? args[0] : args[0].name;\n    return this._parsed.get(key);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-getAll CookieStore#getAll} without the Promise.\n   */\n  getAll(...args) {\n    var _a;\n    const all = Array.from(this._parsed.values());\n    if (!args.length) {\n      return all;\n    }\n    const key = typeof args[0] === \"string\" ? args[0] : (_a = args[0]) == null ? void 0 : _a.name;\n    return all.filter((c) => c.name === key);\n  }\n  has(name) {\n    return this._parsed.has(name);\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-set CookieStore#set} without the Promise.\n   */\n  set(...args) {\n    const [name, value, cookie] = args.length === 1 ? [args[0].name, args[0].value, args[0]] : args;\n    const map = this._parsed;\n    map.set(name, normalizeCookie({ name, value, ...cookie }));\n    replace(map, this._headers);\n    return this;\n  }\n  /**\n   * {@link https://wicg.github.io/cookie-store/#CookieStore-delete CookieStore#delete} without the Promise.\n   */\n  delete(...args) {\n    const [name, options] = typeof args[0] === \"string\" ? [args[0]] : [args[0].name, args[0]];\n    return this.set({ ...options, name, value: \"\", expires: /* @__PURE__ */ new Date(0) });\n  }\n  [Symbol.for(\"edge-runtime.inspect.custom\")]() {\n    return `ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`;\n  }\n  toString() {\n    return [...this._parsed.values()].map(stringifyCookie).join(\"; \");\n  }\n};\nfunction replace(bag, headers) {\n  headers.delete(\"set-cookie\");\n  for (const [, value] of bag) {\n    const serialized = stringifyCookie(value);\n    headers.append(\"set-cookie\", serialized);\n  }\n}\nfunction normalizeCookie(cookie = { name: \"\", value: \"\" }) {\n  if (typeof cookie.expires === \"number\") {\n    cookie.expires = new Date(cookie.expires);\n  }\n  if (cookie.maxAge) {\n    cookie.expires = new Date(Date.now() + cookie.maxAge * 1e3);\n  }\n  if (cookie.path === null || cookie.path === void 0) {\n    cookie.path = \"/\";\n  }\n  return cookie;\n}\n// Annotate the CommonJS export names for ESM import in node:\n0 && (module.exports = {\n  RequestCookies,\n  ResponseCookies,\n  parseCookie,\n  parseSetCookie,\n  stringifyCookie\n});\n", "(()=>{\"use strict\";if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var e={};(()=>{var r=e;\n/*!\n * cookie\n * Copyright(c) 2012-2014 <PERSON>\n * Copyright(c) 2015 <PERSON>\n * MIT Licensed\n */r.parse=parse;r.serialize=serialize;var i=decodeURIComponent;var t=encodeURIComponent;var a=/; */;var n=/^[\\u0009\\u0020-\\u007e\\u0080-\\u00ff]+$/;function parse(e,r){if(typeof e!==\"string\"){throw new TypeError(\"argument str must be a string\")}var t={};var n=r||{};var o=e.split(a);var s=n.decode||i;for(var p=0;p<o.length;p++){var f=o[p];var u=f.indexOf(\"=\");if(u<0){continue}var v=f.substr(0,u).trim();var c=f.substr(++u,f.length).trim();if('\"'==c[0]){c=c.slice(1,-1)}if(undefined==t[v]){t[v]=tryDecode(c,s)}}return t}function serialize(e,r,i){var a=i||{};var o=a.encode||t;if(typeof o!==\"function\"){throw new TypeError(\"option encode is invalid\")}if(!n.test(e)){throw new TypeError(\"argument name is invalid\")}var s=o(r);if(s&&!n.test(s)){throw new TypeError(\"argument val is invalid\")}var p=e+\"=\"+s;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f)){throw new TypeError(\"option maxAge is invalid\")}p+=\"; Max-Age=\"+Math.floor(f)}if(a.domain){if(!n.test(a.domain)){throw new TypeError(\"option domain is invalid\")}p+=\"; Domain=\"+a.domain}if(a.path){if(!n.test(a.path)){throw new TypeError(\"option path is invalid\")}p+=\"; Path=\"+a.path}if(a.expires){if(typeof a.expires.toUTCString!==\"function\"){throw new TypeError(\"option expires is invalid\")}p+=\"; Expires=\"+a.expires.toUTCString()}if(a.httpOnly){p+=\"; HttpOnly\"}if(a.secure){p+=\"; Secure\"}if(a.sameSite){var u=typeof a.sameSite===\"string\"?a.sameSite.toLowerCase():a.sameSite;switch(u){case true:p+=\"; SameSite=Strict\";break;case\"lax\":p+=\"; SameSite=Lax\";break;case\"strict\":p+=\"; SameSite=Strict\";break;case\"none\":p+=\"; SameSite=None\";break;default:throw new TypeError(\"option sameSite is invalid\")}}return p}function tryDecode(e,r){try{return r(e)}catch(r){return e}}})();module.exports=e})();", "(()=>{\"use strict\";var e={993:e=>{var t=Object.prototype.hasOwnProperty,n=\"~\";function Events(){}if(Object.create){Events.prototype=Object.create(null);if(!(new Events).__proto__)n=false}function EE(e,t,n){this.fn=e;this.context=t;this.once=n||false}function addListener(e,t,r,i,s){if(typeof r!==\"function\"){throw new TypeError(\"The listener must be a function\")}var o=new EE(r,i||e,s),u=n?n+t:t;if(!e._events[u])e._events[u]=o,e._eventsCount++;else if(!e._events[u].fn)e._events[u].push(o);else e._events[u]=[e._events[u],o];return e}function clearEvent(e,t){if(--e._eventsCount===0)e._events=new Events;else delete e._events[t]}function EventEmitter(){this._events=new Events;this._eventsCount=0}EventEmitter.prototype.eventNames=function eventNames(){var e=[],r,i;if(this._eventsCount===0)return e;for(i in r=this._events){if(t.call(r,i))e.push(n?i.slice(1):i)}if(Object.getOwnPropertySymbols){return e.concat(Object.getOwnPropertySymbols(r))}return e};EventEmitter.prototype.listeners=function listeners(e){var t=n?n+e:e,r=this._events[t];if(!r)return[];if(r.fn)return[r.fn];for(var i=0,s=r.length,o=new Array(s);i<s;i++){o[i]=r[i].fn}return o};EventEmitter.prototype.listenerCount=function listenerCount(e){var t=n?n+e:e,r=this._events[t];if(!r)return 0;if(r.fn)return 1;return r.length};EventEmitter.prototype.emit=function emit(e,t,r,i,s,o){var u=n?n+e:e;if(!this._events[u])return false;var a=this._events[u],l=arguments.length,c,h;if(a.fn){if(a.once)this.removeListener(e,a.fn,undefined,true);switch(l){case 1:return a.fn.call(a.context),true;case 2:return a.fn.call(a.context,t),true;case 3:return a.fn.call(a.context,t,r),true;case 4:return a.fn.call(a.context,t,r,i),true;case 5:return a.fn.call(a.context,t,r,i,s),true;case 6:return a.fn.call(a.context,t,r,i,s,o),true}for(h=1,c=new Array(l-1);h<l;h++){c[h-1]=arguments[h]}a.fn.apply(a.context,c)}else{var _=a.length,f;for(h=0;h<_;h++){if(a[h].once)this.removeListener(e,a[h].fn,undefined,true);switch(l){case 1:a[h].fn.call(a[h].context);break;case 2:a[h].fn.call(a[h].context,t);break;case 3:a[h].fn.call(a[h].context,t,r);break;case 4:a[h].fn.call(a[h].context,t,r,i);break;default:if(!c)for(f=1,c=new Array(l-1);f<l;f++){c[f-1]=arguments[f]}a[h].fn.apply(a[h].context,c)}}}return true};EventEmitter.prototype.on=function on(e,t,n){return addListener(this,e,t,n,false)};EventEmitter.prototype.once=function once(e,t,n){return addListener(this,e,t,n,true)};EventEmitter.prototype.removeListener=function removeListener(e,t,r,i){var s=n?n+e:e;if(!this._events[s])return this;if(!t){clearEvent(this,s);return this}var o=this._events[s];if(o.fn){if(o.fn===t&&(!i||o.once)&&(!r||o.context===r)){clearEvent(this,s)}}else{for(var u=0,a=[],l=o.length;u<l;u++){if(o[u].fn!==t||i&&!o[u].once||r&&o[u].context!==r){a.push(o[u])}}if(a.length)this._events[s]=a.length===1?a[0]:a;else clearEvent(this,s)}return this};EventEmitter.prototype.removeAllListeners=function removeAllListeners(e){var t;if(e){t=n?n+e:e;if(this._events[t])clearEvent(this,t)}else{this._events=new Events;this._eventsCount=0}return this};EventEmitter.prototype.off=EventEmitter.prototype.removeListener;EventEmitter.prototype.addListener=EventEmitter.prototype.on;EventEmitter.prefixed=n;EventEmitter.EventEmitter=EventEmitter;if(true){e.exports=EventEmitter}},213:e=>{e.exports=(e,t)=>{t=t||(()=>{});return e.then((e=>new Promise((e=>{e(t())})).then((()=>e))),(e=>new Promise((e=>{e(t())})).then((()=>{throw e}))))}},574:(e,t)=>{Object.defineProperty(t,\"__esModule\",{value:true});function lowerBound(e,t,n){let r=0;let i=e.length;while(i>0){const s=i/2|0;let o=r+s;if(n(e[o],t)<=0){r=++o;i-=s+1}else{i=s}}return r}t[\"default\"]=lowerBound},821:(e,t,n)=>{Object.defineProperty(t,\"__esModule\",{value:true});const r=n(574);class PriorityQueue{constructor(){this._queue=[]}enqueue(e,t){t=Object.assign({priority:0},t);const n={priority:t.priority,run:e};if(this.size&&this._queue[this.size-1].priority>=t.priority){this._queue.push(n);return}const i=r.default(this._queue,n,((e,t)=>t.priority-e.priority));this._queue.splice(i,0,n)}dequeue(){const e=this._queue.shift();return e===null||e===void 0?void 0:e.run}filter(e){return this._queue.filter((t=>t.priority===e.priority)).map((e=>e.run))}get size(){return this._queue.length}}t[\"default\"]=PriorityQueue},816:(e,t,n)=>{const r=n(213);class TimeoutError extends Error{constructor(e){super(e);this.name=\"TimeoutError\"}}const pTimeout=(e,t,n)=>new Promise(((i,s)=>{if(typeof t!==\"number\"||t<0){throw new TypeError(\"Expected `milliseconds` to be a positive number\")}if(t===Infinity){i(e);return}const o=setTimeout((()=>{if(typeof n===\"function\"){try{i(n())}catch(e){s(e)}return}const r=typeof n===\"string\"?n:`Promise timed out after ${t} milliseconds`;const o=n instanceof Error?n:new TimeoutError(r);if(typeof e.cancel===\"function\"){e.cancel()}s(o)}),t);r(e.then(i,s),(()=>{clearTimeout(o)}))}));e.exports=pTimeout;e.exports[\"default\"]=pTimeout;e.exports.TimeoutError=TimeoutError}};var t={};function __nccwpck_require__(n){var r=t[n];if(r!==undefined){return r.exports}var i=t[n]={exports:{}};var s=true;try{e[n](i,i.exports,__nccwpck_require__);s=false}finally{if(s)delete t[n]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var n={};(()=>{var e=n;Object.defineProperty(e,\"__esModule\",{value:true});const t=__nccwpck_require__(993);const r=__nccwpck_require__(816);const i=__nccwpck_require__(821);const empty=()=>{};const s=new r.TimeoutError;class PQueue extends t{constructor(e){var t,n,r,s;super();this._intervalCount=0;this._intervalEnd=0;this._pendingCount=0;this._resolveEmpty=empty;this._resolveIdle=empty;e=Object.assign({carryoverConcurrencyCount:false,intervalCap:Infinity,interval:0,concurrency:Infinity,autoStart:true,queueClass:i.default},e);if(!(typeof e.intervalCap===\"number\"&&e.intervalCap>=1)){throw new TypeError(`Expected \\`intervalCap\\` to be a number from 1 and up, got \\`${(n=(t=e.intervalCap)===null||t===void 0?void 0:t.toString())!==null&&n!==void 0?n:\"\"}\\` (${typeof e.intervalCap})`)}if(e.interval===undefined||!(Number.isFinite(e.interval)&&e.interval>=0)){throw new TypeError(`Expected \\`interval\\` to be a finite number >= 0, got \\`${(s=(r=e.interval)===null||r===void 0?void 0:r.toString())!==null&&s!==void 0?s:\"\"}\\` (${typeof e.interval})`)}this._carryoverConcurrencyCount=e.carryoverConcurrencyCount;this._isIntervalIgnored=e.intervalCap===Infinity||e.interval===0;this._intervalCap=e.intervalCap;this._interval=e.interval;this._queue=new e.queueClass;this._queueClass=e.queueClass;this.concurrency=e.concurrency;this._timeout=e.timeout;this._throwOnTimeout=e.throwOnTimeout===true;this._isPaused=e.autoStart===false}get _doesIntervalAllowAnother(){return this._isIntervalIgnored||this._intervalCount<this._intervalCap}get _doesConcurrentAllowAnother(){return this._pendingCount<this._concurrency}_next(){this._pendingCount--;this._tryToStartAnother();this.emit(\"next\")}_resolvePromises(){this._resolveEmpty();this._resolveEmpty=empty;if(this._pendingCount===0){this._resolveIdle();this._resolveIdle=empty;this.emit(\"idle\")}}_onResumeInterval(){this._onInterval();this._initializeIntervalIfNeeded();this._timeoutId=undefined}_isIntervalPaused(){const e=Date.now();if(this._intervalId===undefined){const t=this._intervalEnd-e;if(t<0){this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0}else{if(this._timeoutId===undefined){this._timeoutId=setTimeout((()=>{this._onResumeInterval()}),t)}return true}}return false}_tryToStartAnother(){if(this._queue.size===0){if(this._intervalId){clearInterval(this._intervalId)}this._intervalId=undefined;this._resolvePromises();return false}if(!this._isPaused){const e=!this._isIntervalPaused();if(this._doesIntervalAllowAnother&&this._doesConcurrentAllowAnother){const t=this._queue.dequeue();if(!t){return false}this.emit(\"active\");t();if(e){this._initializeIntervalIfNeeded()}return true}}return false}_initializeIntervalIfNeeded(){if(this._isIntervalIgnored||this._intervalId!==undefined){return}this._intervalId=setInterval((()=>{this._onInterval()}),this._interval);this._intervalEnd=Date.now()+this._interval}_onInterval(){if(this._intervalCount===0&&this._pendingCount===0&&this._intervalId){clearInterval(this._intervalId);this._intervalId=undefined}this._intervalCount=this._carryoverConcurrencyCount?this._pendingCount:0;this._processQueue()}_processQueue(){while(this._tryToStartAnother()){}}get concurrency(){return this._concurrency}set concurrency(e){if(!(typeof e===\"number\"&&e>=1)){throw new TypeError(`Expected \\`concurrency\\` to be a number from 1 and up, got \\`${e}\\` (${typeof e})`)}this._concurrency=e;this._processQueue()}async add(e,t={}){return new Promise(((n,i)=>{const run=async()=>{this._pendingCount++;this._intervalCount++;try{const o=this._timeout===undefined&&t.timeout===undefined?e():r.default(Promise.resolve(e()),t.timeout===undefined?this._timeout:t.timeout,(()=>{if(t.throwOnTimeout===undefined?this._throwOnTimeout:t.throwOnTimeout){i(s)}return undefined}));n(await o)}catch(e){i(e)}this._next()};this._queue.enqueue(run,t);this._tryToStartAnother();this.emit(\"add\")}))}async addAll(e,t){return Promise.all(e.map((async e=>this.add(e,t))))}start(){if(!this._isPaused){return this}this._isPaused=false;this._processQueue();return this}pause(){this._isPaused=true}clear(){this._queue=new this._queueClass}async onEmpty(){if(this._queue.size===0){return}return new Promise((e=>{const t=this._resolveEmpty;this._resolveEmpty=()=>{t();e()}}))}async onIdle(){if(this._pendingCount===0&&this._queue.size===0){return}return new Promise((e=>{const t=this._resolveIdle;this._resolveIdle=()=>{t();e()}}))}get size(){return this._queue.size}sizeBy(e){return this._queue.filter(e).length}get pending(){return this._pendingCount}get isPaused(){return this._isPaused}get timeout(){return this._timeout}set timeout(e){this._timeout=e}}e[\"default\"]=PQueue})();module.exports=n})();", "/**\n * @license React\n * react.production.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\nvar REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n  REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n  REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n  REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n  REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n  REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n  REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n  REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n  REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n  REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n  REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n  REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n  REACT_OFFSCREEN_TYPE = Symbol.for(\"react.offscreen\"),\n  REACT_POSTPONE_TYPE = Symbol.for(\"react.postpone\"),\n  REACT_VIEW_TRANSITION_TYPE = Symbol.for(\"react.view_transition\"),\n  MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nfunction getIteratorFn(maybeIterable) {\n  if (null === maybeIterable || \"object\" !== typeof maybeIterable) return null;\n  maybeIterable =\n    (MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL]) ||\n    maybeIterable[\"@@iterator\"];\n  return \"function\" === typeof maybeIterable ? maybeIterable : null;\n}\nvar ReactNoopUpdateQueue = {\n    isMounted: function () {\n      return !1;\n    },\n    enqueueForceUpdate: function () {},\n    enqueueReplaceState: function () {},\n    enqueueSetState: function () {}\n  },\n  assign = Object.assign,\n  emptyObject = {};\nfunction Component(props, context, updater) {\n  this.props = props;\n  this.context = context;\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\nComponent.prototype.isReactComponent = {};\nComponent.prototype.setState = function (partialState, callback) {\n  if (\n    \"object\" !== typeof partialState &&\n    \"function\" !== typeof partialState &&\n    null != partialState\n  )\n    throw Error(\n      \"takes an object of state variables to update or a function which returns an object of state variables.\"\n    );\n  this.updater.enqueueSetState(this, partialState, callback, \"setState\");\n};\nComponent.prototype.forceUpdate = function (callback) {\n  this.updater.enqueueForceUpdate(this, callback, \"forceUpdate\");\n};\nfunction ComponentDummy() {}\nComponentDummy.prototype = Component.prototype;\nfunction PureComponent(props, context, updater) {\n  this.props = props;\n  this.context = context;\n  this.refs = emptyObject;\n  this.updater = updater || ReactNoopUpdateQueue;\n}\nvar pureComponentPrototype = (PureComponent.prototype = new ComponentDummy());\npureComponentPrototype.constructor = PureComponent;\nassign(pureComponentPrototype, Component.prototype);\npureComponentPrototype.isPureReactComponent = !0;\nvar isArrayImpl = Array.isArray,\n  ReactSharedInternals = { H: null, A: null, T: null, S: null, V: null },\n  hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction ReactElement(type, key, self, source, owner, props) {\n  self = props.ref;\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key,\n    ref: void 0 !== self ? self : null,\n    props: props\n  };\n}\nfunction cloneAndReplaceKey(oldElement, newKey) {\n  return ReactElement(\n    oldElement.type,\n    newKey,\n    void 0,\n    void 0,\n    void 0,\n    oldElement.props\n  );\n}\nfunction isValidElement(object) {\n  return (\n    \"object\" === typeof object &&\n    null !== object &&\n    object.$$typeof === REACT_ELEMENT_TYPE\n  );\n}\nfunction escape(key) {\n  var escaperLookup = { \"=\": \"=0\", \":\": \"=2\" };\n  return (\n    \"$\" +\n    key.replace(/[=:]/g, function (match) {\n      return escaperLookup[match];\n    })\n  );\n}\nvar userProvidedKeyEscapeRegex = /\\/+/g;\nfunction getElementKey(element, index) {\n  return \"object\" === typeof element && null !== element && null != element.key\n    ? escape(\"\" + element.key)\n    : index.toString(36);\n}\nfunction noop$1() {}\nfunction resolveThenable(thenable) {\n  switch (thenable.status) {\n    case \"fulfilled\":\n      return thenable.value;\n    case \"rejected\":\n      throw thenable.reason;\n    default:\n      switch (\n        (\"string\" === typeof thenable.status\n          ? thenable.then(noop$1, noop$1)\n          : ((thenable.status = \"pending\"),\n            thenable.then(\n              function (fulfilledValue) {\n                \"pending\" === thenable.status &&\n                  ((thenable.status = \"fulfilled\"),\n                  (thenable.value = fulfilledValue));\n              },\n              function (error) {\n                \"pending\" === thenable.status &&\n                  ((thenable.status = \"rejected\"), (thenable.reason = error));\n              }\n            )),\n        thenable.status)\n      ) {\n        case \"fulfilled\":\n          return thenable.value;\n        case \"rejected\":\n          throw thenable.reason;\n      }\n  }\n  throw thenable;\n}\nfunction mapIntoArray(children, array, escapedPrefix, nameSoFar, callback) {\n  var type = typeof children;\n  if (\"undefined\" === type || \"boolean\" === type) children = null;\n  var invokeCallback = !1;\n  if (null === children) invokeCallback = !0;\n  else\n    switch (type) {\n      case \"bigint\":\n      case \"string\":\n      case \"number\":\n        invokeCallback = !0;\n        break;\n      case \"object\":\n        switch (children.$$typeof) {\n          case REACT_ELEMENT_TYPE:\n          case REACT_PORTAL_TYPE:\n            invokeCallback = !0;\n            break;\n          case REACT_LAZY_TYPE:\n            return (\n              (invokeCallback = children._init),\n              mapIntoArray(\n                invokeCallback(children._payload),\n                array,\n                escapedPrefix,\n                nameSoFar,\n                callback\n              )\n            );\n        }\n    }\n  if (invokeCallback)\n    return (\n      (callback = callback(children)),\n      (invokeCallback =\n        \"\" === nameSoFar ? \".\" + getElementKey(children, 0) : nameSoFar),\n      isArrayImpl(callback)\n        ? ((escapedPrefix = \"\"),\n          null != invokeCallback &&\n            (escapedPrefix =\n              invokeCallback.replace(userProvidedKeyEscapeRegex, \"$&/\") + \"/\"),\n          mapIntoArray(callback, array, escapedPrefix, \"\", function (c) {\n            return c;\n          }))\n        : null != callback &&\n          (isValidElement(callback) &&\n            (callback = cloneAndReplaceKey(\n              callback,\n              escapedPrefix +\n                (null == callback.key ||\n                (children && children.key === callback.key)\n                  ? \"\"\n                  : (\"\" + callback.key).replace(\n                      userProvidedKeyEscapeRegex,\n                      \"$&/\"\n                    ) + \"/\") +\n                invokeCallback\n            )),\n          array.push(callback)),\n      1\n    );\n  invokeCallback = 0;\n  var nextNamePrefix = \"\" === nameSoFar ? \".\" : nameSoFar + \":\";\n  if (isArrayImpl(children))\n    for (var i = 0; i < children.length; i++)\n      (nameSoFar = children[i]),\n        (type = nextNamePrefix + getElementKey(nameSoFar, i)),\n        (invokeCallback += mapIntoArray(\n          nameSoFar,\n          array,\n          escapedPrefix,\n          type,\n          callback\n        ));\n  else if (((i = getIteratorFn(children)), \"function\" === typeof i))\n    for (\n      children = i.call(children), i = 0;\n      !(nameSoFar = children.next()).done;\n\n    )\n      (nameSoFar = nameSoFar.value),\n        (type = nextNamePrefix + getElementKey(nameSoFar, i++)),\n        (invokeCallback += mapIntoArray(\n          nameSoFar,\n          array,\n          escapedPrefix,\n          type,\n          callback\n        ));\n  else if (\"object\" === type) {\n    if (\"function\" === typeof children.then)\n      return mapIntoArray(\n        resolveThenable(children),\n        array,\n        escapedPrefix,\n        nameSoFar,\n        callback\n      );\n    array = String(children);\n    throw Error(\n      \"Objects are not valid as a React child (found: \" +\n        (\"[object Object]\" === array\n          ? \"object with keys {\" + Object.keys(children).join(\", \") + \"}\"\n          : array) +\n        \"). If you meant to render a collection of children, use an array instead.\"\n    );\n  }\n  return invokeCallback;\n}\nfunction mapChildren(children, func, context) {\n  if (null == children) return children;\n  var result = [],\n    count = 0;\n  mapIntoArray(children, result, \"\", \"\", function (child) {\n    return func.call(context, child, count++);\n  });\n  return result;\n}\nfunction lazyInitializer(payload) {\n  if (-1 === payload._status) {\n    var ctor = payload._result;\n    ctor = ctor();\n    ctor.then(\n      function (moduleObject) {\n        if (0 === payload._status || -1 === payload._status)\n          (payload._status = 1), (payload._result = moduleObject);\n      },\n      function (error) {\n        if (0 === payload._status || -1 === payload._status)\n          (payload._status = 2), (payload._result = error);\n      }\n    );\n    -1 === payload._status && ((payload._status = 0), (payload._result = ctor));\n  }\n  if (1 === payload._status) return payload._result.default;\n  throw payload._result;\n}\nfunction useOptimistic(passthrough, reducer) {\n  return ReactSharedInternals.H.useOptimistic(passthrough, reducer);\n}\nvar reportGlobalError =\n  \"function\" === typeof reportError\n    ? reportError\n    : function (error) {\n        if (\n          \"object\" === typeof window &&\n          \"function\" === typeof window.ErrorEvent\n        ) {\n          var event = new window.ErrorEvent(\"error\", {\n            bubbles: !0,\n            cancelable: !0,\n            message:\n              \"object\" === typeof error &&\n              null !== error &&\n              \"string\" === typeof error.message\n                ? String(error.message)\n                : String(error),\n            error: error\n          });\n          if (!window.dispatchEvent(event)) return;\n        } else if (\n          \"object\" === typeof process &&\n          \"function\" === typeof process.emit\n        ) {\n          process.emit(\"uncaughtException\", error);\n          return;\n        }\n        console.error(error);\n      };\nfunction noop() {}\nexports.Children = {\n  map: mapChildren,\n  forEach: function (children, forEachFunc, forEachContext) {\n    mapChildren(\n      children,\n      function () {\n        forEachFunc.apply(this, arguments);\n      },\n      forEachContext\n    );\n  },\n  count: function (children) {\n    var n = 0;\n    mapChildren(children, function () {\n      n++;\n    });\n    return n;\n  },\n  toArray: function (children) {\n    return (\n      mapChildren(children, function (child) {\n        return child;\n      }) || []\n    );\n  },\n  only: function (children) {\n    if (!isValidElement(children))\n      throw Error(\n        \"React.Children.only expected to receive a single React element child.\"\n      );\n    return children;\n  }\n};\nexports.Component = Component;\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.Profiler = REACT_PROFILER_TYPE;\nexports.PureComponent = PureComponent;\nexports.StrictMode = REACT_STRICT_MODE_TYPE;\nexports.Suspense = REACT_SUSPENSE_TYPE;\nexports.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE =\n  ReactSharedInternals;\nexports.__COMPILER_RUNTIME = {\n  __proto__: null,\n  c: function (size) {\n    return ReactSharedInternals.H.useMemoCache(size);\n  }\n};\nexports.cache = function (fn) {\n  return function () {\n    return fn.apply(null, arguments);\n  };\n};\nexports.cloneElement = function (element, config, children) {\n  if (null === element || void 0 === element)\n    throw Error(\n      \"The argument must be a React element, but you passed \" + element + \".\"\n    );\n  var props = assign({}, element.props),\n    key = element.key,\n    owner = void 0;\n  if (null != config)\n    for (propName in (void 0 !== config.ref && (owner = void 0),\n    void 0 !== config.key && (key = \"\" + config.key),\n    config))\n      !hasOwnProperty.call(config, propName) ||\n        \"key\" === propName ||\n        \"__self\" === propName ||\n        \"__source\" === propName ||\n        (\"ref\" === propName && void 0 === config.ref) ||\n        (props[propName] = config[propName]);\n  var propName = arguments.length - 2;\n  if (1 === propName) props.children = children;\n  else if (1 < propName) {\n    for (var childArray = Array(propName), i = 0; i < propName; i++)\n      childArray[i] = arguments[i + 2];\n    props.children = childArray;\n  }\n  return ReactElement(element.type, key, void 0, void 0, owner, props);\n};\nexports.createContext = function (defaultValue) {\n  defaultValue = {\n    $$typeof: REACT_CONTEXT_TYPE,\n    _currentValue: defaultValue,\n    _currentValue2: defaultValue,\n    _threadCount: 0,\n    Provider: null,\n    Consumer: null\n  };\n  defaultValue.Provider = defaultValue;\n  defaultValue.Consumer = {\n    $$typeof: REACT_CONSUMER_TYPE,\n    _context: defaultValue\n  };\n  return defaultValue;\n};\nexports.createElement = function (type, config, children) {\n  var propName,\n    props = {},\n    key = null;\n  if (null != config)\n    for (propName in (void 0 !== config.key && (key = \"\" + config.key), config))\n      hasOwnProperty.call(config, propName) &&\n        \"key\" !== propName &&\n        \"__self\" !== propName &&\n        \"__source\" !== propName &&\n        (props[propName] = config[propName]);\n  var childrenLength = arguments.length - 2;\n  if (1 === childrenLength) props.children = children;\n  else if (1 < childrenLength) {\n    for (var childArray = Array(childrenLength), i = 0; i < childrenLength; i++)\n      childArray[i] = arguments[i + 2];\n    props.children = childArray;\n  }\n  if (type && type.defaultProps)\n    for (propName in ((childrenLength = type.defaultProps), childrenLength))\n      void 0 === props[propName] &&\n        (props[propName] = childrenLength[propName]);\n  return ReactElement(type, key, void 0, void 0, null, props);\n};\nexports.createRef = function () {\n  return { current: null };\n};\nexports.experimental_useEffectEvent = function (callback) {\n  return ReactSharedInternals.H.useEffectEvent(callback);\n};\nexports.experimental_useOptimistic = function (passthrough, reducer) {\n  return useOptimistic(passthrough, reducer);\n};\nexports.forwardRef = function (render) {\n  return { $$typeof: REACT_FORWARD_REF_TYPE, render: render };\n};\nexports.isValidElement = isValidElement;\nexports.lazy = function (ctor) {\n  return {\n    $$typeof: REACT_LAZY_TYPE,\n    _payload: { _status: -1, _result: ctor },\n    _init: lazyInitializer\n  };\n};\nexports.memo = function (type, compare) {\n  return {\n    $$typeof: REACT_MEMO_TYPE,\n    type: type,\n    compare: void 0 === compare ? null : compare\n  };\n};\nexports.startTransition = function (scope) {\n  var prevTransition = ReactSharedInternals.T,\n    currentTransition = {};\n  ReactSharedInternals.T = currentTransition;\n  try {\n    var returnValue = scope(),\n      onStartTransitionFinish = ReactSharedInternals.S;\n    null !== onStartTransitionFinish &&\n      onStartTransitionFinish(currentTransition, returnValue);\n    \"object\" === typeof returnValue &&\n      null !== returnValue &&\n      \"function\" === typeof returnValue.then &&\n      returnValue.then(noop, reportGlobalError);\n  } catch (error) {\n    reportGlobalError(error);\n  } finally {\n    ReactSharedInternals.T = prevTransition;\n  }\n};\nexports.unstable_Activity = REACT_OFFSCREEN_TYPE;\nexports.unstable_SuspenseList = REACT_SUSPENSE_LIST_TYPE;\nexports.unstable_ViewTransition = REACT_VIEW_TRANSITION_TYPE;\nexports.unstable_addTransitionType = function (type) {\n  var pendingTransitionTypes = ReactSharedInternals.V;\n  null === pendingTransitionTypes\n    ? (ReactSharedInternals.V = [type])\n    : -1 === pendingTransitionTypes.indexOf(type) &&\n      pendingTransitionTypes.push(type);\n};\nexports.unstable_getCacheForType = function (resourceType) {\n  var dispatcher = ReactSharedInternals.A;\n  return dispatcher ? dispatcher.getCacheForType(resourceType) : resourceType();\n};\nexports.unstable_postpone = function (reason) {\n  reason = Error(reason);\n  reason.$$typeof = REACT_POSTPONE_TYPE;\n  throw reason;\n};\nexports.unstable_useCacheRefresh = function () {\n  return ReactSharedInternals.H.useCacheRefresh();\n};\nexports.unstable_useSwipeTransition = function (previous, current, next) {\n  return ReactSharedInternals.H.useSwipeTransition(previous, current, next);\n};\nexports.use = function (usable) {\n  return ReactSharedInternals.H.use(usable);\n};\nexports.useActionState = function (action, initialState, permalink) {\n  return ReactSharedInternals.H.useActionState(action, initialState, permalink);\n};\nexports.useCallback = function (callback, deps) {\n  return ReactSharedInternals.H.useCallback(callback, deps);\n};\nexports.useContext = function (Context) {\n  return ReactSharedInternals.H.useContext(Context);\n};\nexports.useDebugValue = function () {};\nexports.useDeferredValue = function (value, initialValue) {\n  return ReactSharedInternals.H.useDeferredValue(value, initialValue);\n};\nexports.useEffect = function (create, createDeps, update) {\n  var dispatcher = ReactSharedInternals.H;\n  if (\"function\" === typeof update)\n    throw Error(\n      \"useEffect CRUD overload is not enabled in this build of React.\"\n    );\n  return dispatcher.useEffect(create, createDeps);\n};\nexports.useId = function () {\n  return ReactSharedInternals.H.useId();\n};\nexports.useImperativeHandle = function (ref, create, deps) {\n  return ReactSharedInternals.H.useImperativeHandle(ref, create, deps);\n};\nexports.useInsertionEffect = function (create, deps) {\n  return ReactSharedInternals.H.useInsertionEffect(create, deps);\n};\nexports.useLayoutEffect = function (create, deps) {\n  return ReactSharedInternals.H.useLayoutEffect(create, deps);\n};\nexports.useMemo = function (create, deps) {\n  return ReactSharedInternals.H.useMemo(create, deps);\n};\nexports.useOptimistic = useOptimistic;\nexports.useReducer = function (reducer, initialArg, init) {\n  return ReactSharedInternals.H.useReducer(reducer, initialArg, init);\n};\nexports.useRef = function (initialValue) {\n  return ReactSharedInternals.H.useRef(initialValue);\n};\nexports.useState = function (initialState) {\n  return ReactSharedInternals.H.useState(initialState);\n};\nexports.useSyncExternalStore = function (\n  subscribe,\n  getSnapshot,\n  getServerSnapshot\n) {\n  return ReactSharedInternals.H.useSyncExternalStore(\n    subscribe,\n    getSnapshot,\n    getServerSnapshot\n  );\n};\nexports.useTransition = function () {\n  return ReactSharedInternals.H.useTransition();\n};\nexports.version = \"19.1.0-experimental-029e8bd6-20250306\";\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react.production.js');\n} else {\n  module.exports = require('./cjs/react.development.js');\n}\n", "(()=>{\"use strict\";var e={328:e=>{function hash(e){var r=5381,_=e.length;while(_){r=r*33^e.charCodeAt(--_)}return r>>>0}e.exports=hash}};var r={};function __nccwpck_require__(_){var a=r[_];if(a!==undefined){return a.exports}var t=r[_]={exports:{}};var i=true;try{e[_](t,t.exports,__nccwpck_require__);i=false}finally{if(i)delete r[_]}return t.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var _=__nccwpck_require__(328);module.exports=_})();", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "// ISC License\n\n// Copyright (c) 2021 <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>\n\n// Permission to use, copy, modify, and/or distribute this software for any\n// purpose with or without fee is hereby granted, provided that the above\n// copyright notice and this permission notice appear in all copies.\n\n// THE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\n// WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\n// MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\n// ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\n// WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\n// ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF\n// OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n//\n// https://github.com/ale<PERSON><PERSON><PERSON>ov/picocolors/blob/b6261487e7b81aaab2440e397a356732cad9e342/picocolors.js#L1\n\nconst { env, stdout } = globalThis?.process ?? {}\n\nconst enabled =\n  env &&\n  !env.NO_COLOR &&\n  (env.FORCE_COLOR || (stdout?.isTTY && !env.CI && env.TERM !== 'dumb'))\n\nconst replaceClose = (\n  str: string,\n  close: string,\n  replace: string,\n  index: number\n): string => {\n  const start = str.substring(0, index) + replace\n  const end = str.substring(index + close.length)\n  const nextIndex = end.indexOf(close)\n  return ~nextIndex\n    ? start + replaceClose(end, close, replace, nextIndex)\n    : start + end\n}\n\nconst formatter = (open: string, close: string, replace = open) => {\n  if (!enabled) return String\n  return (input: string) => {\n    const string = '' + input\n    const index = string.indexOf(close, open.length)\n    return ~index\n      ? open + replaceClose(string, close, replace, index) + close\n      : open + string + close\n  }\n}\n\nexport const reset = enabled ? (s: string) => `\\x1b[0m${s}\\x1b[0m` : String\nexport const bold = formatter('\\x1b[1m', '\\x1b[22m', '\\x1b[22m\\x1b[1m')\nexport const dim = formatter('\\x1b[2m', '\\x1b[22m', '\\x1b[22m\\x1b[2m')\nexport const italic = formatter('\\x1b[3m', '\\x1b[23m')\nexport const underline = formatter('\\x1b[4m', '\\x1b[24m')\nexport const inverse = formatter('\\x1b[7m', '\\x1b[27m')\nexport const hidden = formatter('\\x1b[8m', '\\x1b[28m')\nexport const strikethrough = formatter('\\x1b[9m', '\\x1b[29m')\nexport const black = formatter('\\x1b[30m', '\\x1b[39m')\nexport const red = formatter('\\x1b[31m', '\\x1b[39m')\nexport const green = formatter('\\x1b[32m', '\\x1b[39m')\nexport const yellow = formatter('\\x1b[33m', '\\x1b[39m')\nexport const blue = formatter('\\x1b[34m', '\\x1b[39m')\nexport const magenta = formatter('\\x1b[35m', '\\x1b[39m')\nexport const purple = formatter('\\x1b[38;2;173;127;168m', '\\x1b[39m')\nexport const cyan = formatter('\\x1b[36m', '\\x1b[39m')\nexport const white = formatter('\\x1b[37m', '\\x1b[39m')\nexport const gray = formatter('\\x1b[90m', '\\x1b[39m')\nexport const bgBlack = formatter('\\x1b[40m', '\\x1b[49m')\nexport const bgRed = formatter('\\x1b[41m', '\\x1b[49m')\nexport const bgGreen = formatter('\\x1b[42m', '\\x1b[49m')\nexport const bgYellow = formatter('\\x1b[43m', '\\x1b[49m')\nexport const bgBlue = formatter('\\x1b[44m', '\\x1b[49m')\nexport const bgMagenta = formatter('\\x1b[45m', '\\x1b[49m')\nexport const bgCyan = formatter('\\x1b[46m', '\\x1b[49m')\nexport const bgWhite = formatter('\\x1b[47m', '\\x1b[49m')\n", "import type { RouteDefinition } from '../route-definitions/route-definition'\n\n/**\n * RouteModuleOptions is the options that are passed to the route module, other\n * route modules should extend this class to add specific options for their\n * route.\n */\nexport interface RouteModuleOptions<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  readonly definition: Readonly<D>\n  readonly userland: Readonly<U>\n}\n\n/**\n * RouteHandlerContext is the base context for a route handler.\n */\nexport interface RouteModuleHandleContext {\n  /**\n   * Any matched parameters for the request. This is only defined for dynamic\n   * routes.\n   */\n  params: Record<string, string | string[] | undefined> | undefined\n}\n\n/**\n * RouteModule is the base class for all route modules. This class should be\n * extended by all route modules.\n */\nexport abstract class RouteModule<\n  D extends RouteDefinition = RouteDefinition,\n  U = unknown,\n> {\n  /**\n   * The userland module. This is the module that is exported from the user's\n   * code. This is marked as readonly to ensure that the module is not mutated\n   * because the module (when compiled) only provides getters.\n   */\n  public readonly userland: Readonly<U>\n\n  /**\n   * The definition of the route.\n   */\n  public readonly definition: Readonly<D>\n\n  /**\n   * The shared modules that are exposed and required for the route module.\n   */\n  public static readonly sharedModules: any\n\n  constructor({ userland, definition }: RouteModuleOptions<D, U>) {\n    this.userland = userland\n    this.definition = definition\n  }\n}\n", "export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n", "export class ReflectAdapter {\n  static get<T extends object>(\n    target: T,\n    prop: string | symbol,\n    receiver: unknown\n  ): any {\n    const value = Reflect.get(target, prop, receiver)\n    if (typeof value === 'function') {\n      return value.bind(target)\n    }\n\n    return value\n  }\n\n  static set<T extends object>(\n    target: T,\n    prop: string | symbol,\n    value: any,\n    receiver: any\n  ): boolean {\n    return Reflect.set(target, prop, value, receiver)\n  }\n\n  static has<T extends object>(target: T, prop: string | symbol): boolean {\n    return Reflect.has(target, prop)\n  }\n\n  static deleteProperty<T extends object>(\n    target: T,\n    prop: string | symbol\n  ): boolean {\n    return Reflect.deleteProperty(target, prop)\n  }\n}\n", "import type { IncomingHttpHeaders } from 'http'\n\nimport { ReflectAdapter } from './reflect'\n\n/**\n * @internal\n */\nexport class ReadonlyHeadersError extends Error {\n  constructor() {\n    super(\n      'Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyHeadersError()\n  }\n}\n\nexport type ReadonlyHeaders = Headers & {\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  append(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  set(...args: any[]): void\n  /** @deprecated Method unavailable on `ReadonlyHeaders`. Read more: https://nextjs.org/docs/app/api-reference/functions/headers */\n  delete(...args: any[]): void\n}\nexport class HeadersAdapter extends Headers {\n  private readonly headers: IncomingHttpHeaders\n\n  constructor(headers: IncomingHttpHeaders) {\n    // We've already overridden the methods that would be called, so we're just\n    // calling the super constructor to ensure that the instanceof check works.\n    super()\n\n    this.headers = new Proxy(headers, {\n      get(target, prop, receiver) {\n        // Because this is just an object, we expect that all \"get\" operations\n        // are for properties. If it's a \"get\" for a symbol, we'll just return\n        // the symbol.\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return undefined.\n        if (typeof original === 'undefined') return\n\n        // If the original casing exists, return the value.\n        return ReflectAdapter.get(target, original, receiver)\n      },\n      set(target, prop, value, receiver) {\n        if (typeof prop === 'symbol') {\n          return ReflectAdapter.set(target, prop, value, receiver)\n        }\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, use the prop as the key.\n        return ReflectAdapter.set(target, original ?? prop, value, receiver)\n      },\n      has(target, prop) {\n        if (typeof prop === 'symbol') return ReflectAdapter.has(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return false.\n        if (typeof original === 'undefined') return false\n\n        // If the original casing exists, return true.\n        return ReflectAdapter.has(target, original)\n      },\n      deleteProperty(target, prop) {\n        if (typeof prop === 'symbol')\n          return ReflectAdapter.deleteProperty(target, prop)\n\n        const lowercased = prop.toLowerCase()\n\n        // Let's find the original casing of the key. This assumes that there is\n        // no mixed case keys (e.g. \"Content-Type\" and \"content-type\") in the\n        // headers object.\n        const original = Object.keys(headers).find(\n          (o) => o.toLowerCase() === lowercased\n        )\n\n        // If the original casing doesn't exist, return true.\n        if (typeof original === 'undefined') return true\n\n        // If the original casing exists, delete the property.\n        return ReflectAdapter.deleteProperty(target, original)\n      },\n    })\n  }\n\n  /**\n   * Seals a Headers instance to prevent modification by throwing an error when\n   * any mutating method is called.\n   */\n  public static seal(headers: Headers): ReadonlyHeaders {\n    return new Proxy<ReadonlyHeaders>(headers, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'append':\n          case 'delete':\n          case 'set':\n            return ReadonlyHeadersError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n\n  /**\n   * Merges a header value into a string. This stores multiple values as an\n   * array, so we need to merge them into a string.\n   *\n   * @param value a header value\n   * @returns a merged header value (a string)\n   */\n  private merge(value: string | string[]): string {\n    if (Array.isArray(value)) return value.join(', ')\n\n    return value\n  }\n\n  /**\n   * Creates a Headers instance from a plain object or a Headers instance.\n   *\n   * @param headers a plain object or a Headers instance\n   * @returns a headers instance\n   */\n  public static from(headers: IncomingHttpHeaders | Headers): Headers {\n    if (headers instanceof Headers) return headers\n\n    return new HeadersAdapter(headers)\n  }\n\n  public append(name: string, value: string): void {\n    const existing = this.headers[name]\n    if (typeof existing === 'string') {\n      this.headers[name] = [existing, value]\n    } else if (Array.isArray(existing)) {\n      existing.push(value)\n    } else {\n      this.headers[name] = value\n    }\n  }\n\n  public delete(name: string): void {\n    delete this.headers[name]\n  }\n\n  public get(name: string): string | null {\n    const value = this.headers[name]\n    if (typeof value !== 'undefined') return this.merge(value)\n\n    return null\n  }\n\n  public has(name: string): boolean {\n    return typeof this.headers[name] !== 'undefined'\n  }\n\n  public set(name: string, value: string): void {\n    this.headers[name] = value\n  }\n\n  public forEach(\n    callbackfn: (value: string, name: string, parent: Headers) => void,\n    thisArg?: any\n  ): void {\n    for (const [name, value] of this.entries()) {\n      callbackfn.call(thisArg, value, name, this)\n    }\n  }\n\n  public *entries(): HeadersIterator<[string, string]> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(name) as string\n\n      yield [name, value] as [string, string]\n    }\n  }\n\n  public *keys(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      const name = key.toLowerCase()\n      yield name\n    }\n  }\n\n  public *values(): HeadersIterator<string> {\n    for (const key of Object.keys(this.headers)) {\n      // We assert here that this is a string because we got it from the\n      // Object.keys() call above.\n      const value = this.get(key) as string\n\n      yield value\n    }\n  }\n\n  public [Symbol.iterator](): HeadersIterator<[string, string]> {\n    return this.entries()\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"next/dist/server/app-render/work-async-storage.external.js\");", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"next/dist/server/app-render/work-unit-async-storage.external.js\");", "import { RequestCookies } from '../cookies'\n\nimport { ResponseCookies } from '../cookies'\nimport { ReflectAdapter } from './reflect'\nimport { workAsyncStorage } from '../../../app-render/work-async-storage.external'\nimport {\n  getExpectedRequestStore,\n  type RequestStore,\n} from '../../../app-render/work-unit-async-storage.external'\n\n/**\n * @internal\n */\nexport class ReadonlyRequestCookiesError extends Error {\n  constructor() {\n    super(\n      'Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options'\n    )\n  }\n\n  public static callable() {\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\n// We use this to type some APIs but we don't construct instances directly\nexport type { ResponseCookies }\n\n// The `cookies()` API is a mix of request and response cookies. For `.get()` methods,\n// we want to return the request cookie if it exists. For mutative methods like `.set()`,\n// we want to return the response cookie.\nexport type ReadonlyRequestCookies = Omit<\n  RequestCookies,\n  'set' | 'clear' | 'delete'\n> &\n  Pick<ResponseCookies, 'set' | 'delete'>\n\nexport class RequestCookiesAdapter {\n  public static seal(cookies: RequestCookies): ReadonlyRequestCookies {\n    return new Proxy(cookies as any, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          case 'clear':\n          case 'delete':\n          case 'set':\n            return ReadonlyRequestCookiesError.callable\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n  }\n}\n\nconst SYMBOL_MODIFY_COOKIE_VALUES = Symbol.for('next.mutated.cookies')\n\nexport function getModifiedCookieValues(\n  cookies: ResponseCookies\n): ResponseCookie[] {\n  const modified: ResponseCookie[] | undefined = (cookies as unknown as any)[\n    SYMBOL_MODIFY_COOKIE_VALUES\n  ]\n  if (!modified || !Array.isArray(modified) || modified.length === 0) {\n    return []\n  }\n\n  return modified\n}\n\ntype SetCookieArgs =\n  | [key: string, value: string, cookie?: Partial<ResponseCookie>]\n  | [options: ResponseCookie]\n\nexport function appendMutableCookies(\n  headers: Headers,\n  mutableCookies: ResponseCookies\n): boolean {\n  const modifiedCookieValues = getModifiedCookieValues(mutableCookies)\n  if (modifiedCookieValues.length === 0) {\n    return false\n  }\n\n  // Return a new response that extends the response with\n  // the modified cookies as fallbacks. `res` cookies\n  // will still take precedence.\n  const resCookies = new ResponseCookies(headers)\n  const returnedCookies = resCookies.getAll()\n\n  // Set the modified cookies as fallbacks.\n  for (const cookie of modifiedCookieValues) {\n    resCookies.set(cookie)\n  }\n\n  // Set the original cookies as the final values.\n  for (const cookie of returnedCookies) {\n    resCookies.set(cookie)\n  }\n\n  return true\n}\n\ntype ResponseCookie = NonNullable<\n  ReturnType<InstanceType<typeof ResponseCookies>['get']>\n>\n\nexport class MutableRequestCookiesAdapter {\n  public static wrap(\n    cookies: RequestCookies,\n    onUpdateCookies?: (cookies: string[]) => void\n  ): ResponseCookies {\n    const responseCookies = new ResponseCookies(new Headers())\n    for (const cookie of cookies.getAll()) {\n      responseCookies.set(cookie)\n    }\n\n    let modifiedValues: ResponseCookie[] = []\n    const modifiedCookies = new Set<string>()\n    const updateResponseCookies = () => {\n      // TODO-APP: change method of getting workStore\n      const workStore = workAsyncStorage.getStore()\n      if (workStore) {\n        workStore.pathWasRevalidated = true\n      }\n\n      const allCookies = responseCookies.getAll()\n      modifiedValues = allCookies.filter((c) => modifiedCookies.has(c.name))\n      if (onUpdateCookies) {\n        const serializedCookies: string[] = []\n        for (const cookie of modifiedValues) {\n          const tempCookies = new ResponseCookies(new Headers())\n          tempCookies.set(cookie)\n          serializedCookies.push(tempCookies.toString())\n        }\n\n        onUpdateCookies(serializedCookies)\n      }\n    }\n\n    const wrappedCookies = new Proxy(responseCookies, {\n      get(target, prop, receiver) {\n        switch (prop) {\n          // A special symbol to get the modified cookie values\n          case SYMBOL_MODIFY_COOKIE_VALUES:\n            return modifiedValues\n\n          // TODO: Throw error if trying to set a cookie after the response\n          // headers have been set.\n          case 'delete':\n            return function (...args: [string] | [ResponseCookie]) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.delete(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n          case 'set':\n            return function (...args: SetCookieArgs) {\n              modifiedCookies.add(\n                typeof args[0] === 'string' ? args[0] : args[0].name\n              )\n              try {\n                target.set(...args)\n                return wrappedCookies\n              } finally {\n                updateResponseCookies()\n              }\n            }\n\n          default:\n            return ReflectAdapter.get(target, prop, receiver)\n        }\n      },\n    })\n\n    return wrappedCookies\n  }\n}\n\nexport function wrapWithMutableAccessCheck(\n  responseCookies: ResponseCookies\n): ResponseCookies {\n  const wrappedCookies = new Proxy(responseCookies, {\n    get(target, prop, receiver) {\n      switch (prop) {\n        case 'delete':\n          return function (...args: [string] | [ResponseCookie]) {\n            ensureCookiesAreStillMutable('cookies().delete')\n            target.delete(...args)\n            return wrappedCookies\n          }\n        case 'set':\n          return function (...args: SetCookieArgs) {\n            ensureCookiesAreStillMutable('cookies().set')\n            target.set(...args)\n            return wrappedCookies\n          }\n\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  })\n  return wrappedCookies\n}\n\nexport function areCookiesMutableInCurrentPhase(requestStore: RequestStore) {\n  return requestStore.phase === 'action'\n}\n\n/** Ensure that cookies() starts throwing on mutation\n * if we changed phases and can no longer mutate.\n *\n * This can happen when going:\n *   'render' -> 'after'\n *   'action' -> 'render'\n * */\nfunction ensureCookiesAreStillMutable(callingExpression: string) {\n  const requestStore = getExpectedRequestStore(callingExpression)\n  if (!areCookiesMutableInCurrentPhase(requestStore)) {\n    // TODO: maybe we can give a more precise error message based on callingExpression?\n    throw new ReadonlyRequestCookiesError()\n  }\n}\n\nexport function responseCookiesToRequestCookies(\n  responseCookies: ResponseCookies\n): RequestCookies {\n  const requestCookies = new RequestCookies(new Headers())\n  for (const cookie of responseCookies.getAll()) {\n    requestCookies.set(cookie)\n  }\n  return requestCookies\n}\n", "import type { ServerRuntime } from '../types'\n\nexport const NEXT_QUERY_PARAM_PREFIX = 'nxtP'\nexport const NEXT_INTERCEPTION_MARKER_PREFIX = 'nxtI'\n\nexport const MATCHED_PATH_HEADER = 'x-matched-path'\nexport const PRERENDER_REVALIDATE_HEADER = 'x-prerender-revalidate'\nexport const PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER =\n  'x-prerender-revalidate-if-generated'\n\nexport const RSC_PREFETCH_SUFFIX = '.prefetch.rsc'\nexport const RSC_SEGMENTS_DIR_SUFFIX = '.segments'\nexport const RSC_SEGMENT_SUFFIX = '.segment.rsc'\nexport const RSC_SUFFIX = '.rsc'\nexport const ACTION_SUFFIX = '.action'\nexport const NEXT_DATA_SUFFIX = '.json'\nexport const NEXT_META_SUFFIX = '.meta'\nexport const NEXT_BODY_SUFFIX = '.body'\n\nexport const NEXT_CACHE_TAGS_HEADER = 'x-next-cache-tags'\nexport const NEXT_CACHE_REVALIDATED_TAGS_HEADER = 'x-next-revalidated-tags'\nexport const NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER =\n  'x-next-revalidate-tag-token'\n\nexport const NEXT_RESUME_HEADER = 'next-resume'\n\n// if these change make sure we update the related\n// documentation as well\nexport const NEXT_CACHE_TAG_MAX_ITEMS = 128\nexport const NEXT_CACHE_TAG_MAX_LENGTH = 256\nexport const NEXT_CACHE_SOFT_TAG_MAX_LENGTH = 1024\nexport const NEXT_CACHE_IMPLICIT_TAG_ID = '_N_T_'\n\n// in seconds\nexport const CACHE_ONE_YEAR = 31536000\n\n// in seconds, represents revalidate=false. I.e. never revaliate.\n// We use this value since it can be represented as a V8 SMI for optimal performance.\n// It can also be serialized as JSON if it ever leaks accidentally as an actual value.\nexport const INFINITE_CACHE = 0xfffffffe\n\n// Patterns to detect middleware files\nexport const MIDDLEWARE_FILENAME = 'middleware'\nexport const MIDDLEWARE_LOCATION_REGEXP = `(?:src/)?${MIDDLEWARE_FILENAME}`\n\n// Pattern to detect instrumentation hooks file\nexport const INSTRUMENTATION_HOOK_FILENAME = 'instrumentation'\n\n// Because on Windows absolute paths in the generated code can break because of numbers, eg 1 in the path,\n// we have to use a private alias\nexport const PAGES_DIR_ALIAS = 'private-next-pages'\nexport const DOT_NEXT_ALIAS = 'private-dot-next'\nexport const ROOT_DIR_ALIAS = 'private-next-root-dir'\nexport const APP_DIR_ALIAS = 'private-next-app-dir'\nexport const RSC_MOD_REF_PROXY_ALIAS = 'private-next-rsc-mod-ref-proxy'\nexport const RSC_ACTION_VALIDATE_ALIAS = 'private-next-rsc-action-validate'\nexport const RSC_ACTION_PROXY_ALIAS = 'private-next-rsc-server-reference'\nexport const RSC_CACHE_WRAPPER_ALIAS = 'private-next-rsc-cache-wrapper'\nexport const RSC_ACTION_ENCRYPTION_ALIAS = 'private-next-rsc-action-encryption'\nexport const RSC_ACTION_CLIENT_WRAPPER_ALIAS =\n  'private-next-rsc-action-client-wrapper'\n\nexport const PUBLIC_DIR_MIDDLEWARE_CONFLICT = `You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict`\n\nexport const SSG_GET_INITIAL_PROPS_CONFLICT = `You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps`\n\nexport const SERVER_PROPS_GET_INIT_PROPS_CONFLICT = `You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.`\n\nexport const SERVER_PROPS_SSG_CONFLICT = `You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps`\n\nexport const STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR = `can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props`\n\nexport const SERVER_PROPS_EXPORT_ERROR = `pages with \\`getServerSideProps\\` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export`\n\nexport const GSP_NO_RETURNED_VALUE =\n  'Your `getStaticProps` function did not return an object. Did you forget to add a `return`?'\nexport const GSSP_NO_RETURNED_VALUE =\n  'Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?'\n\nexport const UNSTABLE_REVALIDATE_RENAME_ERROR =\n  'The `unstable_revalidate` property is available for general use.\\n' +\n  'Please use `revalidate` instead.'\n\nexport const GSSP_COMPONENT_MEMBER_ERROR = `can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member`\n\nexport const NON_STANDARD_NODE_ENV = `You are using a non-standard \"NODE_ENV\" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env`\n\nexport const SSG_FALLBACK_EXPORT_ERROR = `Pages with \\`fallback\\` enabled in \\`getStaticPaths\\` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export`\n\nexport const ESLINT_DEFAULT_DIRS = ['app', 'pages', 'components', 'lib', 'src']\n\nexport const SERVER_RUNTIME: Record<string, ServerRuntime> = {\n  edge: 'edge',\n  experimentalEdge: 'experimental-edge',\n  nodejs: 'nodejs',\n}\n\n/**\n * The names of the webpack layers. These layers are the primitives for the\n * webpack chunks.\n */\nconst WEBPACK_LAYERS_NAMES = {\n  /**\n   * The layer for the shared code between the client and server bundles.\n   */\n  shared: 'shared',\n  /**\n   * The layer for server-only runtime and picking up `react-server` export conditions.\n   * Including app router RSC pages and app router custom routes and metadata routes.\n   */\n  reactServerComponents: 'rsc',\n  /**\n   * Server Side Rendering layer for app (ssr).\n   */\n  serverSideRendering: 'ssr',\n  /**\n   * The browser client bundle layer for actions.\n   */\n  actionBrowser: 'action-browser',\n  /**\n   * The Node.js bundle layer for the API routes.\n   */\n  apiNode: 'api-node',\n  /**\n   * The Edge Lite bundle layer for the API routes.\n   */\n  apiEdge: 'api-edge',\n  /**\n   * The layer for the middleware code.\n   */\n  middleware: 'middleware',\n  /**\n   * The layer for the instrumentation hooks.\n   */\n  instrument: 'instrument',\n  /**\n   * The layer for assets on the edge.\n   */\n  edgeAsset: 'edge-asset',\n  /**\n   * The browser client bundle layer for App directory.\n   */\n  appPagesBrowser: 'app-pages-browser',\n  /**\n   * The browser client bundle layer for Pages directory.\n   */\n  pagesDirBrowser: 'pages-dir-browser',\n  /**\n   * The Edge Lite bundle layer for Pages directory.\n   */\n  pagesDirEdge: 'pages-dir-edge',\n  /**\n   * The Node.js bundle layer for Pages directory.\n   */\n  pagesDirNode: 'pages-dir-node',\n} as const\n\nexport type WebpackLayerName =\n  (typeof WEBPACK_LAYERS_NAMES)[keyof typeof WEBPACK_LAYERS_NAMES]\n\nconst WEBPACK_LAYERS = {\n  ...WEBPACK_LAYERS_NAMES,\n  GROUP: {\n    builtinReact: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n    serverOnly: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    neutralTarget: [\n      // pages api\n      WEBPACK_LAYERS_NAMES.apiNode,\n      WEBPACK_LAYERS_NAMES.apiEdge,\n    ],\n    clientOnly: [\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n    ],\n    bundled: [\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.shared,\n      WEBPACK_LAYERS_NAMES.instrument,\n      WEBPACK_LAYERS_NAMES.middleware,\n    ],\n    appPages: [\n      // app router pages and layouts\n      WEBPACK_LAYERS_NAMES.reactServerComponents,\n      WEBPACK_LAYERS_NAMES.serverSideRendering,\n      WEBPACK_LAYERS_NAMES.appPagesBrowser,\n      WEBPACK_LAYERS_NAMES.actionBrowser,\n    ],\n  },\n}\n\nconst WEBPACK_RESOURCE_QUERIES = {\n  edgeSSREntry: '__next_edge_ssr_entry__',\n  metadata: '__next_metadata__',\n  metadataRoute: '__next_metadata_route__',\n  metadataImageMeta: '__next_metadata_image_meta__',\n}\n\nexport { WEBPACK_LAYERS, WEBPACK_RESOURCE_QUERIES }\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"next/dist/server/lib/trace/tracer\");", "/**\n * Contains predefined constants for the trace span name in next/server.\n *\n * Currently, next/server/tracer is internal implementation only for tracking\n * next.js's implementation only with known span names defined here.\n **/\n\n// eslint typescript has a bug with TS enums\n/* eslint-disable no-shadow */\n\nenum BaseServerSpan {\n  handleRequest = 'BaseServer.handleRequest',\n  run = 'BaseServer.run',\n  pipe = 'BaseServer.pipe',\n  getStaticHTML = 'BaseServer.getStaticHTML',\n  render = 'BaseServer.render',\n  renderToResponseWithComponents = 'BaseServer.renderToResponseWithComponents',\n  renderToResponse = 'BaseServer.renderToResponse',\n  renderToHTML = 'BaseServer.renderToHTML',\n  renderError = 'BaseServer.renderError',\n  renderErrorToResponse = 'BaseServer.renderErrorToResponse',\n  renderErrorToHTML = 'BaseServer.renderErrorToHTML',\n  render404 = 'BaseServer.render404',\n}\n\nenum LoadComponentsSpan {\n  loadDefaultErrorComponents = 'LoadComponents.loadDefaultErrorComponents',\n  loadComponents = 'LoadComponents.loadComponents',\n}\n\nenum NextServerSpan {\n  getRequestHandler = 'NextServer.getRequestHandler',\n  getServer = 'NextServer.getServer',\n  getServerRequestHandler = 'NextServer.getServerRequestHandler',\n  createServer = 'createServer.createServer',\n}\n\nenum NextNodeServerSpan {\n  compression = 'NextNodeServer.compression',\n  getBuildId = 'NextNodeServer.getBuildId',\n  createComponentTree = 'NextNodeServer.createComponentTree',\n  clientComponentLoading = 'NextNodeServer.clientComponentLoading',\n  getLayoutOrPageModule = 'NextNodeServer.getLayoutOrPageModule',\n  generateStaticRoutes = 'NextNodeServer.generateStaticRoutes',\n  generateFsStaticRoutes = 'NextNodeServer.generateFsStaticRoutes',\n  generatePublicRoutes = 'NextNodeServer.generatePublicRoutes',\n  generateImageRoutes = 'NextNodeServer.generateImageRoutes.route',\n  sendRenderResult = 'NextNodeServer.sendRenderResult',\n  proxyRequest = 'NextNodeServer.proxyRequest',\n  runApi = 'NextNodeServer.runApi',\n  render = 'NextNodeServer.render',\n  renderHTML = 'NextNodeServer.renderHTML',\n  imageOptimizer = 'NextNodeServer.imageOptimizer',\n  getPagePath = 'NextNodeServer.getPagePath',\n  getRoutesManifest = 'NextNodeServer.getRoutesManifest',\n  findPageComponents = 'NextNodeServer.findPageComponents',\n  getFontManifest = 'NextNodeServer.getFontManifest',\n  getServerComponentManifest = 'NextNodeServer.getServerComponentManifest',\n  getRequestHandler = 'NextNodeServer.getRequestHandler',\n  renderToHTML = 'NextNodeServer.renderToHTML',\n  renderError = 'NextNodeServer.renderError',\n  renderErrorToHTML = 'NextNodeServer.renderErrorToHTML',\n  render404 = 'NextNodeServer.render404',\n  startResponse = 'NextNodeServer.startResponse',\n\n  // nested inner span, does not require parent scope name\n  route = 'route',\n  onProxyReq = 'onProxyReq',\n  apiResolver = 'apiResolver',\n  internalFetch = 'internalFetch',\n}\n\nenum StartServerSpan {\n  startServer = 'startServer.startServer',\n}\n\nenum RenderSpan {\n  getServerSideProps = 'Render.getServerSideProps',\n  getStaticProps = 'Render.getStaticProps',\n  renderToString = 'Render.renderToString',\n  renderDocument = 'Render.renderDocument',\n  createBodyResult = 'Render.createBodyResult',\n}\n\nenum AppRenderSpan {\n  renderToString = 'AppRender.renderToString',\n  renderToReadableStream = 'AppRender.renderToReadableStream',\n  getBodyResult = 'AppRender.getBodyResult',\n  fetch = 'AppRender.fetch',\n}\n\nenum RouterSpan {\n  executeRoute = 'Router.executeRoute',\n}\n\nenum NodeSpan {\n  runHandler = 'Node.runHandler',\n}\n\nenum AppRouteRouteHandlersSpan {\n  runHandler = 'AppRouteRouteHandlers.runHandler',\n}\n\nenum ResolveMetadataSpan {\n  generateMetadata = 'ResolveMetadata.generateMetadata',\n  generateViewport = 'ResolveMetadata.generateViewport',\n}\n\nenum MiddlewareSpan {\n  execute = 'Middleware.execute',\n}\n\ntype SpanTypes =\n  | `${BaseServerSpan}`\n  | `${LoadComponentsSpan}`\n  | `${NextServerSpan}`\n  | `${StartServerSpan}`\n  | `${NextNodeServerSpan}`\n  | `${RenderSpan}`\n  | `${RouterSpan}`\n  | `${AppRenderSpan}`\n  | `${NodeSpan}`\n  | `${AppRouteRouteHandlersSpan}`\n  | `${ResolveMetadataSpan}`\n  | `${MiddlewareSpan}`\n\n// This list is used to filter out spans that are not relevant to the user\nexport const NextVanillaSpanAllowlist = [\n  MiddlewareSpan.execute,\n  BaseServerSpan.handleRequest,\n  RenderSpan.getServerSideProps,\n  RenderSpan.getStaticProps,\n  AppRenderSpan.fetch,\n  AppRenderSpan.getBodyResult,\n  RenderSpan.renderDocument,\n  NodeSpan.runHandler,\n  AppRouteRouteHandlersSpan.runHandler,\n  ResolveMetadataSpan.generateMetadata,\n  ResolveMetadataSpan.generateViewport,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.getLayoutOrPageModule,\n  NextNodeServerSpan.startResponse,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\n// These Spans are allowed to be always logged\n// when the otel log prefix env is set\nexport const LogSpanAllowList = [\n  NextNodeServerSpan.findPageComponents,\n  NextNodeServerSpan.createComponentTree,\n  NextNodeServerSpan.clientComponentLoading,\n]\n\nexport {\n  BaseServerSpan,\n  LoadComponentsSpan,\n  NextServerSpan,\n  NextNodeServerSpan,\n  StartServerSpan,\n  RenderSpan,\n  RouterSpan,\n  AppRenderSpan,\n  NodeSpan,\n  AppRouteRouteHandlersSpan,\n  ResolveMetadataSpan,\n  MiddlewareSpan,\n}\n\nexport type { SpanTypes }\n", "import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { CookieSerializeOptions } from 'next/dist/compiled/cookie'\nimport type { NextApiResponse } from '../../shared/lib/utils'\n\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport {\n  PRERENDER_REVALIDATE_HEADER,\n  PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER,\n} from '../../lib/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NodeSpan } from '../lib/trace/constants'\n\nexport type NextApiRequestCookies = Partial<{ [key: string]: string }>\nexport type NextApiRequestQuery = Partial<{ [key: string]: string | string[] }>\n\nexport type __ApiPreviewProps = {\n  previewModeId: string\n  previewModeEncryptionKey: string\n  previewModeSigningKey: string\n}\n\nexport function wrapApiHandler<T extends (...args: any[]) => any>(\n  page: string,\n  handler: T\n): T {\n  return ((...args) => {\n    getTracer().setRootSpanAttribute('next.route', page)\n    // Call API route method\n    return getTracer().trace(\n      NodeSpan.runHandler,\n      {\n        spanName: `executing api route (pages) ${page}`,\n      },\n      () => handler(...args)\n    )\n  }) as T\n}\n\n/**\n *\n * @param res response object\n * @param statusCode `HTTP` status code of response\n */\nexport function sendStatusCode(\n  res: NextApiResponse,\n  statusCode: number\n): NextApiResponse<any> {\n  res.statusCode = statusCode\n  return res\n}\n\n/**\n *\n * @param res response object\n * @param [statusOrUrl] `HTTP` status code of redirect\n * @param url URL of redirect\n */\nexport function redirect(\n  res: NextApiResponse,\n  statusOrUrl: string | number,\n  url?: string\n): NextApiResponse<any> {\n  if (typeof statusOrUrl === 'string') {\n    url = statusOrUrl\n    statusOrUrl = 307\n  }\n  if (typeof statusOrUrl !== 'number' || typeof url !== 'string') {\n    throw new Error(\n      `Invalid redirect arguments. Please use a single argument URL, e.g. res.redirect('/destination') or use a status code and URL, e.g. res.redirect(307, '/destination').`\n    )\n  }\n  res.writeHead(statusOrUrl, { Location: url })\n  res.write(url)\n  res.end()\n  return res\n}\n\nexport function checkIsOnDemandRevalidate(\n  req: Request | IncomingMessage | BaseNextRequest,\n  previewProps: __ApiPreviewProps\n): {\n  isOnDemandRevalidate: boolean\n  revalidateOnlyGenerated: boolean\n} {\n  const headers = HeadersAdapter.from(req.headers)\n\n  const previewModeId = headers.get(PRERENDER_REVALIDATE_HEADER)\n  const isOnDemandRevalidate = previewModeId === previewProps.previewModeId\n\n  const revalidateOnlyGenerated = headers.has(\n    PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER\n  )\n\n  return { isOnDemandRevalidate, revalidateOnlyGenerated }\n}\n\nexport const COOKIE_NAME_PRERENDER_BYPASS = `__prerender_bypass`\nexport const COOKIE_NAME_PRERENDER_DATA = `__next_preview_data`\n\nexport const RESPONSE_LIMIT_DEFAULT = 4 * 1024 * 1024\n\nexport const SYMBOL_PREVIEW_DATA = Symbol(COOKIE_NAME_PRERENDER_DATA)\nexport const SYMBOL_CLEARED_COOKIES = Symbol(COOKIE_NAME_PRERENDER_BYPASS)\n\nexport function clearPreviewData<T>(\n  res: NextApiResponse<T>,\n  options: {\n    path?: string\n  } = {}\n): NextApiResponse<T> {\n  if (SYMBOL_CLEARED_COOKIES in res) {\n    return res\n  }\n\n  const { serialize } =\n    require('next/dist/compiled/cookie') as typeof import('cookie')\n  const previous = res.getHeader('Set-Cookie')\n  res.setHeader(`Set-Cookie`, [\n    ...(typeof previous === 'string'\n      ? [previous]\n      : Array.isArray(previous)\n        ? previous\n        : []),\n    serialize(COOKIE_NAME_PRERENDER_BYPASS, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n    serialize(COOKIE_NAME_PRERENDER_DATA, '', {\n      // To delete a cookie, set `expires` to a date in the past:\n      // https://tools.ietf.org/html/rfc6265#section-4.1.1\n      // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n      expires: new Date(0),\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      ...(options.path !== undefined\n        ? ({ path: options.path } as CookieSerializeOptions)\n        : undefined),\n    }),\n  ])\n\n  Object.defineProperty(res, SYMBOL_CLEARED_COOKIES, {\n    value: true,\n    enumerable: false,\n  })\n  return res\n}\n\n/**\n * Custom error class\n */\nexport class ApiError extends Error {\n  readonly statusCode: number\n\n  constructor(statusCode: number, message: string) {\n    super(message)\n    this.statusCode = statusCode\n  }\n}\n\n/**\n * Sends error in `response`\n * @param res response object\n * @param statusCode of response\n * @param message of response\n */\nexport function sendError(\n  res: NextApiResponse,\n  statusCode: number,\n  message: string\n): void {\n  res.statusCode = statusCode\n  res.statusMessage = message\n  res.end(message)\n}\n\ninterface LazyProps {\n  req: IncomingMessage\n}\n\n/**\n * Execute getter function only if its needed\n * @param LazyProps `req` and `params` for lazyProp\n * @param prop name of property\n * @param getter function to get data\n */\nexport function setLazyProp<T>(\n  { req }: LazyProps,\n  prop: string,\n  getter: () => T\n): void {\n  const opts = { configurable: true, enumerable: true }\n  const optsReset = { ...opts, writable: true }\n\n  Object.defineProperty(req, prop, {\n    ...opts,\n    get: () => {\n      const value = getter()\n      // we set the property on the object to avoid recalculating it\n      Object.defineProperty(req, prop, { ...optsReset, value })\n      return value\n    },\n    set: (value) => {\n      Object.defineProperty(req, prop, { ...optsReset, value })\n    },\n  })\n}\n", "import type { IncomingMessage } from 'http'\nimport type { ReadonlyRequestCookies } from '../web/spec-extension/adapters/request-cookies'\nimport type { ResponseCookies } from '../web/spec-extension/cookies'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/spec-extension/request'\n\nimport {\n  COOKIE_NAME_PRERENDER_BYPASS,\n  checkIsOnDemandRevalidate,\n} from '../api-utils'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nexport class DraftModeProvider {\n  public readonly isEnabled: boolean\n\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _previewModeId: string | undefined\n\n  /**\n   * @internal - this declaration is stripped via `tsc --stripInternal`\n   */\n  private readonly _mutableCookies: ResponseCookies\n\n  constructor(\n    previewProps: __ApiPreviewProps | undefined,\n    req: IncomingMessage | BaseNextRequest<unknown> | NextRequest,\n    cookies: ReadonlyRequestCookies,\n    mutableCookies: ResponseCookies\n  ) {\n    // The logic for draftMode() is very similar to tryGetPreviewData()\n    // but Draft Mode does not have any data associated with it.\n    const isOnDemandRevalidate =\n      previewProps &&\n      checkIsOnDemandRevalidate(req, previewProps).isOnDemandRevalidate\n\n    const cookieValue = cookies.get(COOKIE_NAME_PRERENDER_BYPASS)?.value\n\n    this.isEnabled = Boolean(\n      !isOnDemandRevalidate &&\n        cookieValue &&\n        previewProps &&\n        (cookieValue === previewProps.previewModeId ||\n          // In dev mode, the cookie can be actual hash value preview id but the preview props can still be `development-id`.\n          (process.env.NODE_ENV !== 'production' &&\n            previewProps.previewModeId === 'development-id'))\n    )\n\n    this._previewModeId = previewProps?.previewModeId\n    this._mutableCookies = mutableCookies\n  }\n\n  enable() {\n    if (!this._previewModeId) {\n      throw new Error(\n        'Invariant: previewProps missing previewModeId this should never happen'\n      )\n    }\n\n    this._mutableCookies.set({\n      name: COOKIE_NAME_PRERENDER_BYPASS,\n      value: this._previewModeId,\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n    })\n  }\n\n  disable() {\n    // To delete a cookie, set `expires` to a date in the past:\n    // https://tools.ietf.org/html/rfc6265#section-4.1.1\n    // `Max-Age: 0` is not valid, thus ignored, and the cookie is persisted.\n    this._mutableCookies.set({\n      name: COOKIE_NAME_PRERENDER_BYPASS,\n      value: '',\n      httpOnly: true,\n      sameSite: process.env.NODE_ENV !== 'development' ? 'none' : 'lax',\n      secure: process.env.NODE_ENV !== 'development',\n      path: '/',\n      expires: new Date(0),\n    })\n  }\n}\n", "import type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { RenderOpts } from '../app-render/types'\nimport type { NextRequest } from '../web/spec-extension/request'\nimport type { __ApiPreviewProps } from '../api-utils'\n\nimport { FLIGHT_HEADERS } from '../../client/components/app-router-headers'\nimport {\n  HeadersAdapter,\n  type ReadonlyHeaders,\n} from '../web/spec-extension/adapters/headers'\nimport {\n  MutableRequestCookiesAdapter,\n  RequestCookiesAdapter,\n  responseCookiesToRequestCookies,\n  wrapWithMutableAccessCheck,\n  type ReadonlyRequestCookies,\n} from '../web/spec-extension/adapters/request-cookies'\nimport { ResponseCookies, RequestCookies } from '../web/spec-extension/cookies'\nimport { DraftModeProvider } from './draft-mode-provider'\nimport { splitCookiesString } from '../web/utils'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RenderResumeDataCache } from '../resume-data-cache/resume-data-cache'\nimport type { Params } from '../request/params'\n\nfunction getHeaders(headers: Headers | IncomingHttpHeaders): ReadonlyHeaders {\n  const cleaned = HeadersAdapter.from(headers)\n  for (const header of FLIGHT_HEADERS) {\n    cleaned.delete(header.toLowerCase())\n  }\n\n  return HeadersAdapter.seal(cleaned)\n}\n\nfunction getMutableCookies(\n  headers: Headers | IncomingHttpHeaders,\n  onUpdateCookies?: (cookies: string[]) => void\n): ResponseCookies {\n  const cookies = new RequestCookies(HeadersAdapter.from(headers))\n  return MutableRequestCookiesAdapter.wrap(cookies, onUpdateCookies)\n}\n\nexport type WrapperRenderOpts = Partial<Pick<RenderOpts, 'onUpdateCookies'>> & {\n  previewProps?: __ApiPreviewProps\n}\n\ntype RequestContext = RequestResponsePair & {\n  /**\n   * The URL of the request. This only specifies the pathname and the search\n   * part of the URL. This is only undefined when generating static paths (ie,\n   * there is no request in progress, nor do we know one).\n   */\n  url: {\n    /**\n     * The pathname of the requested URL.\n     */\n    pathname: string\n\n    /**\n     * The search part of the requested URL. If the request did not provide a\n     * search part, this will be an empty string.\n     */\n    search?: string\n  }\n  phase: RequestStore['phase']\n  renderOpts?: WrapperRenderOpts\n  isHmrRefresh?: boolean\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  implicitTags?: string[] | undefined\n}\n\ntype RequestResponsePair =\n  | { req: BaseNextRequest; res: BaseNextResponse } // for an app page\n  | { req: NextRequest; res: undefined } // in an api route or middleware\n\n/**\n * If middleware set cookies in this request (indicated by `x-middleware-set-cookie`),\n * then merge those into the existing cookie object, so that when `cookies()` is accessed\n * it's able to read the newly set cookies.\n */\nfunction mergeMiddlewareCookies(\n  req: RequestContext['req'],\n  existingCookies: RequestCookies | ResponseCookies\n) {\n  if (\n    'x-middleware-set-cookie' in req.headers &&\n    typeof req.headers['x-middleware-set-cookie'] === 'string'\n  ) {\n    const setCookieValue = req.headers['x-middleware-set-cookie']\n    const responseHeaders = new Headers()\n\n    for (const cookie of splitCookiesString(setCookieValue)) {\n      responseHeaders.append('set-cookie', cookie)\n    }\n\n    const responseCookies = new ResponseCookies(responseHeaders)\n\n    // Transfer cookies from ResponseCookies to RequestCookies\n    for (const cookie of responseCookies.getAll()) {\n      existingCookies.set(cookie)\n    }\n  }\n}\n\nexport function createRequestStoreForRender(\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache'],\n  renderResumeDataCache: RenderResumeDataCache | undefined\n): RequestStore {\n  return createRequestStoreImpl(\n    // Pages start in render phase by default\n    'render',\n    req,\n    res,\n    url,\n    rootParams,\n    implicitTags,\n    onUpdateCookies,\n    renderResumeDataCache,\n    previewProps,\n    isHmrRefresh,\n    serverComponentsHmrCache\n  )\n}\n\nexport function createRequestStoreForAPI(\n  req: RequestContext['req'],\n  url: RequestContext['url'],\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  previewProps: WrapperRenderOpts['previewProps']\n): RequestStore {\n  return createRequestStoreImpl(\n    // API routes start in action phase by default\n    'action',\n    req,\n    undefined,\n    url,\n    {},\n    implicitTags,\n    onUpdateCookies,\n    undefined,\n    previewProps,\n    false,\n    undefined\n  )\n}\n\nfunction createRequestStoreImpl(\n  phase: RequestStore['phase'],\n  req: RequestContext['req'],\n  res: RequestContext['res'],\n  url: RequestContext['url'],\n  rootParams: Params,\n  implicitTags: RequestContext['implicitTags'],\n  onUpdateCookies: RenderOpts['onUpdateCookies'],\n  renderResumeDataCache: RenderResumeDataCache | undefined,\n  previewProps: WrapperRenderOpts['previewProps'],\n  isHmrRefresh: RequestContext['isHmrRefresh'],\n  serverComponentsHmrCache: RequestContext['serverComponentsHmrCache']\n): RequestStore {\n  function defaultOnUpdateCookies(cookies: string[]) {\n    if (res) {\n      res.setHeader('Set-Cookie', cookies)\n    }\n  }\n\n  const cache: {\n    headers?: ReadonlyHeaders\n    cookies?: ReadonlyRequestCookies\n    mutableCookies?: ResponseCookies\n    userspaceMutableCookies?: ResponseCookies\n    draftMode?: DraftModeProvider\n  } = {}\n\n  return {\n    type: 'request',\n    phase,\n    implicitTags: implicitTags ?? [],\n    // Rather than just using the whole `url` here, we pull the parts we want\n    // to ensure we don't use parts of the URL that we shouldn't. This also\n    // lets us avoid requiring an empty string for `search` in the type.\n    url: { pathname: url.pathname, search: url.search ?? '' },\n    rootParams,\n    get headers() {\n      if (!cache.headers) {\n        // Seal the headers object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.headers = getHeaders(req.headers)\n      }\n\n      return cache.headers\n    },\n    get cookies() {\n      if (!cache.cookies) {\n        // if middleware is setting cookie(s), then include those in\n        // the initial cached cookies so they can be read in render\n        const requestCookies = new RequestCookies(\n          HeadersAdapter.from(req.headers)\n        )\n\n        mergeMiddlewareCookies(req, requestCookies)\n\n        // Seal the cookies object that'll freeze out any methods that could\n        // mutate the underlying data.\n        cache.cookies = RequestCookiesAdapter.seal(requestCookies)\n      }\n\n      return cache.cookies\n    },\n    set cookies(value: ReadonlyRequestCookies) {\n      cache.cookies = value\n    },\n    get mutableCookies() {\n      if (!cache.mutableCookies) {\n        const mutableCookies = getMutableCookies(\n          req.headers,\n          onUpdateCookies || (res ? defaultOnUpdateCookies : undefined)\n        )\n\n        mergeMiddlewareCookies(req, mutableCookies)\n\n        cache.mutableCookies = mutableCookies\n      }\n      return cache.mutableCookies\n    },\n    get userspaceMutableCookies() {\n      if (!cache.userspaceMutableCookies) {\n        const userspaceMutableCookies = wrapWithMutableAccessCheck(\n          this.mutableCookies\n        )\n        cache.userspaceMutableCookies = userspaceMutableCookies\n      }\n      return cache.userspaceMutableCookies\n    },\n    get draftMode() {\n      if (!cache.draftMode) {\n        cache.draftMode = new DraftModeProvider(\n          previewProps,\n          req,\n          this.cookies,\n          this.mutableCookies\n        )\n      }\n\n      return cache.draftMode\n    },\n    renderResumeDataCache: renderResumeDataCache ?? null,\n    isHmrRefresh,\n    serverComponentsHmrCache:\n      serverComponentsHmrCache ||\n      (globalThis as any).__serverComponentsHmrCache,\n  }\n}\n\nexport function synchronizeMutableCookies(store: RequestStore) {\n  // TODO: does this need to update headers as well?\n  store.cookies = RequestCookiesAdapter.seal(\n    responseCookiesToRequestCookies(store.mutableCookies)\n  )\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport {\n  NEXT_INTERCEPTION_MARKER_PREFIX,\n  NEXT_QUERY_PARAM_PREFIX,\n} from '../../lib/constants'\n\n/**\n * Converts a Node.js IncomingHttpHeaders object to a Headers object. Any\n * headers with multiple values will be joined with a comma and space. Any\n * headers that have an undefined value will be ignored and others will be\n * coerced to strings.\n *\n * @param nodeHeaders the headers object to convert\n * @returns the converted headers object\n */\nexport function fromNodeOutgoingHttpHeaders(\n  nodeHeaders: OutgoingHttpHeaders\n): Headers {\n  const headers = new Headers()\n  for (let [key, value] of Object.entries(nodeHeaders)) {\n    const values = Array.isArray(value) ? value : [value]\n    for (let v of values) {\n      if (typeof v === 'undefined') continue\n      if (typeof v === 'number') {\n        v = v.toString()\n      }\n\n      headers.append(key, v)\n    }\n  }\n  return headers\n}\n\n/*\n  Set-Cookie header field-values are sometimes comma joined in one string. This splits them without choking on commas\n  that are within a single set-cookie field-value, such as in the Expires portion.\n  This is uncommon, but explicitly allowed - see https://tools.ietf.org/html/rfc2616#section-4.2\n  Node.js does this for every header *except* set-cookie - see https://github.com/nodejs/node/blob/d5e363b77ebaf1caf67cd7528224b651c86815c1/lib/_http_incoming.js#L128\n  React Native's fetch does this for *every* header, including set-cookie.\n  \n  Based on: https://github.com/google/j2objc/commit/16820fdbc8f76ca0c33472810ce0cb03d20efe25\n  Credits to: https://github.com/tomball for original and https://github.com/chrusart for JavaScript implementation\n*/\nexport function splitCookiesString(cookiesString: string) {\n  var cookiesStrings = []\n  var pos = 0\n  var start\n  var ch\n  var lastComma\n  var nextStart\n  var cookiesSeparatorFound\n\n  function skipWhitespace() {\n    while (pos < cookiesString.length && /\\s/.test(cookiesString.charAt(pos))) {\n      pos += 1\n    }\n    return pos < cookiesString.length\n  }\n\n  function notSpecialChar() {\n    ch = cookiesString.charAt(pos)\n\n    return ch !== '=' && ch !== ';' && ch !== ','\n  }\n\n  while (pos < cookiesString.length) {\n    start = pos\n    cookiesSeparatorFound = false\n\n    while (skipWhitespace()) {\n      ch = cookiesString.charAt(pos)\n      if (ch === ',') {\n        // ',' is a cookie separator if we have later first '=', not ';' or ','\n        lastComma = pos\n        pos += 1\n\n        skipWhitespace()\n        nextStart = pos\n\n        while (pos < cookiesString.length && notSpecialChar()) {\n          pos += 1\n        }\n\n        // currently special character\n        if (pos < cookiesString.length && cookiesString.charAt(pos) === '=') {\n          // we found cookies separator\n          cookiesSeparatorFound = true\n          // pos is inside the next cookie, so back up and return it.\n          pos = nextStart\n          cookiesStrings.push(cookiesString.substring(start, lastComma))\n          start = pos\n        } else {\n          // in param ',' or param separator ';',\n          // we continue from that comma\n          pos = lastComma + 1\n        }\n      } else {\n        pos += 1\n      }\n    }\n\n    if (!cookiesSeparatorFound || pos >= cookiesString.length) {\n      cookiesStrings.push(cookiesString.substring(start, cookiesString.length))\n    }\n  }\n\n  return cookiesStrings\n}\n\n/**\n * Converts a Headers object to a Node.js OutgoingHttpHeaders object. This is\n * required to support the set-cookie header, which may have multiple values.\n *\n * @param headers the headers object to convert\n * @returns the converted headers object\n */\nexport function toNodeOutgoingHttpHeaders(\n  headers: Headers\n): OutgoingHttpHeaders {\n  const nodeHeaders: OutgoingHttpHeaders = {}\n  const cookies: string[] = []\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      if (key.toLowerCase() === 'set-cookie') {\n        // We may have gotten a comma joined string of cookies, or multiple\n        // set-cookie headers. We need to merge them into one header array\n        // to represent all the cookies.\n        cookies.push(...splitCookiesString(value))\n        nodeHeaders[key] = cookies.length === 1 ? cookies[0] : cookies\n      } else {\n        nodeHeaders[key] = value\n      }\n    }\n  }\n  return nodeHeaders\n}\n\n/**\n * Validate the correctness of a user-provided URL.\n */\nexport function validateURL(url: string | URL): string {\n  try {\n    return String(new URL(String(url)))\n  } catch (error: any) {\n    throw new Error(\n      `URL is malformed \"${String(\n        url\n      )}\". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,\n      { cause: error }\n    )\n  }\n}\n\n/**\n * Normalizes `nxtP` and `nxtI` query param values to remove the prefix.\n * This function does not mutate the input key.\n */\nexport function normalizeNextQueryParam(key: string): null | string {\n  const prefixes = [NEXT_QUERY_PARAM_PREFIX, NEXT_INTERCEPTION_MARKER_PREFIX]\n  for (const prefix of prefixes) {\n    if (key !== prefix && key.startsWith(prefix)) {\n      return key.substring(prefix.length)\n    }\n  }\n  return null\n}\n", "export class InvariantError extends Error {\n  constructor(message: string, options?: ErrorOptions) {\n    super(\n      `Invariant: ${message.endsWith('.') ? message : message + '.'} This is a bug in Next.js.`,\n      options\n    )\n    this.name = 'InvariantError'\n  }\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\n\n/** Run a callback, and execute any *new* revalidations added during its runtime. */\nexport async function withExecuteRevalidates<T>(\n  store: WorkStore | undefined,\n  callback: () => Promise<T>\n): Promise<T> {\n  if (!store) {\n    return callback()\n  }\n  // If we executed any revalidates during the request, then we don't want to execute them again.\n  // save the state so we can check if anything changed after we're done running callbacks.\n  const savedRevalidationState = cloneRevalidationState(store)\n  try {\n    return await callback()\n  } finally {\n    // Check if we have any new revalidates, and if so, wait until they are all resolved.\n    const newRevalidates = diffRevalidationState(\n      savedRevalidationState,\n      cloneRevalidationState(store)\n    )\n    await executeRevalidates(store, newRevalidates)\n  }\n}\n\ntype RevalidationState = Required<\n  Pick<\n    WorkStore,\n    'revalidatedTags' | 'pendingRevalidates' | 'pendingRevalidateWrites'\n  >\n>\n\nfunction cloneRevalidationState(store: WorkStore): RevalidationState {\n  return {\n    revalidatedTags: store.revalidatedTags ? [...store.revalidatedTags] : [],\n    pendingRevalidates: { ...store.pendingRevalidates },\n    pendingRevalidateWrites: store.pendingRevalidateWrites\n      ? [...store.pendingRevalidateWrites]\n      : [],\n  }\n}\n\nfunction diffRevalidationState(\n  prev: RevalidationState,\n  curr: RevalidationState\n): RevalidationState {\n  const prevTags = new Set(prev.revalidatedTags)\n  const prevRevalidateWrites = new Set(prev.pendingRevalidateWrites)\n  return {\n    revalidatedTags: curr.revalidatedTags.filter((tag) => !prevTags.has(tag)),\n    pendingRevalidates: Object.fromEntries(\n      Object.entries(curr.pendingRevalidates).filter(\n        ([key]) => !(key in prev.pendingRevalidates)\n      )\n    ),\n    pendingRevalidateWrites: curr.pendingRevalidateWrites.filter(\n      (promise) => !prevRevalidateWrites.has(promise)\n    ),\n  }\n}\n\nasync function executeRevalidates(\n  workStore: WorkStore,\n  {\n    revalidatedTags,\n    pendingRevalidates,\n    pendingRevalidateWrites,\n  }: RevalidationState\n) {\n  return Promise.all([\n    workStore.incrementalCache?.revalidateTag(revalidatedTags),\n    ...Object.values(pendingRevalidates),\n    ...pendingRevalidateWrites,\n  ])\n}\n", "import type { AsyncLocalStorage } from 'async_hooks'\n\nconst sharedAsyncLocalStorageNotAvailableError = new Error(\n  'Invariant: AsyncLocalStorage accessed in runtime where it is not available'\n)\n\nclass FakeAsyncLocalStorage<Store extends {}>\n  implements AsyncLocalStorage<Store>\n{\n  disable(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  getStore(): Store | undefined {\n    // This fake implementation of AsyncLocalStorage always returns `undefined`.\n    return undefined\n  }\n\n  run<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  exit<R>(): R {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  enterWith(): void {\n    throw sharedAsyncLocalStorageNotAvailableError\n  }\n\n  static bind<T>(fn: T): T {\n    return fn\n  }\n}\n\nconst maybeGlobalAsyncLocalStorage =\n  typeof globalThis !== 'undefined' && (globalThis as any).AsyncLocalStorage\n\nexport function createAsyncLocalStorage<\n  Store extends {},\n>(): AsyncLocalStorage<Store> {\n  if (maybeGlobalAsyncLocalStorage) {\n    return new maybeGlobalAsyncLocalStorage()\n  }\n  return new FakeAsyncLocalStorage()\n}\n\nexport function bindSnapshot<T>(fn: T): T {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.bind(fn)\n  }\n  return FakeAsyncLocalStorage.bind(fn)\n}\n\nexport function createSnapshot(): <R, TArgs extends any[]>(\n  fn: (...args: TArgs) => R,\n  ...args: TArgs\n) => R {\n  if (maybeGlobalAsyncLocalStorage) {\n    return maybeGlobalAsyncLocalStorage.snapshot()\n  }\n  return function (fn: any, ...args: any[]) {\n    return fn(...args)\n  }\n}\n", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"next/dist/server/app-render/after-task-async-storage.external.js\");", "import PromiseQueue from 'next/dist/compiled/p-queue'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { AfterCallback, AfterTask } from './after'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport { isThenable } from '../../shared/lib/is-thenable'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { withExecuteRevalidates } from './revalidation-utils'\nimport { bindSnapshot } from '../app-render/async-local-storage'\nimport {\n  workUnitAsyncStorage,\n  type WorkUnitStore,\n} from '../app-render/work-unit-async-storage.external'\nimport { afterTaskAsyncStorage } from '../app-render/after-task-async-storage.external'\n\nexport type AfterContextOpts = {\n  waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  onClose: RequestLifecycleOpts['onClose']\n  onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n}\n\nexport class AfterContext {\n  private waitUntil: RequestLifecycleOpts['waitUntil'] | undefined\n  private onClose: RequestLifecycleOpts['onClose']\n  private onTaskError: RequestLifecycleOpts['onAfterTaskError'] | undefined\n\n  private runCallbacksOnClosePromise: Promise<void> | undefined\n  private callbackQueue: PromiseQueue\n  private workUnitStores = new Set<WorkUnitStore>()\n\n  constructor({ waitUntil, onClose, onTaskError }: AfterContextOpts) {\n    this.waitUntil = waitUntil\n    this.onClose = onClose\n    this.onTaskError = onTaskError\n\n    this.callbackQueue = new PromiseQueue()\n    this.callbackQueue.pause()\n  }\n\n  public after(task: AfterTask): void {\n    if (isThenable(task)) {\n      if (!this.waitUntil) {\n        errorWaitUntilNotAvailable()\n      }\n      this.waitUntil(\n        task.catch((error) => this.reportTaskError('promise', error))\n      )\n    } else if (typeof task === 'function') {\n      // TODO(after): implement tracing\n      this.addCallback(task)\n    } else {\n      throw new Error('`after()`: Argument must be a promise or a function')\n    }\n  }\n\n  private addCallback(callback: AfterCallback) {\n    // if something is wrong, throw synchronously, bubbling up to the `after` callsite.\n    if (!this.waitUntil) {\n      errorWaitUntilNotAvailable()\n    }\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      this.workUnitStores.add(workUnitStore)\n    }\n\n    const afterTaskStore = afterTaskAsyncStorage.getStore()\n\n    // This is used for checking if request APIs can be called inside `after`.\n    // Note that we need to check the phase in which the *topmost* `after` was called (which should be \"action\"),\n    // not the current phase (which might be \"after\" if we're in a nested after).\n    // Otherwise, we might allow `after(() => headers())`, but not `after(() => after(() => headers()))`.\n    const rootTaskSpawnPhase = afterTaskStore\n      ? afterTaskStore.rootTaskSpawnPhase // nested after\n      : workUnitStore?.phase // topmost after\n\n    // this should only happen once.\n    if (!this.runCallbacksOnClosePromise) {\n      this.runCallbacksOnClosePromise = this.runCallbacksOnClose()\n      this.waitUntil(this.runCallbacksOnClosePromise)\n    }\n\n    // Bind the callback to the current execution context (i.e. preserve all currently available ALS-es).\n    // We do this because we want all of these to be equivalent in every regard except timing:\n    //   after(() => x())\n    //   after(x())\n    //   await x()\n    const wrappedCallback = bindSnapshot(async () => {\n      try {\n        await afterTaskAsyncStorage.run({ rootTaskSpawnPhase }, () =>\n          callback()\n        )\n      } catch (error) {\n        this.reportTaskError('function', error)\n      }\n    })\n\n    this.callbackQueue.add(wrappedCallback)\n  }\n\n  private async runCallbacksOnClose() {\n    await new Promise<void>((resolve) => this.onClose!(resolve))\n    return this.runCallbacks()\n  }\n\n  private async runCallbacks(): Promise<void> {\n    if (this.callbackQueue.size === 0) return\n\n    for (const workUnitStore of this.workUnitStores) {\n      workUnitStore.phase = 'after'\n    }\n\n    const workStore = workAsyncStorage.getStore()\n    if (!workStore) {\n      throw new InvariantError('Missing workStore in AfterContext.runCallbacks')\n    }\n\n    return withExecuteRevalidates(workStore, () => {\n      this.callbackQueue.start()\n      return this.callbackQueue.onIdle()\n    })\n  }\n\n  private reportTaskError(taskKind: 'promise' | 'function', error: unknown) {\n    // TODO(after): this is fine for now, but will need better intergration with our error reporting.\n    // TODO(after): should we log this if we have a onTaskError callback?\n    console.error(\n      taskKind === 'promise'\n        ? `A promise passed to \\`after()\\` rejected:`\n        : `An error occurred in a function passed to \\`after()\\`:`,\n      error\n    )\n    if (this.onTaskError) {\n      // this is very defensive, but we really don't want anything to blow up in an error handler\n      try {\n        this.onTaskError?.(error)\n      } catch (handlerError) {\n        console.error(\n          new InvariantError(\n            '`onTaskError` threw while handling an error thrown from an `after` task',\n            {\n              cause: handlerError,\n            }\n          )\n        )\n      }\n    }\n  }\n}\n\nfunction errorWaitUntilNotAvailable(): never {\n  throw new Error(\n    '`after()` will not work correctly, because `waitUntil` is not available in the current environment.'\n  )\n}\n", "/**\n * Check to see if a value is Thenable.\n *\n * @param promise the maybe-thenable value\n * @returns true if the value is thenable\n */\nexport function isThenable<T = unknown>(\n  promise: Promise<T> | T\n): promise is Promise<T> {\n  return (\n    promise !== null &&\n    typeof promise === 'object' &&\n    'then' in promise &&\n    typeof promise.then === 'function'\n  )\n}\n", "import type { Segment } from '../../server/app-render/types'\n\nexport function isGroupSegment(segment: string) {\n  // Use array[0] for performant purpose\n  return segment[0] === '(' && segment.endsWith(')')\n}\n\nexport function isParallelRouteSegment(segment: string) {\n  return segment.startsWith('@') && segment !== '@children'\n}\n\nexport function addSearchParamsIfPageSegment(\n  segment: Segment,\n  searchParams: Record<string, string | string[] | undefined>\n) {\n  const isPageSegment = segment.includes(PAGE_SEGMENT_KEY)\n\n  if (isPageSegment) {\n    const stringifiedQuery = JSON.stringify(searchParams)\n    return stringifiedQuery !== '{}'\n      ? PAGE_SEGMENT_KEY + '?' + stringifiedQuery\n      : PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n\nexport const PAGE_SEGMENT_KEY = '__PAGE__'\nexport const DEFAULT_SEGMENT_KEY = '__DEFAULT__'\n", "/**\n * List of valid HTTP methods that can be implemented by Next.js's Custom App\n * Routes.\n */\nexport const HTTP_METHODS = [\n  'GET',\n  'HEAD',\n  'OPTIONS',\n  'POST',\n  'PUT',\n  'DELETE',\n  'PATCH',\n] as const\n\n/**\n * A type representing the valid HTTP methods that can be implemented by\n * Next.js's Custom App Routes.\n */\nexport type HTTP_METHOD = (typeof HTTP_METHODS)[number]\n\n/**\n * Checks to see if the passed string is an HTTP method. Note that this is case\n * sensitive.\n *\n * @param maybeMethod the string that may be an HTTP method\n * @returns true if the string is an HTTP method\n */\nexport function isHTTPMethod(maybeMethod: string): maybeMethod is HTTP_METHOD {\n  return HTTP_METHODS.includes(maybeMethod as HTTP_METHOD)\n}\n", "import { NEXT_CACHE_IMPLICIT_TAG_ID } from '../../lib/constants'\nimport type { FallbackRouteParams } from '../request/fallback-params'\n\nconst getDerivedTags = (pathname: string): string[] => {\n  const derivedTags: string[] = [`/layout`]\n\n  // we automatically add the current path segments as tags\n  // for revalidatePath handling\n  if (pathname.startsWith('/')) {\n    const pathnameParts = pathname.split('/')\n\n    for (let i = 1; i < pathnameParts.length + 1; i++) {\n      let curPathname = pathnameParts.slice(0, i).join('/')\n\n      if (curPathname) {\n        // all derived tags other than the page are layout tags\n        if (!curPathname.endsWith('/page') && !curPathname.endsWith('/route')) {\n          curPathname = `${curPathname}${\n            !curPathname.endsWith('/') ? '/' : ''\n          }layout`\n        }\n        derivedTags.push(curPathname)\n      }\n    }\n  }\n  return derivedTags\n}\n\nexport function getImplicitTags(\n  page: string,\n  url: {\n    pathname: string\n    search?: string\n  },\n  fallbackRouteParams: null | FallbackRouteParams\n) {\n  // TODO: Cache the result\n  const newTags: string[] = []\n  const hasFallbackRouteParams =\n    fallbackRouteParams && fallbackRouteParams.size > 0\n\n  // Add the derived tags from the page.\n  const derivedTags = getDerivedTags(page)\n  for (let tag of derivedTags) {\n    tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${tag}`\n    newTags.push(tag)\n  }\n\n  // Add the tags from the pathname. If the route has unknown params, we don't\n  // want to add the pathname as a tag, as it will be invalid.\n  if (url.pathname && !hasFallbackRouteParams) {\n    const tag = `${NEXT_CACHE_IMPLICIT_TAG_ID}${url.pathname}`\n    newTags.push(tag)\n  }\n\n  return newTags\n}\n", "const DYNAMIC_ERROR_CODE = 'DYNAMIC_SERVER_USAGE'\n\nexport class DynamicServerError extends Error {\n  digest: typeof DYNAMIC_ERROR_CODE = DYNAMIC_ERROR_CODE\n\n  constructor(public readonly description: string) {\n    super(`Dynamic server usage: ${description}`)\n  }\n}\n\nexport function isDynamicServerError(err: unknown): err is DynamicServerError {\n  if (\n    typeof err !== 'object' ||\n    err === null ||\n    !('digest' in err) ||\n    typeof err.digest !== 'string'\n  ) {\n    return false\n  }\n\n  return err.digest === DYNAMIC_ERROR_CODE\n}\n", "const NEXT_STATIC_GEN_BAILOUT = 'NEXT_STATIC_GEN_BAILOUT'\n\nexport class StaticGenBailoutError extends Error {\n  public readonly code = NEXT_STATIC_GEN_BAILOUT\n}\n\nexport function isStaticGenBailoutError(\n  error: unknown\n): error is StaticGenBailoutError {\n  if (typeof error !== 'object' || error === null || !('code' in error)) {\n    return false\n  }\n\n  return error.code === NEXT_STATIC_GEN_BAILOUT\n}\n", "export function isHangingPromiseRejectionError(\n  err: unknown\n): err is HangingPromiseRejectionError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === HANGING_PROMISE_REJECTION\n}\n\nconst HANGING_PROMISE_REJECTION = 'HANGING_PROMISE_REJECTION'\n\nclass HangingPromiseRejectionError extends Error {\n  public readonly digest = HANGING_PROMISE_REJECTION\n\n  constructor(public readonly expression: string) {\n    super(\n      `During prerendering, ${expression} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${expression} to a different context by using \\`setTimeout\\`, \\`after\\`, or similar functions you may observe this error and you should handle it in that context.`\n    )\n  }\n}\n\n/**\n * This function constructs a promise that will never resolve. This is primarily\n * useful for dynamicIO where we use promise resolution timing to determine which\n * parts of a render can be included in a prerender.\n *\n * @internal\n */\nexport function makeHangingPromise<T>(\n  signal: AbortSignal,\n  expression: string\n): Promise<T> {\n  const hangingPromise = new Promise<T>((_, reject) => {\n    signal.addEventListener(\n      'abort',\n      () => {\n        reject(new HangingPromiseRejectionError(expression))\n      },\n      { once: true }\n    )\n  })\n  // We are fine if no one actually awaits this promise. We shouldn't consider this an unhandled rejection so\n  // we attach a noop catch handler here to suppress this warning. If you actually await somewhere or construct\n  // your own promise out of it you'll need to ensure you handle the error when it rejects.\n  hangingPromise.catch(ignoreReject)\n  return hangingPromise\n}\n\nfunction ignoreReject() {}\n", "/**\n * The functions provided by this module are used to communicate certain properties\n * about the currently running code so that Next.js can make decisions on how to handle\n * the current execution in different rendering modes such as pre-rendering, resuming, and SSR.\n *\n * Today Next.js treats all code as potentially static. Certain APIs may only make sense when dynamically rendering.\n * Traditionally this meant deopting the entire render to dynamic however with PPR we can now deopt parts\n * of a React tree as dynamic while still keeping other parts static. There are really two different kinds of\n * Dynamic indications.\n *\n * The first is simply an intention to be dynamic. unstable_noStore is an example of this where\n * the currently executing code simply declares that the current scope is dynamic but if you use it\n * inside unstable_cache it can still be cached. This type of indication can be removed if we ever\n * make the default dynamic to begin with because the only way you would ever be static is inside\n * a cache scope which this indication does not affect.\n *\n * The second is an indication that a dynamic data source was read. This is a stronger form of dynamic\n * because it means that it is inappropriate to cache this at all. using a dynamic data source inside\n * unstable_cache should error. If you want to use some dynamic data inside unstable_cache you should\n * read that data outside the cache and pass it in as an argument to the cached function.\n */\n\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport type {\n  WorkUnitStore,\n  RequestStore,\n  PrerenderStoreLegacy,\n  PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\n\n// Once postpone is in stable we should switch to importing the postpone export directly\nimport React from 'react'\n\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport {\n  METADATA_BOUNDARY_NAME,\n  VIEWPORT_BOUNDARY_NAME,\n  OUTLET_BOUNDARY_NAME,\n} from '../../lib/metadata/metadata-constants'\nimport { scheduleOnNextTick } from '../../lib/scheduler'\n\nconst hasPostpone = typeof React.unstable_postpone === 'function'\n\nexport type DynamicAccess = {\n  /**\n   * If debugging, this will contain the stack trace of where the dynamic access\n   * occurred. This is used to provide more information to the user about why\n   * their page is being rendered dynamically.\n   */\n  stack?: string\n\n  /**\n   * The expression that was accessed dynamically.\n   */\n  expression: string\n}\n\n// Stores dynamic reasons used during an RSC render.\nexport type DynamicTrackingState = {\n  /**\n   * When true, stack information will also be tracked during dynamic access.\n   */\n  readonly isDebugDynamicAccesses: boolean | undefined\n\n  /**\n   * The dynamic accesses that occurred during the render.\n   */\n  readonly dynamicAccesses: Array<DynamicAccess>\n\n  syncDynamicExpression: undefined | string\n  syncDynamicErrorWithStack: null | Error\n  // Dev only\n  syncDynamicLogged?: boolean\n}\n\n// Stores dynamic reasons used during an SSR render.\nexport type DynamicValidationState = {\n  hasSuspendedDynamic: boolean\n  hasDynamicMetadata: boolean\n  hasDynamicViewport: boolean\n  hasSyncDynamicErrors: boolean\n  dynamicErrors: Array<Error>\n}\n\nexport function createDynamicTrackingState(\n  isDebugDynamicAccesses: boolean | undefined\n): DynamicTrackingState {\n  return {\n    isDebugDynamicAccesses,\n    dynamicAccesses: [],\n    syncDynamicExpression: undefined,\n    syncDynamicErrorWithStack: null,\n  }\n}\n\nexport function createDynamicValidationState(): DynamicValidationState {\n  return {\n    hasSuspendedDynamic: false,\n    hasDynamicMetadata: false,\n    hasDynamicViewport: false,\n    hasSyncDynamicErrors: false,\n    dynamicErrors: [],\n  }\n}\n\nexport function getFirstDynamicReason(\n  trackingState: DynamicTrackingState\n): undefined | string {\n  return trackingState.dynamicAccesses[0]?.expression\n}\n\n/**\n * This function communicates that the current scope should be treated as dynamic.\n *\n * In most cases this function is a no-op but if called during\n * a PPR prerender it will postpone the current sub-tree and calling\n * it during a normal prerender will cause the entire prerender to abort\n */\nexport function markCurrentScopeAsDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | Exclude<WorkUnitStore, PrerenderStoreModern>,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n  }\n\n  // If we're forcing dynamic rendering or we're forcing static rendering, we\n  // don't need to do anything here because the entire page is already dynamic\n  // or it's static and it should not throw or postpone here.\n  if (store.forceDynamic || store.forceStatic) return\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender-ppr') {\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      workUnitStore.revalidate = 0\n\n      // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used ${expression}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n/**\n * This function communicates that some dynamic path parameter was read. This\n * differs from the more general `trackDynamicDataAccessed` in that it is will\n * not error when `dynamic = \"error\"` is set.\n *\n * @param store The static generation store\n * @param expression The expression that was accessed dynamically\n */\nexport function trackFallbackParamAccessed(\n  store: WorkStore,\n  expression: string\n): void {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (!prerenderStore || prerenderStore.type !== 'prerender-ppr') return\n\n  postponeWithTracking(store.route, expression, prerenderStore.dynamicTracking)\n}\n\n/**\n * This function is meant to be used when prerendering without dynamicIO or PPR.\n * When called during a build it will cause Next.js to consider the route as dynamic.\n *\n * @internal\n */\nexport function throwToInterruptStaticGeneration(\n  expression: string,\n  store: WorkStore,\n  prerenderStore: PrerenderStoreLegacy\n): never {\n  // We aren't prerendering but we are generating a static page. We need to bail out of static generation\n  const err = new DynamicServerError(\n    `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n  )\n\n  prerenderStore.revalidate = 0\n\n  store.dynamicUsageDescription = expression\n  store.dynamicUsageStack = err.stack\n\n  throw err\n}\n\n/**\n * This function should be used to track whether something dynamic happened even when\n * we are in a dynamic render. This is useful for Dev where all renders are dynamic but\n * we still track whether dynamic APIs were accessed for helpful messaging\n *\n * @internal\n */\nexport function trackDynamicDataInDynamicRender(\n  _store: WorkStore,\n  workUnitStore: void | WorkUnitStore\n) {\n  if (workUnitStore) {\n    if (\n      workUnitStore.type === 'cache' ||\n      workUnitStore.type === 'unstable-cache'\n    ) {\n      // inside cache scopes marking a scope as dynamic has no effect because the outer cache scope\n      // creates a cache boundary. This is subtly different from reading a dynamic data source which is\n      // forbidden inside a cache scope.\n      return\n    }\n    if (\n      workUnitStore.type === 'prerender' ||\n      workUnitStore.type === 'prerender-legacy'\n    ) {\n      workUnitStore.revalidate = 0\n    }\n    if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n\n// Despite it's name we don't actually abort unless we have a controller to call abort on\n// There are times when we let a prerender run long to discover caches where we want the semantics\n// of tracking dynamic access without terminating the prerender early\nfunction abortOnSynchronousDynamicDataAccess(\n  route: string,\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const reason = `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n\n  const error = createPrerenderInterruptedError(reason)\n\n  prerenderStore.controller.abort(error)\n\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function abortOnSynchronousPlatformIOAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): void {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n    }\n  }\n  return abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n}\n\nexport function trackSynchronousPlatformIOAccessInDev(\n  requestStore: RequestStore\n): void {\n  // We don't actually have a controller to abort but we do the semantic equivalent by\n  // advancing the request store out of prerender mode\n  requestStore.prerenderPhase = false\n}\n\n/**\n * use this function when prerendering with dynamicIO. If we are doing a\n * prospective prerender we don't actually abort because we want to discover\n * all caches for the shell. If this is the actual prerender we do abort.\n *\n * This function accepts a prerenderStore but the caller should ensure we're\n * actually running in dynamicIO mode.\n *\n * @internal\n */\nexport function abortAndThrowOnSynchronousRequestDataAccess(\n  route: string,\n  expression: string,\n  errorWithStack: Error,\n  prerenderStore: PrerenderStoreModern\n): never {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    if (dynamicTracking.syncDynamicErrorWithStack === null) {\n      dynamicTracking.syncDynamicExpression = expression\n      dynamicTracking.syncDynamicErrorWithStack = errorWithStack\n      if (prerenderStore.validating === true) {\n        // We always log Request Access in dev at the point of calling the function\n        // So we mark the dynamic validation as not requiring it to be printed\n        dynamicTracking.syncDynamicLogged = true\n      }\n    }\n  }\n  abortOnSynchronousDynamicDataAccess(route, expression, prerenderStore)\n  throw createPrerenderInterruptedError(\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}.`\n  )\n}\n\n// For now these implementations are the same so we just reexport\nexport const trackSynchronousRequestDataAccessInDev =\n  trackSynchronousPlatformIOAccessInDev\n\n/**\n * This component will call `React.postpone` that throws the postponed error.\n */\ntype PostponeProps = {\n  reason: string\n  route: string\n}\nexport function Postpone({ reason, route }: PostponeProps): never {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  const dynamicTracking =\n    prerenderStore && prerenderStore.type === 'prerender-ppr'\n      ? prerenderStore.dynamicTracking\n      : null\n  postponeWithTracking(route, reason, dynamicTracking)\n}\n\nexport function postponeWithTracking(\n  route: string,\n  expression: string,\n  dynamicTracking: null | DynamicTrackingState\n): never {\n  assertPostpone()\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      // When we aren't debugging, we don't need to create another error for the\n      // stack trace.\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n\n  React.unstable_postpone(createPostponeReason(route, expression))\n}\n\nfunction createPostponeReason(route: string, expression: string) {\n  return (\n    `Route ${route} needs to bail out of prerendering at this point because it used ${expression}. ` +\n    `React throws this special object to indicate where. It should not be caught by ` +\n    `your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`\n  )\n}\n\nexport function isDynamicPostpone(err: unknown) {\n  if (\n    typeof err === 'object' &&\n    err !== null &&\n    typeof (err as any).message === 'string'\n  ) {\n    return isDynamicPostponeReason((err as any).message)\n  }\n  return false\n}\n\nfunction isDynamicPostponeReason(reason: string) {\n  return (\n    reason.includes(\n      'needs to bail out of prerendering at this point because it used'\n    ) &&\n    reason.includes(\n      'Learn more: https://nextjs.org/docs/messages/ppr-caught-error'\n    )\n  )\n}\n\nif (isDynamicPostponeReason(createPostponeReason('%%%', '^^^')) === false) {\n  throw new Error(\n    'Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js'\n  )\n}\n\nconst NEXT_PRERENDER_INTERRUPTED = 'NEXT_PRERENDER_INTERRUPTED'\n\nfunction createPrerenderInterruptedError(message: string): Error {\n  const error = new Error(message)\n  ;(error as any).digest = NEXT_PRERENDER_INTERRUPTED\n  return error\n}\n\ntype DigestError = Error & {\n  digest: string\n}\n\nexport function isPrerenderInterruptedError(\n  error: unknown\n): error is DigestError {\n  return (\n    typeof error === 'object' &&\n    error !== null &&\n    (error as any).digest === NEXT_PRERENDER_INTERRUPTED &&\n    'name' in error &&\n    'message' in error &&\n    error instanceof Error\n  )\n}\n\nexport function accessedDynamicData(\n  dynamicAccesses: Array<DynamicAccess>\n): boolean {\n  return dynamicAccesses.length > 0\n}\n\nexport function consumeDynamicAccess(\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): DynamicTrackingState['dynamicAccesses'] {\n  // We mutate because we only call this once we are no longer writing\n  // to the dynamicTrackingState and it's more efficient than creating a new\n  // array.\n  serverDynamic.dynamicAccesses.push(...clientDynamic.dynamicAccesses)\n  return serverDynamic.dynamicAccesses\n}\n\nexport function formatDynamicAPIAccesses(\n  dynamicAccesses: Array<DynamicAccess>\n): string[] {\n  return dynamicAccesses\n    .filter(\n      (access): access is Required<DynamicAccess> =>\n        typeof access.stack === 'string' && access.stack.length > 0\n    )\n    .map(({ expression, stack }) => {\n      stack = stack\n        .split('\\n')\n        // Remove the \"Error: \" prefix from the first line of the stack trace as\n        // well as the first 4 lines of the stack trace which is the distance\n        // from the user code and the `new Error().stack` call.\n        .slice(4)\n        .filter((line) => {\n          // Exclude Next.js internals from the stack trace.\n          if (line.includes('node_modules/next/')) {\n            return false\n          }\n\n          // Exclude anonymous functions from the stack trace.\n          if (line.includes(' (<anonymous>)')) {\n            return false\n          }\n\n          // Exclude Node.js internals from the stack trace.\n          if (line.includes(' (node:')) {\n            return false\n          }\n\n          return true\n        })\n        .join('\\n')\n      return `Dynamic API Usage Debug - ${expression}:\\n${stack}`\n    })\n}\n\nfunction assertPostpone() {\n  if (!hasPostpone) {\n    throw new Error(\n      `Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js`\n    )\n  }\n}\n\n/**\n * This is a bit of a hack to allow us to abort a render using a Postpone instance instead of an Error which changes React's\n * abort semantics slightly.\n */\nexport function createPostponedAbortSignal(reason: string): AbortSignal {\n  assertPostpone()\n  const controller = new AbortController()\n  // We get our hands on a postpone instance by calling postpone and catching the throw\n  try {\n    React.unstable_postpone(reason)\n  } catch (x: unknown) {\n    controller.abort(x)\n  }\n  return controller.signal\n}\n\n/**\n * In a prerender, we may end up with hanging Promises as inputs due them\n * stalling on connection() or because they're loading dynamic data. In that\n * case we need to abort the encoding of arguments since they'll never complete.\n */\nexport function createHangingInputAbortSignal(\n  workUnitStore: PrerenderStoreModern\n): AbortSignal {\n  const controller = new AbortController()\n\n  if (workUnitStore.cacheSignal) {\n    // If we have a cacheSignal it means we're in a prospective render. If the input\n    // we're waiting on is coming from another cache, we do want to wait for it so that\n    // we can resolve this cache entry too.\n    workUnitStore.cacheSignal.inputReady().then(() => {\n      controller.abort()\n    })\n  } else {\n    // Otherwise we're in the final render and we should already have all our caches\n    // filled. We might still be waiting on some microtasks so we wait one tick before\n    // giving up. When we give up, we still want to render the content of this cache\n    // as deeply as we can so that we can suspend as deeply as possible in the tree\n    // or not at all if we don't end up waiting for the input.\n    scheduleOnNextTick(() => controller.abort())\n  }\n\n  return controller.signal\n}\n\nexport function annotateDynamicAccess(\n  expression: string,\n  prerenderStore: PrerenderStoreModern\n) {\n  const dynamicTracking = prerenderStore.dynamicTracking\n  if (dynamicTracking) {\n    dynamicTracking.dynamicAccesses.push({\n      stack: dynamicTracking.isDebugDynamicAccesses\n        ? new Error().stack\n        : undefined,\n      expression,\n    })\n  }\n}\n\nexport function useDynamicRouteParams(expression: string) {\n  const workStore = workAsyncStorage.getStore()\n\n  if (\n    workStore &&\n    workStore.isStaticGeneration &&\n    workStore.fallbackRouteParams &&\n    workStore.fallbackRouteParams.size > 0\n  ) {\n    // There are fallback route params, we should track these as dynamic\n    // accesses.\n    const workUnitStore = workUnitAsyncStorage.getStore()\n    if (workUnitStore) {\n      // We're prerendering with dynamicIO or PPR or both\n      if (workUnitStore.type === 'prerender') {\n        // We are in a prerender with dynamicIO semantics\n        // We are going to hang here and never resolve. This will cause the currently\n        // rendering component to effectively be a dynamic hole\n        React.use(makeHangingPromise(workUnitStore.renderSignal, expression))\n      } else if (workUnitStore.type === 'prerender-ppr') {\n        // We're prerendering with PPR\n        postponeWithTracking(\n          workStore.route,\n          expression,\n          workUnitStore.dynamicTracking\n        )\n      } else if (workUnitStore.type === 'prerender-legacy') {\n        throwToInterruptStaticGeneration(expression, workStore, workUnitStore)\n      }\n    }\n  }\n}\n\nconst hasSuspenseRegex = /\\n\\s+at Suspense \\(<anonymous>\\)/\nconst hasMetadataRegex = new RegExp(\n  `\\\\n\\\\s+at ${METADATA_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasViewportRegex = new RegExp(\n  `\\\\n\\\\s+at ${VIEWPORT_BOUNDARY_NAME}[\\\\n\\\\s]`\n)\nconst hasOutletRegex = new RegExp(`\\\\n\\\\s+at ${OUTLET_BOUNDARY_NAME}[\\\\n\\\\s]`)\n\nexport function trackAllowedDynamicAccess(\n  route: string,\n  componentStack: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n) {\n  if (hasOutletRegex.test(componentStack)) {\n    // We don't need to track that this is dynamic. It is only so when something else is also dynamic.\n    return\n  } else if (hasMetadataRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicMetadata = true\n    return\n  } else if (hasViewportRegex.test(componentStack)) {\n    dynamicValidation.hasDynamicViewport = true\n    return\n  } else if (hasSuspenseRegex.test(componentStack)) {\n    dynamicValidation.hasSuspendedDynamic = true\n    return\n  } else if (\n    serverDynamic.syncDynamicErrorWithStack ||\n    clientDynamic.syncDynamicErrorWithStack\n  ) {\n    dynamicValidation.hasSyncDynamicErrors = true\n    return\n  } else {\n    const message = `Route \"${route}\": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a \"use cache\" above it. We don't have the exact line number added to error messages yet but you can see which component in the stack below. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`\n    const error = createErrorWithComponentStack(message, componentStack)\n    dynamicValidation.dynamicErrors.push(error)\n    return\n  }\n}\n\nfunction createErrorWithComponentStack(\n  message: string,\n  componentStack: string\n) {\n  const error = new Error(message)\n  error.stack = 'Error: ' + message + componentStack\n  return error\n}\n\nexport function throwIfDisallowedDynamic(\n  route: string,\n  dynamicValidation: DynamicValidationState,\n  serverDynamic: DynamicTrackingState,\n  clientDynamic: DynamicTrackingState\n): void {\n  let syncError: null | Error\n  let syncExpression: undefined | string\n  let syncLogged: boolean\n  if (serverDynamic.syncDynamicErrorWithStack) {\n    syncError = serverDynamic.syncDynamicErrorWithStack\n    syncExpression = serverDynamic.syncDynamicExpression!\n    syncLogged = serverDynamic.syncDynamicLogged === true\n  } else if (clientDynamic.syncDynamicErrorWithStack) {\n    syncError = clientDynamic.syncDynamicErrorWithStack\n    syncExpression = clientDynamic.syncDynamicExpression!\n    syncLogged = clientDynamic.syncDynamicLogged === true\n  } else {\n    syncError = null\n    syncExpression = undefined\n    syncLogged = false\n  }\n\n  if (dynamicValidation.hasSyncDynamicErrors && syncError) {\n    if (!syncLogged) {\n      // In dev we already log errors about sync dynamic access. But during builds we need to ensure\n      // the offending sync error is logged before we exit the build\n      console.error(syncError)\n    }\n    // The actual error should have been logged when the sync access ocurred\n    throw new StaticGenBailoutError()\n  }\n\n  const dynamicErrors = dynamicValidation.dynamicErrors\n  if (dynamicErrors.length) {\n    for (let i = 0; i < dynamicErrors.length; i++) {\n      console.error(dynamicErrors[i])\n    }\n\n    throw new StaticGenBailoutError()\n  }\n\n  if (!dynamicValidation.hasSuspendedDynamic) {\n    if (dynamicValidation.hasDynamicMetadata) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateMetadata\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateMetadata\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateMetadata\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    } else if (dynamicValidation.hasDynamicViewport) {\n      if (syncError) {\n        console.error(syncError)\n        throw new StaticGenBailoutError(\n          `Route \"${route}\" has a \\`generateViewport\\` that could not finish rendering before ${syncExpression} was used. Follow the instructions in the error for this expression to resolve.`\n        )\n      }\n      throw new StaticGenBailoutError(\n        `Route \"${route}\" has a \\`generateViewport\\` that depends on Request data (\\`cookies()\\`, etc...) or external data (\\`fetch(...)\\`, etc...) but the rest of the route was static or only used cached data (\\`\"use cache\"\\`). If you expected this route to be prerenderable update your \\`generateViewport\\` to not use Request data and only use cached external data. Otherwise, add \\`await connection()\\` somewhere within this route to indicate explicitly it should not be prerendered.`\n      )\n    }\n  }\n}\n", "/**\n * Clones a response by teeing the body so we can return two independent\n * ReadableStreams from it. This avoids the bug in the undici library around\n * response cloning.\n *\n * After cloning, the original response's body will be consumed and closed.\n *\n * @see https://github.com/vercel/next.js/pull/73274\n *\n * @param original - The original response to clone.\n * @returns A tuple containing two independent clones of the original response.\n */\nexport function cloneResponse(original: Response): [Response, Response] {\n  // If the response has no body, then we can just return the original response\n  // twice because it's immutable.\n  if (!original.body) {\n    return [original, original]\n  }\n\n  const [body1, body2] = original.body.tee()\n\n  const cloned1 = new Response(body1, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned1, 'url', {\n    value: original.url,\n  })\n\n  const cloned2 = new Response(body2, {\n    status: original.status,\n    statusText: original.statusText,\n    headers: original.headers,\n  })\n\n  Object.defineProperty(cloned2, 'url', {\n    value: original.url,\n  })\n\n  return [cloned1, cloned2]\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport type RenderResult from '../render-result'\nimport type { CacheControl, Revalidate } from '../lib/cache-control'\nimport type { RouteKind } from '../route-kind'\n\nexport interface ResponseCacheBase {\n  get(\n    key: string | null,\n    responseGenerator: ResponseGenerator,\n    context: {\n      isOnDemandRevalidate?: boolean\n      isPrefetch?: boolean\n      incrementalCache: IncrementalCache\n      /**\n       * This is a hint to the cache to help it determine what kind of route\n       * this is so it knows where to look up the cache entry from. If not\n       * provided it will test the filesystem to check.\n       */\n      routeKind: RouteKind\n\n      /**\n       * True if this is a fallback request.\n       */\n      isFallback?: boolean\n\n      /**\n       * True if the route is enabled for PPR.\n       */\n      isRoutePPREnabled?: boolean\n    }\n  ): Promise<ResponseCacheEntry | null>\n}\n\n// The server components HMR cache might store other data as well in the future,\n// at which point this should be refactored to a discriminated union type.\nexport interface ServerComponentsHmrCache {\n  get(key: string): CachedFetchData | undefined\n  set(key: string, data: CachedFetchData): void\n}\n\nexport type CachedFetchData = {\n  headers: Record<string, string>\n  body: string\n  url: string\n  status?: number\n}\n\nexport const enum CachedRouteKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  REDIRECT = 'REDIRECT',\n  IMAGE = 'IMAGE',\n}\n\nexport interface CachedFetchValue {\n  kind: CachedRouteKind.FETCH\n  data: CachedFetchData\n  // tags are only present with file-system-cache\n  // fetch cache stores tags outside of cache entry\n  tags?: string[]\n  revalidate: number\n}\n\nexport interface CachedRedirectValue {\n  kind: CachedRouteKind.REDIRECT\n  props: Object\n}\n\nexport interface CachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  rscData: Buffer | undefined\n  status: number | undefined\n  postponed: string | undefined\n  headers: OutgoingHttpHeaders | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface CachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  html: RenderResult\n  pageData: Object\n  status: number | undefined\n  headers: OutgoingHttpHeaders | undefined\n}\n\nexport interface CachedRouteValue {\n  kind: CachedRouteKind.APP_ROUTE\n  // this needs to be a RenderResult so since renderResponse\n  // expects that type instead of a string\n  body: Buffer\n  status: number\n  headers: OutgoingHttpHeaders\n}\n\nexport interface CachedImageValue {\n  kind: CachedRouteKind.IMAGE\n  etag: string\n  upstreamEtag: string\n  buffer: Buffer\n  extension: string\n  isMiss?: boolean\n  isStale?: boolean\n}\n\nexport interface IncrementalCachedAppPageValue {\n  kind: CachedRouteKind.APP_PAGE\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  rscData: Buffer | undefined\n  headers: OutgoingHttpHeaders | undefined\n  postponed: string | undefined\n  status: number | undefined\n  segmentData: Map<string, Buffer> | undefined\n}\n\nexport interface IncrementalCachedPageValue {\n  kind: CachedRouteKind.PAGES\n  // this needs to be a string since the cache expects to store\n  // the string value\n  html: string\n  pageData: Object\n  headers: OutgoingHttpHeaders | undefined\n  status: number | undefined\n}\n\nexport interface IncrementalResponseCacheEntry {\n  cacheControl?: CacheControl\n  /**\n   * timestamp in milliseconds to revalidate after\n   */\n  revalidateAfter?: Revalidate\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n  value: Exclude<IncrementalCacheValue, CachedFetchValue> | null\n}\n\nexport interface IncrementalFetchCacheEntry {\n  /**\n   * `-1` here dictates a blocking revalidate should be used\n   */\n  isStale?: boolean | -1\n  value: CachedFetchValue\n}\n\nexport type IncrementalCacheEntry =\n  | IncrementalResponseCacheEntry\n  | IncrementalFetchCacheEntry\n\nexport type IncrementalCacheValue =\n  | CachedRedirectValue\n  | IncrementalCachedPageValue\n  | IncrementalCachedAppPageValue\n  | CachedImageValue\n  | CachedFetchValue\n  | CachedRouteValue\n\nexport type ResponseCacheValue =\n  | CachedRedirectValue\n  | CachedPageValue\n  | CachedAppPageValue\n  | CachedImageValue\n  | CachedRouteValue\n\nexport type ResponseCacheEntry = {\n  cacheControl?: CacheControl\n  value: ResponseCacheValue | null\n  isStale?: boolean | -1\n  isMiss?: boolean\n  isFallback: boolean | undefined\n}\n\n/**\n * @param hasResolved whether the responseGenerator has resolved it's promise\n * @param previousCacheEntry the previous cache entry if it exists or the current\n */\nexport type ResponseGenerator = (state: {\n  hasResolved: boolean\n  previousCacheEntry?: IncrementalResponseCacheEntry | null\n  isRevalidating?: boolean\n}) => Promise<ResponseCacheEntry | null>\n\nexport const enum IncrementalCacheKind {\n  APP_PAGE = 'APP_PAGE',\n  APP_ROUTE = 'APP_ROUTE',\n  PAGES = 'PAGES',\n  FETCH = 'FETCH',\n  IMAGE = 'IMAGE',\n}\n\nexport interface GetIncrementalFetchCacheContext {\n  kind: IncrementalCacheKind.FETCH\n  revalidate?: Revalidate\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n  softTags?: string[]\n}\n\nexport interface GetIncrementalResponseCacheContext {\n  kind: Exclude<IncrementalCacheKind, IncrementalCacheKind.FETCH>\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback: boolean\n}\n\nexport interface SetIncrementalFetchCacheContext {\n  fetchCache: true\n  fetchUrl?: string\n  fetchIdx?: number\n  tags?: string[]\n}\n\nexport interface SetIncrementalResponseCacheContext {\n  fetchCache?: false\n  cacheControl?: CacheControl\n\n  /**\n   * True if the route is enabled for PPR.\n   */\n  isRoutePPREnabled?: boolean\n\n  /**\n   * True if this is a fallback request.\n   */\n  isFallback?: boolean\n}\n\nexport interface IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n\nexport interface IncrementalCache extends IncrementalResponseCache {\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalFetchCacheContext\n  ): Promise<IncrementalFetchCacheEntry | null>\n  get(\n    cacheKey: string,\n    ctx: GetIncrementalResponseCacheContext\n  ): Promise<IncrementalResponseCacheEntry | null>\n  set(\n    key: string,\n    data: CachedFetchValue | null,\n    ctx: SetIncrementalFetchCacheContext\n  ): Promise<void>\n  set(\n    key: string,\n    data: Exclude<IncrementalCacheValue, CachedFetchValue> | null,\n    ctx: SetIncrementalResponseCacheContext\n  ): Promise<void>\n}\n", "/**\n * Removes the trailing slash for a given route or page path. Preserves the\n * root page. Examples:\n *   - `/foo/bar/` -> `/foo/bar`\n *   - `/foo/bar` -> `/foo/bar`\n *   - `/` -> `/`\n */\nexport function removeTrailingSlash(route: string) {\n  return route.replace(/\\/$/, '') || '/'\n}\n", "/**\n * Given a path this function will find the pathname, query and hash and return\n * them. This is useful to parse full paths on the client side.\n * @param path A path to parse e.g. /foo/bar?id=1#hash\n */\nexport function parsePath(path: string) {\n  const hashIndex = path.indexOf('#')\n  const queryIndex = path.indexOf('?')\n  const hasQuery = queryIndex > -1 && (hashIndex < 0 || queryIndex < hashIndex)\n\n  if (hasQuery || hashIndex > -1) {\n    return {\n      pathname: path.substring(0, hasQuery ? queryIndex : hashIndex),\n      query: hasQuery\n        ? path.substring(queryIndex, hashIndex > -1 ? hashIndex : undefined)\n        : '',\n      hash: hashIndex > -1 ? path.slice(hashIndex) : '',\n    }\n  }\n\n  return { pathname: path, query: '', hash: '' }\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Adds the provided prefix to the given path. It first ensures that the path\n * is indeed starting with a slash.\n */\nexport function addPathPrefix(path: string, prefix?: string) {\n  if (!path.startsWith('/') || !prefix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${prefix}${pathname}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Similarly to `addPathPrefix`, this function adds a suffix at the end on the\n * provided path. It also works only for paths ensuring the argument starts\n * with a slash.\n */\nexport function addPathSuffix(path: string, suffix?: string) {\n  if (!path.startsWith('/') || !suffix) {\n    return path\n  }\n\n  const { pathname, query, hash } = parsePath(path)\n  return `${pathname}${suffix}${query}${hash}`\n}\n", "import { parsePath } from './parse-path'\n\n/**\n * Checks if a given path starts with a given prefix. It ensures it matches\n * exactly without containing extra chars. e.g. prefix /docs should replace\n * for /docs, /docs/, /docs/a but not /docsss\n * @param path The path to check.\n * @param prefix The prefix to check against.\n */\nexport function pathHasPrefix(path: string, prefix: string) {\n  if (typeof path !== 'string') {\n    return false\n  }\n\n  const { pathname } = parsePath(path)\n  return pathname === prefix || pathname.startsWith(prefix + '/')\n}\n", "export const ENCODED_TAGS = {\n  // opening tags do not have the closing `>` since they can contain other attributes such as `<body className=''>`\n  OPENING: {\n    // <html\n    HTML: new Uint8Array([60, 104, 116, 109, 108]),\n    // <body\n    BODY: new Uint8Array([60, 98, 111, 100, 121]),\n  },\n  CLOSED: {\n    // </head>\n    HEAD: new Uint8Array([60, 47, 104, 101, 97, 100, 62]),\n    // </body>\n    BODY: new Uint8Array([60, 47, 98, 111, 100, 121, 62]),\n    // </html>\n    HTML: new Uint8Array([60, 47, 104, 116, 109, 108, 62]),\n    // </body></html>\n    BODY_AND_HTML: new Uint8Array([\n      60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62,\n    ]),\n  },\n} as const\n", "import { getTracer } from '../lib/trace/tracer'\nimport { AppRenderSpan } from '../lib/trace/constants'\nimport { DetachedPromise } from '../../lib/detached-promise'\nimport { scheduleImmediate, atLeastOneTask } from '../../lib/scheduler'\nimport { ENCODED_TAGS } from './encodedTags'\nimport {\n  indexOfUint8Array,\n  isEquivalentUint8Arrays,\n  removeFromUint8Array,\n} from './uint8array-helpers'\n\nfunction voidCatch() {\n  // this catcher is designed to be used with pipeTo where we expect the underlying\n  // pipe implementation to forward errors but we don't want the pipeTo promise to reject\n  // and be unhandled\n}\n\nexport type ReactReadableStream = ReadableStream<Uint8Array> & {\n  allReady?: Promise<void> | undefined\n}\n\n// We can share the same encoder instance everywhere\n// Notably we cannot do the same for TextDecoder because it is stateful\n// when handling streaming data\nconst encoder = new TextEncoder()\n\nexport function chainStreams<T>(\n  ...streams: ReadableStream<T>[]\n): ReadableStream<T> {\n  // We could encode this invariant in the arguments but current uses of this function pass\n  // use spread so it would be missed by\n  if (streams.length === 0) {\n    throw new Error('Invariant: chainStreams requires at least one stream')\n  }\n\n  // If we only have 1 stream we fast path it by returning just this stream\n  if (streams.length === 1) {\n    return streams[0]\n  }\n\n  const { readable, writable } = new TransformStream()\n\n  // We always initiate pipeTo immediately. We know we have at least 2 streams\n  // so we need to avoid closing the writable when this one finishes.\n  let promise = streams[0].pipeTo(writable, { preventClose: true })\n\n  let i = 1\n  for (; i < streams.length - 1; i++) {\n    const nextStream = streams[i]\n    promise = promise.then(() =>\n      nextStream.pipeTo(writable, { preventClose: true })\n    )\n  }\n\n  // We can omit the length check because we halted before the last stream and there\n  // is at least two streams so the lastStream here will always be defined\n  const lastStream = streams[i]\n  promise = promise.then(() => lastStream.pipeTo(writable))\n\n  // Catch any errors from the streams and ignore them, they will be handled\n  // by whatever is consuming the readable stream.\n  promise.catch(voidCatch)\n\n  return readable\n}\n\nexport function streamFromString(str: string): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(encoder.encode(str))\n      controller.close()\n    },\n  })\n}\n\nexport function streamFromBuffer(chunk: Buffer): ReadableStream<Uint8Array> {\n  return new ReadableStream({\n    start(controller) {\n      controller.enqueue(chunk)\n      controller.close()\n    },\n  })\n}\n\nexport async function streamToBuffer(\n  stream: ReadableStream<Uint8Array>\n): Promise<Buffer> {\n  const reader = stream.getReader()\n  const chunks: Uint8Array[] = []\n\n  while (true) {\n    const { done, value } = await reader.read()\n    if (done) {\n      break\n    }\n\n    chunks.push(value)\n  }\n\n  return Buffer.concat(chunks)\n}\n\nexport async function streamToString(\n  stream: ReadableStream<Uint8Array>,\n  signal?: AbortSignal\n): Promise<string> {\n  const decoder = new TextDecoder('utf-8', { fatal: true })\n  let string = ''\n\n  for await (const chunk of stream) {\n    if (signal?.aborted) {\n      return string\n    }\n\n    string += decoder.decode(chunk, { stream: true })\n  }\n\n  string += decoder.decode()\n\n  return string\n}\n\nexport function createBufferedTransformStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let bufferedChunks: Array<Uint8Array> = []\n  let bufferByteLength: number = 0\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    // If we already have a pending flush, then return early.\n    if (pending) return\n\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        const chunk = new Uint8Array(bufferByteLength)\n        let copiedBytes = 0\n\n        for (let i = 0; i < bufferedChunks.length; i++) {\n          const bufferedChunk = bufferedChunks[i]\n          chunk.set(bufferedChunk, copiedBytes)\n          copiedBytes += bufferedChunk.byteLength\n        }\n        // We just wrote all the buffered chunks so we need to reset the bufferedChunks array\n        // and our bufferByteLength to prepare for the next round of buffered chunks\n        bufferedChunks.length = 0\n        bufferByteLength = 0\n        controller.enqueue(chunk)\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      // Combine the previous buffer with the new chunk.\n      bufferedChunks.push(chunk)\n      bufferByteLength += chunk.byteLength\n\n      // Flush the buffer to the controller.\n      flush(controller)\n    },\n    flush() {\n      if (!pending) return\n\n      return pending.promise\n    },\n  })\n}\n\nexport function renderToInitialFizzStream({\n  ReactDOMServer,\n  element,\n  streamOptions,\n}: {\n  ReactDOMServer: typeof import('react-dom/server.edge')\n  element: React.ReactElement\n  streamOptions?: Parameters<typeof ReactDOMServer.renderToReadableStream>[1]\n}): Promise<ReactReadableStream> {\n  return getTracer().trace(AppRenderSpan.renderToReadableStream, async () =>\n    ReactDOMServer.renderToReadableStream(element, streamOptions)\n  )\n}\n\nfunction createHeadInsertionTransformStream(\n  insert: () => Promise<string>\n): TransformStream<Uint8Array, Uint8Array> {\n  let inserted = false\n\n  // We need to track if this transform saw any bytes because if it didn't\n  // we won't want to insert any server HTML at all\n  let hasBytes = false\n\n  return new TransformStream({\n    async transform(chunk, controller) {\n      hasBytes = true\n\n      const insertion = await insert()\n      if (inserted) {\n        if (insertion) {\n          const encodedInsertion = encoder.encode(insertion)\n          controller.enqueue(encodedInsertion)\n        }\n        controller.enqueue(chunk)\n      } else {\n        // TODO (@Ethan-Arrowood): Replace the generic `indexOfUint8Array` method with something finely tuned for the subset of things actually being checked for.\n        const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.HEAD)\n        // In fully static rendering or non PPR rendering cases:\n        // `/head>` will always be found in the chunk in first chunk rendering.\n        if (index !== -1) {\n          if (insertion) {\n            const encodedInsertion = encoder.encode(insertion)\n            // Get the total count of the bytes in the chunk and the insertion\n            // e.g.\n            // chunk = <head><meta charset=\"utf-8\"></head>\n            // insertion = <script>...</script>\n            // output = <head><meta charset=\"utf-8\"> [ <script>...</script> ] </head>\n            const insertedHeadContent = new Uint8Array(\n              chunk.length + encodedInsertion.length\n            )\n            // Append the first part of the chunk, before the head tag\n            insertedHeadContent.set(chunk.slice(0, index))\n            // Append the server inserted content\n            insertedHeadContent.set(encodedInsertion, index)\n            // Append the rest of the chunk\n            insertedHeadContent.set(\n              chunk.slice(index),\n              index + encodedInsertion.length\n            )\n            controller.enqueue(insertedHeadContent)\n          } else {\n            controller.enqueue(chunk)\n          }\n          inserted = true\n        } else {\n          // This will happens in PPR rendering during next start, when the page is partially rendered.\n          // When the page resumes, the head tag will be found in the middle of the chunk.\n          // Where we just need to append the insertion and chunk to the current stream.\n          // e.g.\n          // PPR-static: <head>...</head><body> [ resume content ] </body>\n          // PPR-resume: [ insertion ] [ rest content ]\n          if (insertion) {\n            controller.enqueue(encoder.encode(insertion))\n          }\n          controller.enqueue(chunk)\n          inserted = true\n        }\n      }\n    },\n    async flush(controller) {\n      // Check before closing if there's anything remaining to insert.\n      if (hasBytes) {\n        const insertion = await insert()\n        if (insertion) {\n          controller.enqueue(encoder.encode(insertion))\n        }\n      }\n    },\n  })\n}\n\n// Suffix after main body content - scripts before </body>,\n// but wait for the major chunks to be enqueued.\nfunction createDeferredSuffixStream(\n  suffix: string\n): TransformStream<Uint8Array, Uint8Array> {\n  let flushed = false\n  let pending: DetachedPromise<void> | undefined\n\n  const flush = (controller: TransformStreamDefaultController) => {\n    const detached = new DetachedPromise<void>()\n    pending = detached\n\n    scheduleImmediate(() => {\n      try {\n        controller.enqueue(encoder.encode(suffix))\n      } catch {\n        // If an error occurs while enqueuing it can't be due to this\n        // transformers fault. It's likely due to the controller being\n        // errored due to the stream being cancelled.\n      } finally {\n        pending = undefined\n        detached.resolve()\n      }\n    })\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // If we've already flushed, we're done.\n      if (flushed) return\n\n      // Schedule the flush to happen.\n      flushed = true\n      flush(controller)\n    },\n    flush(controller) {\n      if (pending) return pending.promise\n      if (flushed) return\n\n      // Flush now.\n      controller.enqueue(encoder.encode(suffix))\n    },\n  })\n}\n\n// Merge two streams into one. Ensure the final transform stream is closed\n// when both are finished.\nfunction createMergedTransformStream(\n  stream: ReadableStream<Uint8Array>\n): TransformStream<Uint8Array, Uint8Array> {\n  let pull: Promise<void> | null = null\n  let donePulling = false\n\n  async function startPulling(controller: TransformStreamDefaultController) {\n    if (pull) {\n      return\n    }\n\n    const reader = stream.getReader()\n\n    // NOTE: streaming flush\n    // We are buffering here for the inlined data stream because the\n    // \"shell\" stream might be chunkenized again by the underlying stream\n    // implementation, e.g. with a specific high-water mark. To ensure it's\n    // the safe timing to pipe the data stream, this extra tick is\n    // necessary.\n\n    // We don't start reading until we've left the current Task to ensure\n    // that it's inserted after flushing the shell. Note that this implementation\n    // might get stale if impl details of Fizz change in the future.\n    await atLeastOneTask()\n\n    try {\n      while (true) {\n        const { done, value } = await reader.read()\n        if (done) {\n          donePulling = true\n          return\n        }\n\n        controller.enqueue(value)\n      }\n    } catch (err) {\n      controller.error(err)\n    }\n  }\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      controller.enqueue(chunk)\n\n      // Start the streaming if it hasn't already been started yet.\n      if (!pull) {\n        pull = startPulling(controller)\n      }\n    },\n    flush(controller) {\n      if (donePulling) {\n        return\n      }\n      return pull || startPulling(controller)\n    },\n  })\n}\n\nconst CLOSE_TAG = '</body></html>'\n\n/**\n * This transform stream moves the suffix to the end of the stream, so results\n * like `</body></html><script>...</script>` will be transformed to\n * `<script>...</script></body></html>`.\n */\nfunction createMoveSuffixStream(): TransformStream<Uint8Array, Uint8Array> {\n  let foundSuffix = false\n\n  return new TransformStream({\n    transform(chunk, controller) {\n      if (foundSuffix) {\n        return controller.enqueue(chunk)\n      }\n\n      const index = indexOfUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n      if (index > -1) {\n        foundSuffix = true\n\n        // If the whole chunk is the suffix, then don't write anything, it will\n        // be written in the flush.\n        if (chunk.length === ENCODED_TAGS.CLOSED.BODY_AND_HTML.length) {\n          return\n        }\n\n        // Write out the part before the suffix.\n        const before = chunk.slice(0, index)\n        controller.enqueue(before)\n\n        // In the case where the suffix is in the middle of the chunk, we need\n        // to split the chunk into two parts.\n        if (chunk.length > ENCODED_TAGS.CLOSED.BODY_AND_HTML.length + index) {\n          // Write out the part after the suffix.\n          const after = chunk.slice(\n            index + ENCODED_TAGS.CLOSED.BODY_AND_HTML.length\n          )\n          controller.enqueue(after)\n        }\n      } else {\n        controller.enqueue(chunk)\n      }\n    },\n    flush(controller) {\n      // Even if we didn't find the suffix, the HTML is not valid if we don't\n      // add it, so insert it at the end.\n      controller.enqueue(ENCODED_TAGS.CLOSED.BODY_AND_HTML)\n    },\n  })\n}\n\nfunction createStripDocumentClosingTagsTransform(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  return new TransformStream({\n    transform(chunk, controller) {\n      // We rely on the assumption that chunks will never break across a code unit.\n      // This is reasonable because we currently concat all of React's output from a single\n      // flush into one chunk before streaming it forward which means the chunk will represent\n      // a single coherent utf-8 string. This is not safe to use if we change our streaming to no\n      // longer do this large buffered chunk\n      if (\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY_AND_HTML) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.BODY) ||\n        isEquivalentUint8Arrays(chunk, ENCODED_TAGS.CLOSED.HTML)\n      ) {\n        // the entire chunk is the closing tags; return without enqueueing anything.\n        return\n      }\n\n      // We assume these tags will go at together at the end of the document and that\n      // they won't appear anywhere else in the document. This is not really a safe assumption\n      // but until we revamp our streaming infra this is a performant way to string the tags\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.BODY)\n      chunk = removeFromUint8Array(chunk, ENCODED_TAGS.CLOSED.HTML)\n\n      controller.enqueue(chunk)\n    },\n  })\n}\n\n/*\n * Checks if the root layout is missing the html or body tags\n * and if so, it will inject a script tag to throw an error in the browser, showing the user\n * the error message in the error overlay.\n */\nexport function createRootLayoutValidatorStream(): TransformStream<\n  Uint8Array,\n  Uint8Array\n> {\n  let foundHtml = false\n  let foundBody = false\n  return new TransformStream({\n    async transform(chunk, controller) {\n      // Peek into the streamed chunk to see if the tags are present.\n      if (\n        !foundHtml &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.HTML) > -1\n      ) {\n        foundHtml = true\n      }\n\n      if (\n        !foundBody &&\n        indexOfUint8Array(chunk, ENCODED_TAGS.OPENING.BODY) > -1\n      ) {\n        foundBody = true\n      }\n\n      controller.enqueue(chunk)\n    },\n    flush(controller) {\n      const missingTags: typeof window.__next_root_layout_missing_tags = []\n      if (!foundHtml) missingTags.push('html')\n      if (!foundBody) missingTags.push('body')\n\n      if (!missingTags.length) return\n\n      controller.enqueue(\n        encoder.encode(\n          `<script>self.__next_root_layout_missing_tags=${JSON.stringify(\n            missingTags\n          )}</script>`\n        )\n      )\n    },\n  })\n}\n\nfunction chainTransformers<T>(\n  readable: ReadableStream<T>,\n  transformers: ReadonlyArray<TransformStream<T, T> | null>\n): ReadableStream<T> {\n  let stream = readable\n  for (const transformer of transformers) {\n    if (!transformer) continue\n\n    stream = stream.pipeThrough(transformer)\n  }\n  return stream\n}\n\nexport type ContinueStreamOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array> | undefined\n  isStaticGeneration: boolean\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n  validateRootLayout?: boolean\n  /**\n   * Suffix to inject after the buffered data, but before the close tags.\n   */\n  suffix?: string | undefined\n}\n\nexport async function continueFizzStream(\n  renderStream: ReactReadableStream,\n  {\n    suffix,\n    inlinedDataStream,\n    isStaticGeneration,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n    validateRootLayout,\n  }: ContinueStreamOptions\n): Promise<ReadableStream<Uint8Array>> {\n  // Suffix itself might contain close tags at the end, so we need to split it.\n  const suffixUnclosed = suffix ? suffix.split(CLOSE_TAG, 1)[0] : null\n\n  // If we're generating static HTML and there's an `allReady` promise on the\n  // stream, we need to wait for it to resolve before continuing.\n  if (isStaticGeneration && 'allReady' in renderStream) {\n    await renderStream.allReady\n  }\n\n  return chainTransformers(renderStream, [\n    // Buffer everything to avoid flushing too frequently\n    createBufferedTransformStream(),\n\n    // Insert generated metadata\n    createHeadInsertionTransformStream(getServerInsertedMetadata),\n\n    // Insert suffix content\n    suffixUnclosed != null && suffixUnclosed.length > 0\n      ? createDeferredSuffixStream(suffixUnclosed)\n      : null,\n\n    // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n    inlinedDataStream ? createMergedTransformStream(inlinedDataStream) : null,\n\n    // Validate the root layout for missing html or body tags\n    validateRootLayout ? createRootLayoutValidatorStream() : null,\n\n    // Close tags should always be deferred to the end\n    createMoveSuffixStream(),\n\n    // Special head insertions\n    // TODO-APP: Insert server side html to end of head in app layout rendering, to avoid\n    // hydration errors. Remove this once it's ready to be handled by react itself.\n    createHeadInsertionTransformStream(getServerInsertedHTML),\n  ])\n}\n\ntype ContinueDynamicPrerenderOptions = {\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueDynamicPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      .pipeThrough(createStripDocumentClosingTagsTransform())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n  )\n}\n\ntype ContinueStaticPrerenderOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueStaticPrerender(\n  prerenderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueStaticPrerenderOptions\n) {\n  return (\n    prerenderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to head\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\ntype ContinueResumeOptions = {\n  inlinedDataStream: ReadableStream<Uint8Array>\n  getServerInsertedHTML: () => Promise<string>\n  getServerInsertedMetadata: () => Promise<string>\n}\n\nexport async function continueDynamicHTMLResume(\n  renderStream: ReadableStream<Uint8Array>,\n  {\n    inlinedDataStream,\n    getServerInsertedHTML,\n    getServerInsertedMetadata,\n  }: ContinueResumeOptions\n) {\n  return (\n    renderStream\n      // Buffer everything to avoid flushing too frequently\n      .pipeThrough(createBufferedTransformStream())\n      // Insert generated tags to head\n      .pipeThrough(createHeadInsertionTransformStream(getServerInsertedHTML))\n      // Insert generated metadata to body\n      .pipeThrough(\n        createHeadInsertionTransformStream(getServerInsertedMetadata)\n      )\n      // Insert the inlined data (Flight data, form state, etc.) stream into the HTML\n      .pipeThrough(createMergedTransformStream(inlinedDataStream))\n      // Close tags should always be deferred to the end\n      .pipeThrough(createMoveSuffixStream())\n  )\n}\n\nexport function createDocumentClosingStream(): ReadableStream<Uint8Array> {\n  return streamFromString(CLOSE_TAG)\n}\n", "/* eslint-disable no-redeclare */\nimport type { IncomingMessage } from 'http'\nimport type { ParsedUrlQuery } from 'querystring'\nimport type { UrlWithParsedQuery } from 'url'\nimport type { BaseNextRequest } from './base-http'\nimport type { CloneableBody } from './body-streams'\nimport type { RouteMatch } from './route-matches/route-match'\nimport type { NEXT_RSC_UNION_QUERY } from '../client/components/app-router-headers'\nimport type { ServerComponentsHmrCache } from './response-cache'\n\n// FIXME: (wyattjoh) this is a temporary solution to allow us to pass data between bundled modules\nexport const NEXT_REQUEST_META = Symbol.for('NextInternalRequestMeta')\n\nexport type NextIncomingMessage = (BaseNextRequest | IncomingMessage) & {\n  [NEXT_REQUEST_META]?: RequestMeta\n}\n\nexport interface RequestMeta {\n  /**\n   * The query that was used to make the request.\n   */\n  initQuery?: ParsedUrlQuery\n\n  /**\n   * The URL that was used to make the request.\n   */\n  initURL?: string\n\n  /**\n   * The protocol that was used to make the request.\n   */\n  initProtocol?: string\n\n  /**\n   * The body that was read from the request. This is used to allow the body to\n   * be read multiple times.\n   */\n  clonableBody?: CloneableBody\n\n  /**\n   * True when the request matched a locale domain that was configured in the\n   * next.config.js file.\n   */\n  isLocaleDomain?: boolean\n\n  /**\n   * True when the request had locale information stripped from the pathname\n   * part of the URL.\n   */\n  didStripLocale?: boolean\n\n  /**\n   * If the request had it's URL rewritten, this is the URL it was rewritten to.\n   */\n  rewroteURL?: string\n\n  /**\n   * The cookies that were added by middleware and were added to the response.\n   */\n  middlewareCookie?: string[]\n\n  /**\n   * The match on the request for a given route.\n   */\n  match?: RouteMatch\n\n  /**\n   * The incremental cache to use for the request.\n   */\n  incrementalCache?: any\n\n  /**\n   * The server components HMR cache, only for dev.\n   */\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n\n  /**\n   * Equals the segment path that was used for the prefetch RSC request.\n   */\n  segmentPrefetchRSCRequest?: string\n\n  /**\n   * True when the request is for the prefetch flight data.\n   */\n  isPrefetchRSCRequest?: true\n\n  /**\n   * True when the request is for the flight data.\n   */\n  isRSCRequest?: true\n\n  /**\n   * True when the request is for the `/_next/data` route using the pages\n   * router.\n   */\n  isNextDataReq?: true\n\n  /**\n   * Postponed state to use for resumption. If present it's assumed that the\n   * request is for a page that has postponed (there are no guarantees that the\n   * page actually has postponed though as it would incur an additional cache\n   * lookup).\n   */\n  postponed?: string\n\n  /**\n   * If provided, this will be called when a response cache entry was generated\n   * or looked up in the cache.\n   */\n  onCacheEntry?: (\n    cacheEntry: any,\n    requestMeta: any\n  ) => Promise<boolean | void> | boolean | void\n\n  /**\n   * The previous revalidate before rendering 404 page for notFound: true\n   */\n  notFoundRevalidate?: number | false\n\n  /**\n   * In development, the original source page that returned a 404.\n   */\n  developmentNotFoundSourcePage?: string\n\n  /**\n   * The path we routed to and should be invoked\n   */\n  invokePath?: string\n\n  /**\n   * The specific page output we should be matching\n   */\n  invokeOutput?: string\n\n  /**\n   * The status we are invoking the request with from routing\n   */\n  invokeStatus?: number\n\n  /**\n   * The routing error we are invoking with\n   */\n  invokeError?: Error\n\n  /**\n   * The query parsed for the invocation\n   */\n  invokeQuery?: Record<string, undefined | string | string[]>\n\n  /**\n   * Whether the request is a middleware invocation\n   */\n  middlewareInvoke?: boolean\n\n  /**\n   * Whether the default route matches were set on the request during routing.\n   */\n  didSetDefaultRouteMatches?: boolean\n\n  /**\n   * Whether the request is for the custom error page.\n   */\n  customErrorRender?: true\n\n  /**\n   * Whether to bubble up the NoFallbackError to the caller when a 404 is\n   * returned.\n   */\n  bubbleNoFallback?: true\n\n  /**\n   * True when the request had locale information inferred from the default\n   * locale.\n   */\n  localeInferredFromDefault?: true\n\n  /**\n   * The locale that was inferred or explicitly set for the request.\n   */\n  locale?: string\n\n  /**\n   * The default locale that was inferred or explicitly set for the request.\n   */\n  defaultLocale?: string\n}\n\n/**\n * Gets the request metadata. If no key is provided, the entire metadata object\n * is returned.\n *\n * @param req the request to get the metadata from\n * @param key the key to get from the metadata (optional)\n * @returns the value for the key or the entire metadata object\n */\nexport function getRequestMeta(\n  req: NextIncomingMessage,\n  key?: undefined\n): RequestMeta\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key: K\n): RequestMeta[K]\nexport function getRequestMeta<K extends keyof RequestMeta>(\n  req: NextIncomingMessage,\n  key?: K\n): RequestMeta | RequestMeta[K] {\n  const meta = req[NEXT_REQUEST_META] || {}\n  return typeof key === 'string' ? meta[key] : meta\n}\n\n/**\n * Sets the request metadata.\n *\n * @param req the request to set the metadata on\n * @param meta the metadata to set\n * @returns the mutated request metadata\n */\nexport function setRequestMeta(req: NextIncomingMessage, meta: RequestMeta) {\n  req[NEXT_REQUEST_META] = meta\n  return meta\n}\n\n/**\n * Adds a value to the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to set\n * @param value the value to set\n * @returns the mutated request metadata\n */\nexport function addRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K,\n  value: RequestMeta[K]\n) {\n  const meta = getRequestMeta(request)\n  meta[key] = value\n  return setRequestMeta(request, meta)\n}\n\n/**\n * Removes a key from the request metadata.\n *\n * @param request the request to mutate\n * @param key the key to remove\n * @returns the mutated request metadata\n */\nexport function removeRequestMeta<K extends keyof RequestMeta>(\n  request: NextIncomingMessage,\n  key: K\n) {\n  const meta = getRequestMeta(request)\n  delete meta[key]\n  return setRequestMeta(request, meta)\n}\n\ntype NextQueryMetadata = {\n  /**\n   * The `_rsc` query parameter used for cache busting to ensure that the RSC\n   * requests do not get cached by the browser explicitly.\n   */\n  [NEXT_RSC_UNION_QUERY]?: string\n}\n\nexport type NextParsedUrlQuery = ParsedUrlQuery &\n  NextQueryMetadata & {\n    amp?: '1'\n  }\n\nexport interface NextUrlWithParsedQuery extends UrlWithParsedQuery {\n  query: NextParsedUrlQuery\n}\n", "export interface PathLocale {\n  detectedLocale?: string\n  pathname: string\n}\n\n/**\n * A cache of lowercased locales for each list of locales. This is stored as a\n * WeakMap so if the locales are garbage collected, the cache entry will be\n * removed as well.\n */\nconst cache = new WeakMap<readonly string[], readonly string[]>()\n\n/**\n * For a pathname that may include a locale from a list of locales, it\n * removes the locale from the pathname returning it alongside with the\n * detected locale.\n *\n * @param pathname A pathname that may include a locale.\n * @param locales A list of locales.\n * @returns The detected locale and pathname without locale\n */\nexport function normalizeLocalePath(\n  pathname: string,\n  locales?: readonly string[]\n): PathLocale {\n  // If locales is undefined, return the pathname as is.\n  if (!locales) return { pathname }\n\n  // Get the cached lowercased locales or create a new cache entry.\n  let lowercasedLocales = cache.get(locales)\n  if (!lowercasedLocales) {\n    lowercasedLocales = locales.map((locale) => locale.toLowerCase())\n    cache.set(locales, lowercasedLocales)\n  }\n\n  let detectedLocale: string | undefined\n\n  // The first segment will be empty, because it has a leading `/`. If\n  // there is no further segment, there is no locale (or it's the default).\n  const segments = pathname.split('/', 2)\n\n  // If there's no second segment (ie, the pathname is just `/`), there's no\n  // locale.\n  if (!segments[1]) return { pathname }\n\n  // The second segment will contain the locale part if any.\n  const segment = segments[1].toLowerCase()\n\n  // See if the segment matches one of the locales. If it doesn't, there is\n  // no locale (or it's the default).\n  const index = lowercasedLocales.indexOf(segment)\n  if (index < 0) return { pathname }\n\n  // Return the case-sensitive locale.\n  detectedLocale = locales[index]\n\n  // Remove the `/${locale}` part of the pathname.\n  pathname = pathname.slice(detectedLocale.length + 1) || '/'\n\n  return { pathname, detectedLocale }\n}\n", "import type { OutgoingHttpHeaders } from 'http'\nimport type { DomainLocale, I18NConfig } from '../config-shared'\nimport type { I18NProvider } from '../lib/i18n-provider'\n\nimport { detectDomainLocale } from '../../shared/lib/i18n/detect-domain-locale'\nimport { formatNextPathnameInfo } from '../../shared/lib/router/utils/format-next-pathname-info'\nimport { getHostname } from '../../shared/lib/get-hostname'\nimport { getNextPathnameInfo } from '../../shared/lib/router/utils/get-next-pathname-info'\n\ninterface Options {\n  base?: string | URL\n  headers?: OutgoingHttpHeaders\n  forceLocale?: boolean\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  i18nProvider?: I18NProvider\n}\n\nconst REGEX_LOCALHOST_HOSTNAME =\n  /(?!^https?:\\/\\/)(127(?:\\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\\[::1\\]|localhost)/\n\nfunction parseURL(url: string | URL, base?: string | URL) {\n  return new URL(\n    String(url).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost'),\n    base && String(base).replace(REGEX_LOCALHOST_HOSTNAME, 'localhost')\n  )\n}\n\nconst Internal = Symbol('NextURLInternal')\n\nexport class NextURL {\n  private [Internal]: {\n    basePath: string\n    buildId?: string\n    flightSearchParameters?: Record<string, string>\n    defaultLocale?: string\n    domainLocale?: DomainLocale\n    locale?: string\n    options: Options\n    trailingSlash?: boolean\n    url: URL\n  }\n\n  constructor(input: string | URL, base?: string | URL, opts?: Options)\n  constructor(input: string | URL, opts?: Options)\n  constructor(\n    input: string | URL,\n    baseOrOpts?: string | URL | Options,\n    opts?: Options\n  ) {\n    let base: undefined | string | URL\n    let options: Options\n\n    if (\n      (typeof baseOrOpts === 'object' && 'pathname' in baseOrOpts) ||\n      typeof baseOrOpts === 'string'\n    ) {\n      base = baseOrOpts\n      options = opts || {}\n    } else {\n      options = opts || baseOrOpts || {}\n    }\n\n    this[Internal] = {\n      url: parseURL(input, base ?? options.base),\n      options: options,\n      basePath: '',\n    }\n\n    this.analyze()\n  }\n\n  private analyze() {\n    const info = getNextPathnameInfo(this[Internal].url.pathname, {\n      nextConfig: this[Internal].options.nextConfig,\n      parseData: !process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE,\n      i18nProvider: this[Internal].options.i18nProvider,\n    })\n\n    const hostname = getHostname(\n      this[Internal].url,\n      this[Internal].options.headers\n    )\n    this[Internal].domainLocale = this[Internal].options.i18nProvider\n      ? this[Internal].options.i18nProvider.detectDomainLocale(hostname)\n      : detectDomainLocale(\n          this[Internal].options.nextConfig?.i18n?.domains,\n          hostname\n        )\n\n    const defaultLocale =\n      this[Internal].domainLocale?.defaultLocale ||\n      this[Internal].options.nextConfig?.i18n?.defaultLocale\n\n    this[Internal].url.pathname = info.pathname\n    this[Internal].defaultLocale = defaultLocale\n    this[Internal].basePath = info.basePath ?? ''\n    this[Internal].buildId = info.buildId\n    this[Internal].locale = info.locale ?? defaultLocale\n    this[Internal].trailingSlash = info.trailingSlash\n  }\n\n  private formatPathname() {\n    return formatNextPathnameInfo({\n      basePath: this[Internal].basePath,\n      buildId: this[Internal].buildId,\n      defaultLocale: !this[Internal].options.forceLocale\n        ? this[Internal].defaultLocale\n        : undefined,\n      locale: this[Internal].locale,\n      pathname: this[Internal].url.pathname,\n      trailingSlash: this[Internal].trailingSlash,\n    })\n  }\n\n  private formatSearch() {\n    return this[Internal].url.search\n  }\n\n  public get buildId() {\n    return this[Internal].buildId\n  }\n\n  public set buildId(buildId: string | undefined) {\n    this[Internal].buildId = buildId\n  }\n\n  public get locale() {\n    return this[Internal].locale ?? ''\n  }\n\n  public set locale(locale: string) {\n    if (\n      !this[Internal].locale ||\n      !this[Internal].options.nextConfig?.i18n?.locales.includes(locale)\n    ) {\n      throw new TypeError(\n        `The NextURL configuration includes no locale \"${locale}\"`\n      )\n    }\n\n    this[Internal].locale = locale\n  }\n\n  get defaultLocale() {\n    return this[Internal].defaultLocale\n  }\n\n  get domainLocale() {\n    return this[Internal].domainLocale\n  }\n\n  get searchParams() {\n    return this[Internal].url.searchParams\n  }\n\n  get host() {\n    return this[Internal].url.host\n  }\n\n  set host(value: string) {\n    this[Internal].url.host = value\n  }\n\n  get hostname() {\n    return this[Internal].url.hostname\n  }\n\n  set hostname(value: string) {\n    this[Internal].url.hostname = value\n  }\n\n  get port() {\n    return this[Internal].url.port\n  }\n\n  set port(value: string) {\n    this[Internal].url.port = value\n  }\n\n  get protocol() {\n    return this[Internal].url.protocol\n  }\n\n  set protocol(value: string) {\n    this[Internal].url.protocol = value\n  }\n\n  get href() {\n    const pathname = this.formatPathname()\n    const search = this.formatSearch()\n    return `${this.protocol}//${this.host}${pathname}${search}${this.hash}`\n  }\n\n  set href(url: string) {\n    this[Internal].url = parseURL(url)\n    this.analyze()\n  }\n\n  get origin() {\n    return this[Internal].url.origin\n  }\n\n  get pathname() {\n    return this[Internal].url.pathname\n  }\n\n  set pathname(value: string) {\n    this[Internal].url.pathname = value\n  }\n\n  get hash() {\n    return this[Internal].url.hash\n  }\n\n  set hash(value: string) {\n    this[Internal].url.hash = value\n  }\n\n  get search() {\n    return this[Internal].url.search\n  }\n\n  set search(value: string) {\n    this[Internal].url.search = value\n  }\n\n  get password() {\n    return this[Internal].url.password\n  }\n\n  set password(value: string) {\n    this[Internal].url.password = value\n  }\n\n  get username() {\n    return this[Internal].url.username\n  }\n\n  set username(value: string) {\n    this[Internal].url.username = value\n  }\n\n  get basePath() {\n    return this[Internal].basePath\n  }\n\n  set basePath(value: string) {\n    this[Internal].basePath = value.startsWith('/') ? value : `/${value}`\n  }\n\n  toString() {\n    return this.href\n  }\n\n  toJSON() {\n    return this.href\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      href: this.href,\n      origin: this.origin,\n      protocol: this.protocol,\n      username: this.username,\n      password: this.password,\n      host: this.host,\n      hostname: this.hostname,\n      port: this.port,\n      pathname: this.pathname,\n      search: this.search,\n      searchParams: this.searchParams,\n      hash: this.hash,\n    }\n  }\n\n  clone() {\n    return new NextURL(String(this), this[Internal].options)\n  }\n}\n", "import { normalizeLocalePath } from '../../i18n/normalize-locale-path'\nimport { removePathPrefix } from './remove-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\nimport type { I18NProvider } from '../../../../server/lib/i18n-provider'\n\nexport interface NextPathnameInfo {\n  /**\n   * The base path in case the pathname included it.\n   */\n  basePath?: string\n  /**\n   * The buildId for when the parsed URL is a data URL. Parsing it can be\n   * disabled with the `parseData` option.\n   */\n  buildId?: string\n  /**\n   * If there was a locale in the pathname, this will hold its value.\n   */\n  locale?: string\n  /**\n   * The processed pathname without a base path, locale, or data URL elements\n   * when parsing it is enabled.\n   */\n  pathname: string\n  /**\n   * A boolean telling if the pathname had a trailingSlash. This can be only\n   * true if trailingSlash is enabled.\n   */\n  trailingSlash?: boolean\n}\n\ninterface Options {\n  /**\n   * When passed to true, this function will also parse Nextjs data URLs.\n   */\n  parseData?: boolean\n  /**\n   * A partial of the Next.js configuration to parse the URL.\n   */\n  nextConfig?: {\n    basePath?: string\n    i18n?: { locales?: readonly string[] } | null\n    trailingSlash?: boolean\n  }\n\n  /**\n   * If provided, this normalizer will be used to detect the locale instead of\n   * the default locale detection.\n   */\n  i18nProvider?: I18NProvider\n}\n\nexport function getNextPathnameInfo(\n  pathname: string,\n  options: Options\n): NextPathnameInfo {\n  const { basePath, i18n, trailingSlash } = options.nextConfig ?? {}\n  const info: NextPathnameInfo = {\n    pathname,\n    trailingSlash: pathname !== '/' ? pathname.endsWith('/') : trailingSlash,\n  }\n\n  if (basePath && pathHasPrefix(info.pathname, basePath)) {\n    info.pathname = removePathPrefix(info.pathname, basePath)\n    info.basePath = basePath\n  }\n  let pathnameNoDataPrefix = info.pathname\n\n  if (\n    info.pathname.startsWith('/_next/data/') &&\n    info.pathname.endsWith('.json')\n  ) {\n    const paths = info.pathname\n      .replace(/^\\/_next\\/data\\//, '')\n      .replace(/\\.json$/, '')\n      .split('/')\n\n    const buildId = paths[0]\n    info.buildId = buildId\n    pathnameNoDataPrefix =\n      paths[1] !== 'index' ? `/${paths.slice(1).join('/')}` : '/'\n\n    // update pathname with normalized if enabled although\n    // we use normalized to populate locale info still\n    if (options.parseData === true) {\n      info.pathname = pathnameNoDataPrefix\n    }\n  }\n\n  // If provided, use the locale route normalizer to detect the locale instead\n  // of the function below.\n  if (i18n) {\n    let result = options.i18nProvider\n      ? options.i18nProvider.analyze(info.pathname)\n      : normalizeLocalePath(info.pathname, i18n.locales)\n\n    info.locale = result.detectedLocale\n    info.pathname = result.pathname ?? info.pathname\n\n    if (!result.detectedLocale && info.buildId) {\n      result = options.i18nProvider\n        ? options.i18nProvider.analyze(pathnameNoDataPrefix)\n        : normalizeLocalePath(pathnameNoDataPrefix, i18n.locales)\n\n      if (result.detectedLocale) {\n        info.locale = result.detectedLocale\n      }\n    }\n  }\n  return info\n}\n", "import { pathHasPrefix } from './path-has-prefix'\n\n/**\n * Given a path and a prefix it will remove the prefix when it exists in the\n * given path. It ensures it matches exactly without containing extra chars\n * and if the prefix is not there it will be noop.\n *\n * @param path The path to remove the prefix from.\n * @param prefix The prefix to be removed.\n */\nexport function removePathPrefix(path: string, prefix: string): string {\n  // If the path doesn't start with the prefix we can return it as is. This\n  // protects us from situations where the prefix is a substring of the path\n  // prefix such as:\n  //\n  // For prefix: /blog\n  //\n  //   /blog -> true\n  //   /blog/ -> true\n  //   /blog/1 -> true\n  //   /blogging -> false\n  //   /blogging/ -> false\n  //   /blogging/1 -> false\n  if (!pathHasPrefix(path, prefix)) {\n    return path\n  }\n\n  // Remove the prefix from the path via slicing.\n  const withoutPrefix = path.slice(prefix.length)\n\n  // If the path without the prefix starts with a `/` we can return it as is.\n  if (withoutPrefix.startsWith('/')) {\n    return withoutPrefix\n  }\n\n  // If the path without the prefix doesn't start with a `/` we need to add it\n  // back to the path to make sure it's a valid path.\n  return `/${withoutPrefix}`\n}\n", "import type { OutgoingHttpHeaders } from 'http'\n\n/**\n * Takes an object with a hostname property (like a parsed URL) and some\n * headers that may contain Host and returns the preferred hostname.\n * @param parsed An object containing a hostname property.\n * @param headers A dictionary with headers containing a `host`.\n */\nexport function getHostname(\n  parsed: { hostname?: string | null },\n  headers?: OutgoingHttpHeaders\n): string | undefined {\n  // Get the hostname from the headers if it exists, otherwise use the parsed\n  // hostname.\n  let hostname: string\n  if (headers?.host && !Array.isArray(headers.host)) {\n    hostname = headers.host.toString().split(':', 1)[0]\n  } else if (parsed.hostname) {\n    hostname = parsed.hostname\n  } else return\n\n  return hostname.toLowerCase()\n}\n", "import type { DomainLocale } from '../../../server/config-shared'\n\nexport function detectDomainLocale(\n  domainItems?: readonly DomainLocale[],\n  hostname?: string,\n  detectedLocale?: string\n) {\n  if (!domainItems) return\n\n  if (detectedLocale) {\n    detectedLocale = detectedLocale.toLowerCase()\n  }\n\n  for (const item of domainItems) {\n    // remove port if present\n    const domainHostname = item.domain?.split(':', 1)[0].toLowerCase()\n    if (\n      hostname === domainHostname ||\n      detectedLocale === item.defaultLocale.toLowerCase() ||\n      item.locales?.some((locale) => locale.toLowerCase() === detectedLocale)\n    ) {\n      return item\n    }\n  }\n}\n", "import type { NextPathnameInfo } from './get-next-pathname-info'\nimport { removeTrailingSlash } from './remove-trailing-slash'\nimport { addPathPrefix } from './add-path-prefix'\nimport { addPathSuffix } from './add-path-suffix'\nimport { addLocale } from './add-locale'\n\ninterface ExtendedInfo extends NextPathnameInfo {\n  defaultLocale?: string\n  ignorePrefix?: boolean\n}\n\nexport function formatNextPathnameInfo(info: ExtendedInfo) {\n  let pathname = addLocale(\n    info.pathname,\n    info.locale,\n    info.buildId ? undefined : info.defaultLocale,\n    info.ignorePrefix\n  )\n\n  if (info.buildId || !info.trailingSlash) {\n    pathname = removeTrailingSlash(pathname)\n  }\n\n  if (info.buildId) {\n    pathname = addPathSuffix(\n      addPathPrefix(pathname, `/_next/data/${info.buildId}`),\n      info.pathname === '/' ? 'index.json' : '.json'\n    )\n  }\n\n  pathname = addPathPrefix(pathname, info.basePath)\n  return !info.buildId && info.trailingSlash\n    ? !pathname.endsWith('/')\n      ? addPathSuffix(pathname, '/')\n      : pathname\n    : removeTrailingSlash(pathname)\n}\n", "import { addPathPrefix } from './add-path-prefix'\nimport { pathHasPrefix } from './path-has-prefix'\n\n/**\n * For a given path and a locale, if the locale is given, it will prefix the\n * locale. The path shouldn't be an API path. If a default locale is given the\n * prefix will be omitted if the locale is already the default locale.\n */\nexport function addLocale(\n  path: string,\n  locale?: string | false,\n  defaultLocale?: string,\n  ignorePrefix?: boolean\n) {\n  // If no locale was given or the locale is the default locale, we don't need\n  // to prefix the path.\n  if (!locale || locale === defaultLocale) return path\n\n  const lower = path.toLowerCase()\n\n  // If the path is an API path or the path already has the locale prefix, we\n  // don't need to prefix the path.\n  if (!ignorePrefix) {\n    if (pathHasPrefix(lower, '/api')) return path\n    if (pathHasPrefix(lower, `/${locale.toLowerCase()}`)) return path\n  }\n\n  // Add the locale prefix to the path.\n  return addPathPrefix(path, `/${locale}`)\n}\n", "import type { I18NConfig } from '../../config-shared'\nimport { NextURL } from '../next-url'\nimport { toNodeOutgoingHttpHeaders, validateURL } from '../utils'\nimport { RemovedUAError, RemovedPageError } from '../error'\nimport { RequestCookies } from './cookies'\n\nexport const INTERNALS = Symbol('internal request')\n\n/**\n * This class extends the [Web `Request` API](https://developer.mozilla.org/docs/Web/API/Request) with additional convenience methods.\n *\n * Read more: [Next.js Docs: `NextRequest`](https://nextjs.org/docs/app/api-reference/functions/next-request)\n */\nexport class NextRequest extends Request {\n  [INTERNALS]: {\n    cookies: RequestCookies\n    url: string\n    nextUrl: NextURL\n  }\n\n  constructor(input: URL | RequestInfo, init: RequestInit = {}) {\n    const url =\n      typeof input !== 'string' && 'url' in input ? input.url : String(input)\n\n    validateURL(url)\n\n    // node Request instance requires duplex option when a body\n    // is present or it errors, we don't handle this for\n    // Request being passed in since it would have already\n    // errored if this wasn't configured\n    if (process.env.NEXT_RUNTIME !== 'edge') {\n      if (init.body && init.duplex !== 'half') {\n        init.duplex = 'half'\n      }\n    }\n\n    if (input instanceof Request) super(input, init)\n    else super(url, init)\n\n    const nextUrl = new NextURL(url, {\n      headers: toNodeOutgoingHttpHeaders(this.headers),\n      nextConfig: init.nextConfig,\n    })\n    this[INTERNALS] = {\n      cookies: new RequestCookies(this.headers),\n      nextUrl,\n      url: process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n        ? url\n        : nextUrl.toString(),\n    }\n  }\n\n  [Symbol.for('edge-runtime.inspect.custom')]() {\n    return {\n      cookies: this.cookies,\n      nextUrl: this.nextUrl,\n      url: this.url,\n      // rest of props come from Request\n      bodyUsed: this.bodyUsed,\n      cache: this.cache,\n      credentials: this.credentials,\n      destination: this.destination,\n      headers: Object.fromEntries(this.headers),\n      integrity: this.integrity,\n      keepalive: this.keepalive,\n      method: this.method,\n      mode: this.mode,\n      redirect: this.redirect,\n      referrer: this.referrer,\n      referrerPolicy: this.referrerPolicy,\n      signal: this.signal,\n    }\n  }\n\n  public get cookies() {\n    return this[INTERNALS].cookies\n  }\n\n  public get nextUrl() {\n    return this[INTERNALS].nextUrl\n  }\n\n  /**\n   * @deprecated\n   * `page` has been deprecated in favour of `URLPattern`.\n   * Read more: https://nextjs.org/docs/messages/middleware-request-page\n   */\n  public get page() {\n    throw new RemovedPageError()\n  }\n\n  /**\n   * @deprecated\n   * `ua` has been removed in favour of \\`userAgent\\` function.\n   * Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent\n   */\n  public get ua() {\n    throw new RemovedUAError()\n  }\n\n  public get url() {\n    return this[INTERNALS].url\n  }\n}\n\nexport interface RequestInit extends globalThis.RequestInit {\n  nextConfig?: {\n    basePath?: string\n    i18n?: I18NConfig | null\n    trailingSlash?: boolean\n  }\n  signal?: AbortSignal\n  // see https://github.com/whatwg/fetch/pull/1457\n  duplex?: 'half'\n}\n", "export type ScheduledFn<T = void> = () => T | PromiseLike<T>\nexport type SchedulerFn<T = void> = (cb: ScheduledFn<T>) => void\n\n/**\n * Schedules a function to be called on the next tick after the other promises\n * have been resolved.\n *\n * @param cb the function to schedule\n */\nexport const scheduleOnNextTick = <T = void>(cb: ScheduledFn<T>): void => {\n  // We use Promise.resolve().then() here so that the operation is scheduled at\n  // the end of the promise job queue, we then add it to the next process tick\n  // to ensure it's evaluated afterwards.\n  //\n  // This was inspired by the implementation of the DataLoader interface: https://github.com/graphql/dataloader/blob/d336bd15282664e0be4b4a657cb796f09bafbc6b/src/index.js#L213-L255\n  //\n  Promise.resolve().then(() => {\n    if (process.env.NEXT_RUNTIME === 'edge') {\n      setTimeout(cb, 0)\n    } else {\n      process.nextTick(cb)\n    }\n  })\n}\n\n/**\n * Schedules a function to be called using `setImmediate` or `setTimeout` if\n * `setImmediate` is not available (like in the Edge runtime).\n *\n * @param cb the function to schedule\n */\nexport const scheduleImmediate = <T = void>(cb: ScheduledFn<T>): void => {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    setTimeout(cb, 0)\n  } else {\n    setImmediate(cb)\n  }\n}\n\n/**\n * returns a promise than resolves in a future task. There is no guarantee that the task it resolves in\n * will be the next task but if you await it you can at least be sure that the current task is over and\n * most usefully that the entire microtask queue of the current task has been emptied.\n */\nexport function atLeastOneTask() {\n  return new Promise<void>((resolve) => scheduleImmediate(resolve))\n}\n\n/**\n * This utility function is extracted to make it easier to find places where we are doing\n * specific timing tricks to try to schedule work after React has rendered. This is especially\n * important at the moment because Next.js uses the edge builds of React which use setTimeout to\n * schedule work when you might expect that something like setImmediate would do the trick.\n *\n * Long term we should switch to the node versions of React rendering when possible and then\n * update this to use setImmediate rather than setTimeout\n */\nexport function waitAtLeastOneReactRenderTask(): Promise<void> {\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    return new Promise((r) => setTimeout(r, 0))\n  } else {\n    return new Promise((r) => setImmediate(r))\n  }\n}\n", "import type {\n  WorkAsyncStorage,\n  WorkStore,\n} from '../app-render/work-async-storage.external'\n\nimport { AppRenderSpan, NextNodeServerSpan } from './trace/constants'\nimport { getTracer, SpanKind } from './trace/tracer'\nimport {\n  CACHE_ONE_YEAR,\n  INFINITE_CACHE,\n  NEXT_CACHE_TAG_MAX_ITEMS,\n  NEXT_CACHE_TAG_MAX_LENGTH,\n} from '../../lib/constants'\nimport { markCurrentScopeAsDynamic } from '../app-render/dynamic-rendering'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport type { FetchMetric } from '../base-http'\nimport { createDedupeFetch } from './dedupe-fetch'\nimport type { WorkUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport {\n  CachedRouteKind,\n  IncrementalCacheKind,\n  type CachedFetchData,\n} from '../response-cache'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport { cloneResponse } from './clone-response'\n\nconst isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n\ntype Fetcher = typeof fetch\n\ntype PatchedFetcher = Fetcher & {\n  readonly __nextPatched: true\n  readonly __nextGetStaticStore: () => WorkAsyncStorage\n  readonly _nextOriginalFetch: Fetcher\n}\n\nexport const NEXT_PATCH_SYMBOL = Symbol.for('next-patch')\n\nfunction isFetchPatched() {\n  return (globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] === true\n}\n\nexport function validateRevalidate(\n  revalidateVal: unknown,\n  route: string\n): undefined | number {\n  try {\n    let normalizedRevalidate: number | undefined = undefined\n\n    if (revalidateVal === false) {\n      normalizedRevalidate = INFINITE_CACHE\n    } else if (\n      typeof revalidateVal === 'number' &&\n      !isNaN(revalidateVal) &&\n      revalidateVal > -1\n    ) {\n      normalizedRevalidate = revalidateVal\n    } else if (typeof revalidateVal !== 'undefined') {\n      throw new Error(\n        `Invalid revalidate value \"${revalidateVal}\" on \"${route}\", must be a non-negative number or false`\n      )\n    }\n    return normalizedRevalidate\n  } catch (err: any) {\n    // handle client component error from attempting to check revalidate value\n    if (err instanceof Error && err.message.includes('Invalid revalidate')) {\n      throw err\n    }\n    return undefined\n  }\n}\n\nexport function validateTags(tags: any[], description: string) {\n  const validTags: string[] = []\n  const invalidTags: Array<{\n    tag: any\n    reason: string\n  }> = []\n\n  for (let i = 0; i < tags.length; i++) {\n    const tag = tags[i]\n\n    if (typeof tag !== 'string') {\n      invalidTags.push({ tag, reason: 'invalid type, must be a string' })\n    } else if (tag.length > NEXT_CACHE_TAG_MAX_LENGTH) {\n      invalidTags.push({\n        tag,\n        reason: `exceeded max length of ${NEXT_CACHE_TAG_MAX_LENGTH}`,\n      })\n    } else {\n      validTags.push(tag)\n    }\n\n    if (validTags.length > NEXT_CACHE_TAG_MAX_ITEMS) {\n      console.warn(\n        `Warning: exceeded max tag count for ${description}, dropped tags:`,\n        tags.slice(i).join(', ')\n      )\n      break\n    }\n  }\n\n  if (invalidTags.length > 0) {\n    console.warn(`Warning: invalid tags passed to ${description}: `)\n\n    for (const { tag, reason } of invalidTags) {\n      console.log(`tag: \"${tag}\" ${reason}`)\n    }\n  }\n  return validTags\n}\n\nfunction trackFetchMetric(\n  workStore: WorkStore,\n  ctx: Omit<FetchMetric, 'end' | 'idx'>\n) {\n  // If the static generation store is not available, we can't track the fetch\n  if (!workStore) return\n  if (workStore.requestEndedState?.ended) return\n\n  const isDebugBuild =\n    (!!process.env.NEXT_DEBUG_BUILD ||\n      process.env.NEXT_SSG_FETCH_METRICS === '1') &&\n    workStore.isStaticGeneration\n  const isDevelopment = process.env.NODE_ENV === 'development'\n\n  if (\n    // The only time we want to track fetch metrics outside of development is when\n    // we are performing a static generation & we are in debug mode.\n    !isDebugBuild &&\n    !isDevelopment\n  ) {\n    return\n  }\n\n  workStore.fetchMetrics ??= []\n\n  workStore.fetchMetrics.push({\n    ...ctx,\n    end: performance.timeOrigin + performance.now(),\n    idx: workStore.nextFetchId || 0,\n  })\n}\n\ninterface PatchableModule {\n  workAsyncStorage: WorkAsyncStorage\n  workUnitAsyncStorage: WorkUnitAsyncStorage\n}\n\nexport function createPatchedFetcher(\n  originFetch: Fetcher,\n  { workAsyncStorage, workUnitAsyncStorage }: PatchableModule\n): PatchedFetcher {\n  // Create the patched fetch function. We don't set the type here, as it's\n  // verified as the return value of this function.\n  const patched = async (\n    input: RequestInfo | URL,\n    init: RequestInit | undefined\n  ) => {\n    let url: URL | undefined\n    try {\n      url = new URL(input instanceof Request ? input.url : input)\n      url.username = ''\n      url.password = ''\n    } catch {\n      // Error caused by malformed URL should be handled by native fetch\n      url = undefined\n    }\n    const fetchUrl = url?.href ?? ''\n    const method = init?.method?.toUpperCase() || 'GET'\n\n    // Do create a new span trace for internal fetches in the\n    // non-verbose mode.\n    const isInternal = (init?.next as any)?.internal === true\n    const hideSpan = process.env.NEXT_OTEL_FETCH_DISABLED === '1'\n    // We don't track fetch metrics for internal fetches\n    // so it's not critical that we have a start time, as it won't be recorded.\n    // This is to workaround a flaky issue where performance APIs might\n    // not be available and will require follow-up investigation.\n    const fetchStart: number | undefined = isInternal\n      ? undefined\n      : performance.timeOrigin + performance.now()\n\n    const workStore = workAsyncStorage.getStore()\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    // During static generation we track cache reads so we can reason about when they fill\n    let cacheSignal =\n      workUnitStore && workUnitStore.type === 'prerender'\n        ? workUnitStore.cacheSignal\n        : null\n    if (cacheSignal) {\n      cacheSignal.beginRead()\n    }\n\n    const result = getTracer().trace(\n      isInternal ? NextNodeServerSpan.internalFetch : AppRenderSpan.fetch,\n      {\n        hideSpan,\n        kind: SpanKind.CLIENT,\n        spanName: ['fetch', method, fetchUrl].filter(Boolean).join(' '),\n        attributes: {\n          'http.url': fetchUrl,\n          'http.method': method,\n          'net.peer.name': url?.hostname,\n          'net.peer.port': url?.port || undefined,\n        },\n      },\n      async () => {\n        // If this is an internal fetch, we should not do any special treatment.\n        if (isInternal) {\n          return originFetch(input, init)\n        }\n\n        // If the workStore is not available, we can't do any\n        // special treatment of fetch, therefore fallback to the original\n        // fetch implementation.\n        if (!workStore) {\n          return originFetch(input, init)\n        }\n\n        // We should also fallback to the original fetch implementation if we\n        // are in draft mode, it does not constitute a static generation.\n        if (workStore.isDraftMode) {\n          return originFetch(input, init)\n        }\n\n        const isRequestInput =\n          input &&\n          typeof input === 'object' &&\n          typeof (input as Request).method === 'string'\n\n        const getRequestMeta = (field: string) => {\n          // If request input is present but init is not, retrieve from input first.\n          const value = (init as any)?.[field]\n          return value || (isRequestInput ? (input as any)[field] : null)\n        }\n\n        let finalRevalidate: number | undefined = undefined\n        const getNextField = (field: 'revalidate' | 'tags') => {\n          return typeof init?.next?.[field] !== 'undefined'\n            ? init?.next?.[field]\n            : isRequestInput\n              ? (input as any).next?.[field]\n              : undefined\n        }\n        // RequestInit doesn't keep extra fields e.g. next so it's\n        // only available if init is used separate\n        let currentFetchRevalidate = getNextField('revalidate')\n        const tags: string[] = validateTags(\n          getNextField('tags') || [],\n          `fetch ${input.toString()}`\n        )\n\n        const revalidateStore =\n          workUnitStore &&\n          (workUnitStore.type === 'cache' ||\n            workUnitStore.type === 'prerender' ||\n            workUnitStore.type === 'prerender-ppr' ||\n            workUnitStore.type === 'prerender-legacy')\n            ? workUnitStore\n            : undefined\n\n        if (revalidateStore) {\n          if (Array.isArray(tags)) {\n            // Collect tags onto parent caches or parent prerenders.\n            const collectedTags =\n              revalidateStore.tags ?? (revalidateStore.tags = [])\n            for (const tag of tags) {\n              if (!collectedTags.includes(tag)) {\n                collectedTags.push(tag)\n              }\n            }\n          }\n        }\n\n        const implicitTags =\n          !workUnitStore || workUnitStore.type === 'unstable-cache'\n            ? []\n            : workUnitStore.implicitTags\n\n        // Inside unstable-cache we treat it the same as force-no-store on the\n        // page.\n        const pageFetchCacheMode =\n          workUnitStore && workUnitStore.type === 'unstable-cache'\n            ? 'force-no-store'\n            : workStore.fetchCache\n\n        const isUsingNoStore = !!workStore.isUnstableNoStore\n\n        let currentFetchCacheConfig = getRequestMeta('cache')\n        let cacheReason = ''\n        let cacheWarning: string | undefined\n\n        if (\n          typeof currentFetchCacheConfig === 'string' &&\n          typeof currentFetchRevalidate !== 'undefined'\n        ) {\n          // If the revalidate value conflicts with the cache value, we should warn the user and unset the conflicting values.\n          const isConflictingRevalidate =\n            // revalidate: 0 and cache: force-cache\n            (currentFetchCacheConfig === 'force-cache' &&\n              currentFetchRevalidate === 0) ||\n            // revalidate: >0 or revalidate: false and cache: no-store\n            (currentFetchCacheConfig === 'no-store' &&\n              (currentFetchRevalidate > 0 || currentFetchRevalidate === false))\n\n          if (isConflictingRevalidate) {\n            cacheWarning = `Specified \"cache: ${currentFetchCacheConfig}\" and \"revalidate: ${currentFetchRevalidate}\", only one should be specified.`\n            currentFetchCacheConfig = undefined\n            currentFetchRevalidate = undefined\n          }\n        }\n\n        const hasExplicitFetchCacheOptOut =\n          // fetch config itself signals not to cache\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store' ||\n          // the fetch isn't explicitly caching and the segment level cache config signals not to cache\n          // note: `pageFetchCacheMode` is also set by being in an unstable_cache context.\n          pageFetchCacheMode === 'force-no-store' ||\n          pageFetchCacheMode === 'only-no-store'\n\n        // If no explicit fetch cache mode is set, but dynamic = `force-dynamic` is set,\n        // we shouldn't consider caching the fetch. This is because the `dynamic` cache\n        // is considered a \"top-level\" cache mode, whereas something like `fetchCache` is more\n        // fine-grained. Top-level modes are responsible for setting reasonable defaults for the\n        // other configurations.\n        const noFetchConfigAndForceDynamic =\n          !pageFetchCacheMode &&\n          !currentFetchCacheConfig &&\n          !currentFetchRevalidate &&\n          workStore.forceDynamic\n\n        if (\n          // force-cache was specified without a revalidate value. We set the revalidate value to false\n          // which will signal the cache to not revalidate\n          currentFetchCacheConfig === 'force-cache' &&\n          typeof currentFetchRevalidate === 'undefined'\n        ) {\n          currentFetchRevalidate = false\n        } else if (\n          // if we are inside of \"use cache\"/\"unstable_cache\"\n          // we shouldn't set the revalidate to 0 as it's overridden\n          // by the cache context\n          workUnitStore?.type !== 'cache' &&\n          (hasExplicitFetchCacheOptOut || noFetchConfigAndForceDynamic)\n        ) {\n          currentFetchRevalidate = 0\n        }\n\n        if (\n          currentFetchCacheConfig === 'no-cache' ||\n          currentFetchCacheConfig === 'no-store'\n        ) {\n          cacheReason = `cache: ${currentFetchCacheConfig}`\n        }\n\n        finalRevalidate = validateRevalidate(\n          currentFetchRevalidate,\n          workStore.route\n        )\n\n        const _headers = getRequestMeta('headers')\n        const initHeaders: Headers =\n          typeof _headers?.get === 'function'\n            ? _headers\n            : new Headers(_headers || {})\n\n        const hasUnCacheableHeader =\n          initHeaders.get('authorization') || initHeaders.get('cookie')\n\n        const isUnCacheableMethod = !['get', 'head'].includes(\n          getRequestMeta('method')?.toLowerCase() || 'get'\n        )\n\n        /**\n         * We automatically disable fetch caching under the following conditions:\n         * - Fetch cache configs are not set. Specifically:\n         *    - A page fetch cache mode is not set (export const fetchCache=...)\n         *    - A fetch cache mode is not set in the fetch call (fetch(url, { cache: ... }))\n         *      or the fetch cache mode is set to 'default'\n         *    - A fetch revalidate value is not set in the fetch call (fetch(url, { revalidate: ... }))\n         * - OR the fetch comes after a configuration that triggered dynamic rendering (e.g., reading cookies())\n         *   and the fetch was considered uncacheable (e.g., POST method or has authorization headers)\n         */\n        const hasNoExplicitCacheConfig =\n          // eslint-disable-next-line eqeqeq\n          pageFetchCacheMode == undefined &&\n          // eslint-disable-next-line eqeqeq\n          (currentFetchCacheConfig == undefined ||\n            // when considering whether to opt into the default \"no-cache\" fetch semantics,\n            // a \"default\" cache config should be treated the same as no cache config\n            currentFetchCacheConfig === 'default') &&\n          // eslint-disable-next-line eqeqeq\n          currentFetchRevalidate == undefined\n        const autoNoCache =\n          // this condition is hit for null/undefined\n          // eslint-disable-next-line eqeqeq\n          (hasNoExplicitCacheConfig &&\n            // we disable automatic no caching behavior during build time SSG so that we can still\n            // leverage the fetch cache between SSG workers\n            !workStore.isPrerendering) ||\n          ((hasUnCacheableHeader || isUnCacheableMethod) &&\n            revalidateStore &&\n            revalidateStore.revalidate === 0)\n\n        if (\n          hasNoExplicitCacheConfig &&\n          workUnitStore !== undefined &&\n          workUnitStore.type === 'prerender'\n        ) {\n          // If we have no cache config, and we're in Dynamic I/O prerendering, it'll be a dynamic call.\n          // We don't have to issue that dynamic call.\n          if (cacheSignal) {\n            cacheSignal.endRead()\n            cacheSignal = null\n          }\n          return makeHangingPromise<Response>(\n            workUnitStore.renderSignal,\n            'fetch()'\n          )\n        }\n\n        switch (pageFetchCacheMode) {\n          case 'force-no-store': {\n            cacheReason = 'fetchCache = force-no-store'\n            break\n          }\n          case 'only-no-store': {\n            if (\n              currentFetchCacheConfig === 'force-cache' ||\n              (typeof finalRevalidate !== 'undefined' && finalRevalidate > 0)\n            ) {\n              throw new Error(\n                `cache: 'force-cache' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-no-store'`\n              )\n            }\n            cacheReason = 'fetchCache = only-no-store'\n            break\n          }\n          case 'only-cache': {\n            if (currentFetchCacheConfig === 'no-store') {\n              throw new Error(\n                `cache: 'no-store' used on fetch for ${fetchUrl} with 'export const fetchCache = 'only-cache'`\n              )\n            }\n            break\n          }\n          case 'force-cache': {\n            if (\n              typeof currentFetchRevalidate === 'undefined' ||\n              currentFetchRevalidate === 0\n            ) {\n              cacheReason = 'fetchCache = force-cache'\n              finalRevalidate = INFINITE_CACHE\n            }\n            break\n          }\n          default:\n          // sometimes we won't match the above cases. the reason we don't move\n          // everything to this switch is the use of autoNoCache which is not a fetchCacheMode\n          // I suspect this could be unified with fetchCacheMode however in which case we could\n          // simplify the switch case and ensure we have an exhaustive switch handling all modes\n        }\n\n        if (typeof finalRevalidate === 'undefined') {\n          if (pageFetchCacheMode === 'default-cache' && !isUsingNoStore) {\n            finalRevalidate = INFINITE_CACHE\n            cacheReason = 'fetchCache = default-cache'\n          } else if (pageFetchCacheMode === 'default-no-store') {\n            finalRevalidate = 0\n            cacheReason = 'fetchCache = default-no-store'\n          } else if (isUsingNoStore) {\n            finalRevalidate = 0\n            cacheReason = 'noStore call'\n          } else if (autoNoCache) {\n            finalRevalidate = 0\n            cacheReason = 'auto no cache'\n          } else {\n            // TODO: should we consider this case an invariant?\n            cacheReason = 'auto cache'\n            finalRevalidate = revalidateStore\n              ? revalidateStore.revalidate\n              : INFINITE_CACHE\n          }\n        } else if (!cacheReason) {\n          cacheReason = `revalidate: ${finalRevalidate}`\n        }\n\n        if (\n          // when force static is configured we don't bail from\n          // `revalidate: 0` values\n          !(workStore.forceStatic && finalRevalidate === 0) &&\n          // we don't consider autoNoCache to switch to dynamic for ISR\n          !autoNoCache &&\n          // If the revalidate value isn't currently set or the value is less\n          // than the current revalidate value, we should update the revalidate\n          // value.\n          revalidateStore &&\n          finalRevalidate < revalidateStore.revalidate\n        ) {\n          // If we were setting the revalidate value to 0, we should try to\n          // postpone instead first.\n          if (finalRevalidate === 0) {\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `revalidate: 0 fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          // We only want to set the revalidate store's revalidate time if it\n          // was explicitly set for the fetch call, i.e. currentFetchRevalidate.\n          if (revalidateStore && currentFetchRevalidate === finalRevalidate) {\n            revalidateStore.revalidate = finalRevalidate\n          }\n        }\n\n        const isCacheableRevalidate =\n          typeof finalRevalidate === 'number' && finalRevalidate > 0\n\n        let cacheKey: string | undefined\n        const { incrementalCache } = workStore\n\n        const useCacheOrRequestStore =\n          workUnitStore?.type === 'request' || workUnitStore?.type === 'cache'\n            ? workUnitStore\n            : undefined\n\n        if (\n          incrementalCache &&\n          (isCacheableRevalidate ||\n            useCacheOrRequestStore?.serverComponentsHmrCache)\n        ) {\n          try {\n            cacheKey = await incrementalCache.generateCacheKey(\n              fetchUrl,\n              isRequestInput ? (input as RequestInit) : init\n            )\n          } catch (err) {\n            console.error(`Failed to generate cache key for`, input)\n          }\n        }\n\n        const fetchIdx = workStore.nextFetchId ?? 1\n        workStore.nextFetchId = fetchIdx + 1\n\n        let handleUnlock = () => Promise.resolve()\n\n        const doOriginalFetch = async (\n          isStale?: boolean,\n          cacheReasonOverride?: string\n        ) => {\n          const requestInputFields = [\n            'cache',\n            'credentials',\n            'headers',\n            'integrity',\n            'keepalive',\n            'method',\n            'mode',\n            'redirect',\n            'referrer',\n            'referrerPolicy',\n            'window',\n            'duplex',\n\n            // don't pass through signal when revalidating\n            ...(isStale ? [] : ['signal']),\n          ]\n\n          if (isRequestInput) {\n            const reqInput: Request = input as any\n            const reqOptions: RequestInit = {\n              body: (reqInput as any)._ogBody || reqInput.body,\n            }\n\n            for (const field of requestInputFields) {\n              // @ts-expect-error custom fields\n              reqOptions[field] = reqInput[field]\n            }\n            input = new Request(reqInput.url, reqOptions)\n          } else if (init) {\n            const { _ogBody, body, signal, ...otherInput } =\n              init as RequestInit & { _ogBody?: any }\n            init = {\n              ...otherInput,\n              body: _ogBody || body,\n              signal: isStale ? undefined : signal,\n            }\n          }\n\n          // add metadata to init without editing the original\n          const clonedInit = {\n            ...init,\n            next: { ...init?.next, fetchType: 'origin', fetchIdx },\n          }\n\n          return originFetch(input, clonedInit)\n            .then(async (res) => {\n              if (!isStale && fetchStart) {\n                trackFetchMetric(workStore, {\n                  start: fetchStart,\n                  url: fetchUrl,\n                  cacheReason: cacheReasonOverride || cacheReason,\n                  cacheStatus:\n                    finalRevalidate === 0 || cacheReasonOverride\n                      ? 'skip'\n                      : 'miss',\n                  cacheWarning,\n                  status: res.status,\n                  method: clonedInit.method || 'GET',\n                })\n              }\n              if (\n                res.status === 200 &&\n                incrementalCache &&\n                cacheKey &&\n                (isCacheableRevalidate ||\n                  useCacheOrRequestStore?.serverComponentsHmrCache)\n              ) {\n                const normalizedRevalidate =\n                  finalRevalidate >= INFINITE_CACHE\n                    ? CACHE_ONE_YEAR\n                    : finalRevalidate\n\n                if (workUnitStore && workUnitStore.type === 'prerender') {\n                  // We are prerendering at build time or revalidate time with dynamicIO so we need to\n                  // buffer the response so we can guarantee it can be read in a microtask\n                  const bodyBuffer = await res.arrayBuffer()\n\n                  const fetchedData = {\n                    headers: Object.fromEntries(res.headers.entries()),\n                    body: Buffer.from(bodyBuffer).toString('base64'),\n                    status: res.status,\n                    url: res.url,\n                  }\n\n                  // We can skip checking the serverComponentsHmrCache because we aren't in\n                  // dev mode.\n\n                  await incrementalCache.set(\n                    cacheKey,\n                    {\n                      kind: CachedRouteKind.FETCH,\n                      data: fetchedData,\n                      revalidate: normalizedRevalidate,\n                    },\n                    { fetchCache: true, fetchUrl, fetchIdx, tags }\n                  )\n                  await handleUnlock()\n\n                  // We return a new Response to the caller.\n                  return new Response(bodyBuffer, {\n                    headers: res.headers,\n                    status: res.status,\n                    statusText: res.statusText,\n                  })\n                } else {\n                  // We're cloning the response using this utility because there\n                  // exists a bug in the undici library around response cloning.\n                  // See the following pull request for more details:\n                  // https://github.com/vercel/next.js/pull/73274\n\n                  const [cloned1, cloned2] = cloneResponse(res)\n\n                  // We are dynamically rendering including dev mode. We want to return\n                  // the response to the caller as soon as possible because it might stream\n                  // over a very long time.\n                  cloned1\n                    .arrayBuffer()\n                    .then(async (arrayBuffer) => {\n                      const bodyBuffer = Buffer.from(arrayBuffer)\n\n                      const fetchedData = {\n                        headers: Object.fromEntries(cloned1.headers.entries()),\n                        body: bodyBuffer.toString('base64'),\n                        status: cloned1.status,\n                        url: cloned1.url,\n                      }\n\n                      useCacheOrRequestStore?.serverComponentsHmrCache?.set(\n                        cacheKey,\n                        fetchedData\n                      )\n\n                      if (isCacheableRevalidate) {\n                        await incrementalCache.set(\n                          cacheKey,\n                          {\n                            kind: CachedRouteKind.FETCH,\n                            data: fetchedData,\n                            revalidate: normalizedRevalidate,\n                          },\n                          { fetchCache: true, fetchUrl, fetchIdx, tags }\n                        )\n                      }\n                    })\n                    .catch((error) =>\n                      console.warn(`Failed to set fetch cache`, input, error)\n                    )\n                    .finally(handleUnlock)\n\n                  return cloned2\n                }\n              }\n\n              // we had response that we determined shouldn't be cached so we return it\n              // and don't cache it. This also needs to unlock the cache lock we acquired.\n              await handleUnlock()\n\n              return res\n            })\n            .catch((error) => {\n              handleUnlock()\n              throw error\n            })\n        }\n\n        let cacheReasonOverride\n        let isForegroundRevalidate = false\n        let isHmrRefreshCache = false\n\n        if (cacheKey && incrementalCache) {\n          let cachedFetchData: CachedFetchData | undefined\n\n          if (\n            useCacheOrRequestStore?.isHmrRefresh &&\n            useCacheOrRequestStore.serverComponentsHmrCache\n          ) {\n            cachedFetchData =\n              useCacheOrRequestStore.serverComponentsHmrCache.get(cacheKey)\n\n            isHmrRefreshCache = true\n          }\n\n          if (isCacheableRevalidate && !cachedFetchData) {\n            handleUnlock = await incrementalCache.lock(cacheKey)\n            const entry = workStore.isOnDemandRevalidate\n              ? null\n              : await incrementalCache.get(cacheKey, {\n                  kind: IncrementalCacheKind.FETCH,\n                  revalidate: finalRevalidate,\n                  fetchUrl,\n                  fetchIdx,\n                  tags,\n                  softTags: implicitTags,\n                })\n\n            if (hasNoExplicitCacheConfig) {\n              // We sometimes use the cache to dedupe fetches that do not specify a cache configuration\n              // In these cases we want to make sure we still exclude them from prerenders if dynamicIO is on\n              // so we introduce an artificial Task boundary here.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                await waitAtLeastOneReactRenderTask()\n              }\n            }\n\n            if (entry) {\n              await handleUnlock()\n            } else {\n              // in dev, incremental cache response will be null in case the browser adds `cache-control: no-cache` in the request headers\n              cacheReasonOverride = 'cache-control: no-cache (hard refresh)'\n            }\n\n            if (entry?.value && entry.value.kind === CachedRouteKind.FETCH) {\n              // when stale and is revalidating we wait for fresh data\n              // so the revalidated entry has the updated data\n              if (workStore.isRevalidate && entry.isStale) {\n                isForegroundRevalidate = true\n              } else {\n                if (entry.isStale) {\n                  workStore.pendingRevalidates ??= {}\n                  if (!workStore.pendingRevalidates[cacheKey]) {\n                    const pendingRevalidate = doOriginalFetch(true)\n                      .then(async (response) => ({\n                        body: await response.arrayBuffer(),\n                        headers: response.headers,\n                        status: response.status,\n                        statusText: response.statusText,\n                      }))\n                      .finally(() => {\n                        workStore.pendingRevalidates ??= {}\n                        delete workStore.pendingRevalidates[cacheKey || '']\n                      })\n\n                    // Attach the empty catch here so we don't get a \"unhandled\n                    // promise rejection\" warning.\n                    pendingRevalidate.catch(console.error)\n\n                    workStore.pendingRevalidates[cacheKey] = pendingRevalidate\n                  }\n                }\n\n                cachedFetchData = entry.value.data\n              }\n            }\n          }\n\n          if (cachedFetchData) {\n            if (fetchStart) {\n              trackFetchMetric(workStore, {\n                start: fetchStart,\n                url: fetchUrl,\n                cacheReason,\n                cacheStatus: isHmrRefreshCache ? 'hmr' : 'hit',\n                cacheWarning,\n                status: cachedFetchData.status || 200,\n                method: init?.method || 'GET',\n              })\n            }\n\n            const response = new Response(\n              Buffer.from(cachedFetchData.body, 'base64'),\n              {\n                headers: cachedFetchData.headers,\n                status: cachedFetchData.status,\n              }\n            )\n\n            Object.defineProperty(response, 'url', {\n              value: cachedFetchData.url,\n            })\n\n            return response\n          }\n        }\n\n        if (workStore.isStaticGeneration && init && typeof init === 'object') {\n          const { cache } = init\n\n          // Delete `cache` property as Cloudflare Workers will throw an error\n          if (isEdgeRuntime) delete init.cache\n\n          if (cache === 'no-store') {\n            // If enabled, we should bail out of static generation.\n            if (workUnitStore && workUnitStore.type === 'prerender') {\n              if (cacheSignal) {\n                cacheSignal.endRead()\n                cacheSignal = null\n              }\n              return makeHangingPromise<Response>(\n                workUnitStore.renderSignal,\n                'fetch()'\n              )\n            } else {\n              markCurrentScopeAsDynamic(\n                workStore,\n                workUnitStore,\n                `no-store fetch ${input} ${workStore.route}`\n              )\n            }\n          }\n\n          const hasNextConfig = 'next' in init\n          const { next = {} } = init\n          if (\n            typeof next.revalidate === 'number' &&\n            revalidateStore &&\n            next.revalidate < revalidateStore.revalidate\n          ) {\n            if (next.revalidate === 0) {\n              // If enabled, we should bail out of static generation.\n              if (workUnitStore && workUnitStore.type === 'prerender') {\n                return makeHangingPromise<Response>(\n                  workUnitStore.renderSignal,\n                  'fetch()'\n                )\n              } else {\n                markCurrentScopeAsDynamic(\n                  workStore,\n                  workUnitStore,\n                  `revalidate: 0 fetch ${input} ${workStore.route}`\n                )\n              }\n            }\n\n            if (!workStore.forceStatic || next.revalidate !== 0) {\n              revalidateStore.revalidate = next.revalidate\n            }\n          }\n          if (hasNextConfig) delete init.next\n        }\n\n        // if we are revalidating the whole page via time or on-demand and\n        // the fetch cache entry is stale we should still de-dupe the\n        // origin hit if it's a cache-able entry\n        if (cacheKey && isForegroundRevalidate) {\n          const pendingRevalidateKey = cacheKey\n          workStore.pendingRevalidates ??= {}\n          let pendingRevalidate =\n            workStore.pendingRevalidates[pendingRevalidateKey]\n\n          if (pendingRevalidate) {\n            const revalidatedResult: {\n              body: ArrayBuffer\n              headers: Headers\n              status: number\n              statusText: string\n            } = await pendingRevalidate\n            return new Response(revalidatedResult.body, {\n              headers: revalidatedResult.headers,\n              status: revalidatedResult.status,\n              statusText: revalidatedResult.statusText,\n            })\n          }\n\n          // We used to just resolve the Response and clone it however for\n          // static generation with dynamicIO we need the response to be able to\n          // be resolved in a microtask and cloning the response will never have\n          // a body that can resolve in a microtask in node (as observed through\n          // experimentation) So instead we await the body and then when it is\n          // available we construct manually cloned Response objects with the\n          // body as an ArrayBuffer. This will be resolvable in a microtask\n          // making it compatible with dynamicIO.\n          const pendingResponse = doOriginalFetch(true, cacheReasonOverride)\n            // We're cloning the response using this utility because there\n            // exists a bug in the undici library around response cloning.\n            // See the following pull request for more details:\n            // https://github.com/vercel/next.js/pull/73274\n            .then(cloneResponse)\n\n          pendingRevalidate = pendingResponse\n            .then(async (responses) => {\n              const response = responses[0]\n              return {\n                body: await response.arrayBuffer(),\n                headers: response.headers,\n                status: response.status,\n                statusText: response.statusText,\n              }\n            })\n            .finally(() => {\n              // If the pending revalidate is not present in the store, then\n              // we have nothing to delete.\n              if (!workStore.pendingRevalidates?.[pendingRevalidateKey]) {\n                return\n              }\n\n              delete workStore.pendingRevalidates[pendingRevalidateKey]\n            })\n\n          // Attach the empty catch here so we don't get a \"unhandled promise\n          // rejection\" warning\n          pendingRevalidate.catch(() => {})\n\n          workStore.pendingRevalidates[pendingRevalidateKey] = pendingRevalidate\n\n          return pendingResponse.then((responses) => responses[1])\n        } else {\n          return doOriginalFetch(false, cacheReasonOverride)\n        }\n      }\n    )\n\n    if (cacheSignal) {\n      try {\n        return await result\n      } finally {\n        if (cacheSignal) {\n          cacheSignal.endRead()\n        }\n      }\n    }\n    return result\n  }\n\n  // Attach the necessary properties to the patched fetch function.\n  // We don't use this to determine if the fetch function has been patched,\n  // but for external consumers to determine if the fetch function has been\n  // patched.\n  patched.__nextPatched = true as const\n  patched.__nextGetStaticStore = () => workAsyncStorage\n  patched._nextOriginalFetch = originFetch\n  ;(globalThis as Record<symbol, unknown>)[NEXT_PATCH_SYMBOL] = true\n\n  return patched\n}\n// we patch fetch to collect cache information used for\n// determining if a page is static or not\nexport function patchFetch(options: PatchableModule) {\n  // If we've already patched fetch, we should not patch it again.\n  if (isFetchPatched()) return\n\n  // Grab the original fetch function. We'll attach this so we can use it in\n  // the patched fetch function.\n  const original = createDedupeFetch(globalThis.fetch)\n\n  // Set the global fetch to the patched fetch.\n  globalThis.fetch = createPatchedFetcher(original, options)\n}\n", "import { bold, green, magenta, red, yellow, white } from '../../lib/picocolors'\nimport { LRUCache } from '../../server/lib/lru-cache'\n\nexport const prefixes = {\n  wait: white(bold('○')),\n  error: red(bold('⨯')),\n  warn: yellow(bold('⚠')),\n  ready: '▲', // no color\n  info: white(bold(' ')),\n  event: green(bold('✓')),\n  trace: magenta(bold('»')),\n} as const\n\nconst LOGGING_METHOD = {\n  log: 'log',\n  warn: 'warn',\n  error: 'error',\n} as const\n\nfunction prefixedLog(prefixType: keyof typeof prefixes, ...message: any[]) {\n  if ((message[0] === '' || message[0] === undefined) && message.length === 1) {\n    message.shift()\n  }\n\n  const consoleMethod: keyof typeof LOGGING_METHOD =\n    prefixType in LOGGING_METHOD\n      ? LOGGING_METHOD[prefixType as keyof typeof LOGGING_METHOD]\n      : 'log'\n\n  const prefix = prefixes[prefixType]\n  // If there's no message, don't print the prefix but a new line\n  if (message.length === 0) {\n    console[consoleMethod]('')\n  } else {\n    // Ensure if there's ANSI escape codes it's concatenated into one string.\n    // Chrome DevTool can only handle color if it's in one string.\n    if (message.length === 1 && typeof message[0] === 'string') {\n      console[consoleMethod](' ' + prefix + ' ' + message[0])\n    } else {\n      console[consoleMethod](' ' + prefix, ...message)\n    }\n  }\n}\n\nexport function bootstrap(...message: string[]) {\n  // logging format: ' <prefix> <message>'\n  // e.g. ' ✓ Compiled successfully'\n  // Add spaces to align with the indent of other logs\n  console.log('   ' + message.join(' '))\n}\n\nexport function wait(...message: any[]) {\n  prefixedLog('wait', ...message)\n}\n\nexport function error(...message: any[]) {\n  prefixedLog('error', ...message)\n}\n\nexport function warn(...message: any[]) {\n  prefixedLog('warn', ...message)\n}\n\nexport function ready(...message: any[]) {\n  prefixedLog('ready', ...message)\n}\n\nexport function info(...message: any[]) {\n  prefixedLog('info', ...message)\n}\n\nexport function event(...message: any[]) {\n  prefixedLog('event', ...message)\n}\n\nexport function trace(...message: any[]) {\n  prefixedLog('trace', ...message)\n}\n\nconst warnOnceCache = new LRUCache<string>(10_000, (value) => value.length)\nexport function warnOnce(...message: any[]) {\n  const key = message.join(' ')\n  if (!warnOnceCache.has(key)) {\n    warnOnceCache.set(key, key)\n    warn(...message)\n  }\n}\n", "export class LRUCache<T> {\n  private cache: Map<string, T>\n  private sizes: Map<string, number>\n  private totalSize: number\n  private maxSize: number\n  private calculateSize: (value: T) => number\n\n  constructor(maxSize: number, calculateSize?: (value: T) => number) {\n    this.cache = new Map()\n    this.sizes = new Map()\n    this.totalSize = 0\n    this.maxSize = maxSize\n    this.calculateSize = calculateSize || (() => 1)\n  }\n\n  set(key?: string | null, value?: T): void {\n    if (!key || !value) return\n\n    const size = this.calculateSize(value)\n\n    if (size > this.maxSize) {\n      console.warn('Single item size exceeds maxSize')\n      return\n    }\n\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n    }\n\n    this.cache.set(key, value)\n    this.sizes.set(key, size)\n    this.totalSize += size\n\n    this.touch(key)\n  }\n\n  has(key?: string | null): boolean {\n    if (!key) return false\n\n    this.touch(key)\n    return Boolean(this.cache.get(key))\n  }\n\n  get(key?: string | null): T | undefined {\n    if (!key) return\n\n    const value = this.cache.get(key)\n    if (value === undefined) {\n      return undefined\n    }\n\n    this.touch(key)\n    return value\n  }\n\n  private touch(key: string): void {\n    const value = this.cache.get(key)\n    if (value !== undefined) {\n      this.cache.delete(key)\n      this.cache.set(key, value)\n      this.evictIfNecessary()\n    }\n  }\n\n  private evictIfNecessary(): void {\n    while (this.totalSize > this.maxSize && this.cache.size > 0) {\n      this.evictLeastRecentlyUsed()\n    }\n  }\n\n  private evictLeastRecentlyUsed(): void {\n    const lruKey = this.cache.keys().next().value\n    if (lruKey !== undefined) {\n      const lruSize = this.sizes.get(lruKey) || 0\n      this.totalSize -= lruSize\n      this.cache.delete(lruKey)\n      this.sizes.delete(lruKey)\n    }\n  }\n\n  reset() {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  keys() {\n    return [...this.cache.keys()]\n  }\n\n  remove(key: string): void {\n    if (this.cache.has(key)) {\n      this.totalSize -= this.sizes.get(key) || 0\n      this.cache.delete(key)\n      this.sizes.delete(key)\n    }\n  }\n\n  clear(): void {\n    this.cache.clear()\n    this.sizes.clear()\n    this.totalSize = 0\n  }\n\n  get size(): number {\n    return this.cache.size\n  }\n\n  get currentSize(): number {\n    return this.totalSize\n  }\n}\n", "import type { AppRouteHandlerFn, AppRouteHandlers } from '../module'\n\nimport { HTTP_METHODS, type HTTP_METHOD } from '../../../web/http'\n\nconst AUTOMATIC_ROUTE_METHODS = ['HEAD', 'OPTIONS'] as const\n\nfunction handleMethodNotAllowedResponse(): Response {\n  return new Response(null, { status: 405 })\n}\n\nexport function autoImplementMethods(\n  handlers: AppRouteHandlers\n): Record<HTTP_METHOD, AppRouteHandlerFn> {\n  // Loop through all the HTTP methods to create the initial methods object.\n  // Each of the methods will be set to the 405 response handler.\n  const methods: Record<HTTP_METHOD, AppRouteHandlerFn> = HTTP_METHODS.reduce(\n    (acc, method) => ({\n      ...acc,\n      // If the userland module implements the method, then use it. Otherwise,\n      // use the 405 response handler.\n      [method]: handlers[method] ?? handleMethodNotAllowedResponse,\n    }),\n    {} as Record<HTTP_METHOD, AppRouteHandlerFn>\n  )\n\n  // Get all the methods that could be automatically implemented that were not\n  // implemented by the userland module.\n  const implemented = new Set(HTTP_METHODS.filter((method) => handlers[method]))\n  const missing = AUTOMATIC_ROUTE_METHODS.filter(\n    (method) => !implemented.has(method)\n  )\n\n  // Loop over the missing methods to automatically implement them if we can.\n  for (const method of missing) {\n    // If the userland module doesn't implement the HEAD method, then\n    // we'll automatically implement it by calling the GET method (if it\n    // exists).\n    if (method === 'HEAD') {\n      if (handlers.GET) {\n        // Implement the HEAD method by calling the GET method.\n        methods.HEAD = handlers.GET\n\n        // Mark it as implemented.\n        implemented.add('HEAD')\n      }\n      continue\n    }\n\n    // If OPTIONS is not provided then implement it.\n    if (method === 'OPTIONS') {\n      // TODO: check if HEAD is implemented, if so, use it to add more headers\n\n      // Get all the methods that were implemented by the userland module.\n      const allow: HTTP_METHOD[] = ['OPTIONS', ...implemented]\n\n      // If the list of methods doesn't include HEAD, but it includes GET, then\n      // add HEAD as it's automatically implemented.\n      if (!implemented.has('HEAD') && implemented.has('GET')) {\n        allow.push('HEAD')\n      }\n\n      // Sort and join the list with commas to create the `Allow` header. See:\n      // https://httpwg.org/specs/rfc9110.html#field.allow\n      const headers = { Allow: allow.sort().join(', ') }\n\n      // Implement the OPTIONS method by returning a 204 response with the\n      // `Allow` header.\n      methods.OPTIONS = () => new Response(null, { status: 204, headers })\n\n      // Mark this method as implemented.\n      implemented.add('OPTIONS')\n\n      continue\n    }\n\n    throw new Error(\n      `Invariant: should handle all automatic implementable methods, got method: ${method}`\n    )\n  }\n\n  return methods\n}\n", "export const HTTPAccessErrorStatus = {\n  NOT_FOUND: 404,\n  FORBIDDEN: 403,\n  UNAUTHORIZED: 401,\n}\n\nconst ALLOWED_CODES = new Set(Object.values(HTTPAccessErrorStatus))\n\nexport const HTTP_ERROR_FALLBACK_ERROR_CODE = 'NEXT_HTTP_ERROR_FALLBACK'\n\nexport type HTTPAccessFallbackError = Error & {\n  digest: `${typeof HTTP_ERROR_FALLBACK_ERROR_CODE};${string}`\n}\n\n/**\n * Checks an error to determine if it's an error generated by\n * the HTTP navigation APIs `notFound()`, `forbidden()` or `unauthorized()`.\n *\n * @param error the error that may reference a HTTP access error\n * @returns true if the error is a HTTP access error\n */\nexport function isHTTPAccessFallbackError(\n  error: unknown\n): error is HTTPAccessFallbackError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n  const [prefix, httpStatus] = error.digest.split(';')\n\n  return (\n    prefix === HTTP_ERROR_FALLBACK_ERROR_CODE &&\n    ALLOWED_CODES.has(Number(httpStatus))\n  )\n}\n\nexport function getAccessFallbackHTTPStatus(\n  error: HTTPAccessFallbackError\n): number {\n  const httpStatus = error.digest.split(';')[1]\n  return Number(httpStatus)\n}\n\nexport function getAccessFallbackErrorTypeByStatus(\n  status: number\n): 'not-found' | 'forbidden' | 'unauthorized' | undefined {\n  switch (status) {\n    case 401:\n      return 'unauthorized'\n    case 403:\n      return 'forbidden'\n    case 404:\n      return 'not-found'\n    default:\n      return\n  }\n}\n", "export enum RedirectStatusCode {\n  SeeOther = 303,\n  TemporaryRedirect = 307,\n  PermanentRedirect = 308,\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\n\nexport const REDIRECT_ERROR_CODE = 'NEXT_REDIRECT'\n\nexport enum RedirectType {\n  push = 'push',\n  replace = 'replace',\n}\n\nexport type RedirectError = Error & {\n  digest: `${typeof REDIRECT_ERROR_CODE};${RedirectType};${string};${RedirectStatusCode};`\n}\n\n/**\n * Checks an error to determine if it's an error generated by the\n * `redirect(url)` helper.\n *\n * @param error the error that may reference a redirect error\n * @returns true if the error is a redirect error\n */\nexport function isRedirectError(error: unknown): error is RedirectError {\n  if (\n    typeof error !== 'object' ||\n    error === null ||\n    !('digest' in error) ||\n    typeof error.digest !== 'string'\n  ) {\n    return false\n  }\n\n  const digest = error.digest.split(';')\n  const [errorCode, type] = digest\n  const destination = digest.slice(2, -2).join(';')\n  const status = digest.at(-2)\n\n  const statusCode = Number(status)\n\n  return (\n    errorCode === REDIRECT_ERROR_CODE &&\n    (type === 'replace' || type === 'push') &&\n    typeof destination === 'string' &&\n    !isNaN(statusCode) &&\n    statusCode in RedirectStatusCode\n  )\n}\n", "import { getDigestForWellKnownError } from './create-error-handler'\n\nexport function printDebugThrownValueForProspectiveRender(\n  thrownValue: unknown,\n  route: string\n) {\n  // We don't need to print well-known Next.js errors.\n  if (getDigestForWellKnownError(thrownValue)) {\n    return\n  }\n\n  let message: undefined | string\n  if (\n    typeof thrownValue === 'object' &&\n    thrownValue !== null &&\n    typeof (thrownValue as any).message === 'string'\n  ) {\n    message = (thrownValue as any).message\n    if (typeof (thrownValue as any).stack === 'string') {\n      const originalErrorStack: string = (thrownValue as any).stack\n      const stackStart = originalErrorStack.indexOf('\\n')\n      if (stackStart > -1) {\n        const error = new Error(\n          `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled.\n          \nOriginal Error: ${message}`\n        )\n        error.stack =\n          'Error: ' + error.message + originalErrorStack.slice(stackStart)\n        console.error(error)\n        return\n      }\n    }\n  } else if (typeof thrownValue === 'string') {\n    message = thrownValue\n  }\n\n  if (message) {\n    console.error(`Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. No stack was provided.\n          \nOriginal Message: ${message}`)\n    return\n  }\n\n  console.error(\n    `Route ${route} errored during the prospective render. These errors are normally ignored and may not prevent the route from prerendering but are logged here because build debugging is enabled. The thrown value is logged just following this message`\n  )\n  console.error(thrownValue)\n  return\n}\n", "import type { ErrorInfo } from 'react'\n\nimport stringHash from 'next/dist/compiled/string-hash'\nimport { formatServerError } from '../../lib/format-server-error'\nimport { SpanStatusCode, getTracer } from '../lib/trace/tracer'\nimport { isAbortError } from '../pipe-readable'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport { isNextRouterError } from '../../client/components/is-next-router-error'\nimport { getProperError } from '../../lib/is-error'\nimport { createDigestWithErrorCode } from '../../lib/error-telemetry-utils'\n\ndeclare global {\n  var __next_log_error__: undefined | ((err: unknown) => void)\n}\n\ntype RSCErrorHandler = (err: unknown) => string | undefined\ntype SSRErrorHandler = (\n  err: unknown,\n  errorInfo?: ErrorInfo\n) => string | undefined\n\nexport type DigestedError = Error & { digest: string }\n\n/**\n * Returns a digest for well-known Next.js errors, otherwise `undefined`. If a\n * digest is returned this also means that the error does not need to be\n * reported.\n */\nexport function getDigestForWellKnownError(error: unknown): string | undefined {\n  // If we're bailing out to CSR, we don't need to log the error.\n  if (isBailoutToCSRError(error)) return error.digest\n\n  // If this is a navigation error, we don't need to log the error.\n  if (isNextRouterError(error)) return error.digest\n\n  // If this error occurs, we know that we should be stopping the static\n  // render. This is only thrown in static generation when PPR is not enabled,\n  // which causes the whole page to be marked as dynamic. We don't need to\n  // tell the user about this error, as it's not actionable.\n  if (isDynamicServerError(error)) return error.digest\n\n  return undefined\n}\n\nexport function createFlightReactServerErrorHandler(\n  shouldFormatError: boolean,\n  onReactServerRenderError: (err: DigestedError) => void\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + err.stack || '').toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Record exception in an active span, if available.\n    const span = getTracer().getActiveScopeSpan()\n    if (span) {\n      span.recordException(err)\n      span.setStatus({\n        code: SpanStatusCode.ERROR,\n        message: err.message,\n      })\n    }\n\n    onReactServerRenderError(err)\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLReactServerErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  silenceLogger: boolean,\n  onReactServerRenderError: undefined | ((err: DigestedError) => void)\n): RSCErrorHandler {\n  return (thrownValue: unknown) => {\n    if (typeof thrownValue === 'string') {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      return stringHash(thrownValue).toString()\n    }\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (!err.digest) {\n      // TODO-APP: look at using webcrypto instead. Requires a promise to be awaited.\n      err.digest = stringHash(err.message + (err.stack || '')).toString()\n    }\n\n    // @TODO by putting this here and not at the top it is possible that\n    // we don't error the build in places we actually expect to\n    if (!reactServerErrors.has(err.digest)) {\n      reactServerErrors.set(err.digest, err)\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (!silenceLogger) {\n        onReactServerRenderError?.(err)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function createHTMLErrorHandler(\n  shouldFormatError: boolean,\n  isNextExport: boolean,\n  reactServerErrors: Map<string, DigestedError>,\n  allCapturedErrors: Array<unknown>,\n  silenceLogger: boolean,\n  onHTMLRenderSSRError: (err: DigestedError, errorInfo?: ErrorInfo) => void\n): SSRErrorHandler {\n  return (thrownValue: unknown, errorInfo?: ErrorInfo) => {\n    let isSSRError = true\n\n    allCapturedErrors.push(thrownValue)\n\n    // If the response was closed, we don't need to log the error.\n    if (isAbortError(thrownValue)) return\n\n    const digest = getDigestForWellKnownError(thrownValue)\n\n    if (digest) {\n      return digest\n    }\n\n    const err = getProperError(thrownValue) as DigestedError\n    // If the error already has a digest, respect the original digest,\n    // so it won't get re-generated into another new error.\n    if (err.digest) {\n      if (reactServerErrors.has(err.digest)) {\n        // This error is likely an obfuscated error from react-server.\n        // We recover the original error here.\n        thrownValue = reactServerErrors.get(err.digest)\n        isSSRError = false\n      } else {\n        // The error is not from react-server but has a digest\n        // from other means so we don't need to produce a new one\n      }\n    } else {\n      err.digest = stringHash(\n        err.message + (errorInfo?.componentStack || err.stack || '')\n      ).toString()\n    }\n\n    // Format server errors in development to add more helpful error messages\n    if (shouldFormatError) {\n      formatServerError(err)\n    }\n\n    // Don't log the suppressed error during export\n    if (\n      !(\n        isNextExport &&\n        err?.message?.includes(\n          'The specific message is omitted in production builds to avoid leaking sensitive details.'\n        )\n      )\n    ) {\n      // Record exception in an active span, if available.\n      const span = getTracer().getActiveScopeSpan()\n      if (span) {\n        span.recordException(err)\n        span.setStatus({\n          code: SpanStatusCode.ERROR,\n          message: err.message,\n        })\n      }\n\n      if (\n        !silenceLogger &&\n        // HTML errors contain RSC errors as well, filter them out before reporting\n        isSSRError\n      ) {\n        onHTMLRenderSSRError(err, errorInfo)\n      }\n    }\n\n    return createDigestWithErrorCode(thrownValue, err.digest)\n  }\n}\n\nexport function isUserLandError(err: any): boolean {\n  return (\n    !isAbortError(err) && !isBailoutToCSRError(err) && !isNextRouterError(err)\n  )\n}\n", "// This has to be a shared module which is shared between client component error boundary and dynamic component\nconst BAILOUT_TO_CSR = 'BAILOUT_TO_CLIENT_SIDE_RENDERING'\n\n/** An error that should be thrown when we want to bail out to client-side rendering. */\nexport class BailoutToCSRError extends Error {\n  public readonly digest = BAILOUT_TO_CSR\n\n  constructor(public readonly reason: string) {\n    super(`Bail out to client-side rendering: ${reason}`)\n  }\n}\n\n/** Checks if a passed argument is an error that is thrown if we want to bail out to client-side rendering. */\nexport function isBailoutToCSRError(err: unknown): err is BailoutToCSRError {\n  if (typeof err !== 'object' || err === null || !('digest' in err)) {\n    return false\n  }\n\n  return err.digest === BAILOUT_TO_CSR\n}\n", "import {\n  isHTTPAccessFallbackError,\n  type HTTPAccessFallbackError,\n} from './http-access-fallback/http-access-fallback'\nimport { isRedirectError, type RedirectError } from './redirect-error'\n\n/**\n * Returns true if the error is a navigation signal error. These errors are\n * thrown by user code to perform navigation operations and interrupt the React\n * render.\n */\nexport function isNextRouterError(\n  error: unknown\n): error is RedirectError | HTTPAccessFallbackError {\n  return isRedirectError(error) || isHTTPAccessFallbackError(error)\n}\n", "'use client'\n\nimport type { FetchServerResponseResult } from '../../client/components/router-reducer/fetch-server-response'\nimport type {\n  FocusAndScrollRef,\n  PrefetchKind,\n  RouterChangeByServerResponse,\n} from '../../client/components/router-reducer/router-reducer-types'\nimport type {\n  FlightRouterState,\n  FlightSegmentPath,\n} from '../../server/app-render/types'\nimport React from 'react'\n\nexport type ChildSegmentMap = Map<string, CacheNode>\n\n/**\n * Cache node used in app-router / layout-router.\n */\nexport type CacheNode = ReadyCacheNode | LazyCacheNode\n\nexport type LoadingModuleData =\n  | [React.JSX.Element, React.ReactNode, React.ReactNode]\n  | null\n\n/** viewport metadata node */\nexport type HeadData = React.ReactNode\n\nexport type LazyCacheNode = {\n  /**\n   * When rsc is null, this is a lazily-initialized cache node.\n   *\n   * If the app attempts to render it, it triggers a lazy data fetch,\n   * postpones the render, and schedules an update to a new tree.\n   *\n   * TODO: This mechanism should not be used when PPR is enabled, though it\n   * currently is in some cases until we've implemented partial\n   * segment fetching.\n   */\n  rsc: null\n\n  /**\n   * A prefetched version of the segment data. See explanation in corresponding\n   * field of ReadyCacheNode (below).\n   *\n   * Since LazyCacheNode mostly only exists in the non-PPR implementation, this\n   * will usually be null, but it could have been cloned from a previous\n   * CacheNode that was created by the PPR implementation. Eventually we want\n   * to migrate everything away from LazyCacheNode entirely.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * A pending response for the lazy data fetch. If this is not present\n   * during render, it is lazily created.\n   */\n  lazyData: Promise<FetchServerResponseResult> | null\n\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  /**\n   * Child parallel routes.\n   */\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport type ReadyCacheNode = {\n  /**\n   * When rsc is not null, it represents the RSC data for the\n   * corresponding segment.\n   *\n   * `null` is a valid React Node but because segment data is always a\n   * <LayoutRouter> component, we can use `null` to represent empty.\n   *\n   * TODO: For additional type safety, update this type to\n   * Exclude<React.ReactNode, null>. Need to update createEmptyCacheNode to\n   * accept rsc as an argument, or just inline the callers.\n   */\n  rsc: React.ReactNode\n\n  /**\n   * Represents a static version of the segment that can be shown immediately,\n   * and may or may not contain dynamic holes. It's prefetched before a\n   * navigation occurs.\n   *\n   * During rendering, we will choose whether to render `rsc` or `prefetchRsc`\n   * with `useDeferredValue`. As with the `rsc` field, a value of `null` means\n   * no value was provided. In this case, the LayoutRouter will go straight to\n   * rendering the `rsc` value; if that one is also missing, it will suspend and\n   * trigger a lazy fetch.\n   */\n  prefetchRsc: React.ReactNode\n\n  /**\n   * There should never be a lazy data request in this case.\n   */\n  lazyData: null\n  prefetchHead: HeadData | null\n\n  head: HeadData\n\n  loading: LoadingModuleData | Promise<LoadingModuleData>\n\n  parallelRoutes: Map<string, ChildSegmentMap>\n}\n\nexport interface NavigateOptions {\n  scroll?: boolean\n}\n\nexport interface PrefetchOptions {\n  kind: PrefetchKind\n}\n\nexport interface AppRouterInstance {\n  /**\n   * Navigate to the previous history entry.\n   */\n  back(): void\n  /**\n   * Navigate to the next history entry.\n   */\n  forward(): void\n  /**\n   * Refresh the current page.\n   */\n  refresh(): void\n  /**\n   * Refresh the current page. Use in development only.\n   * @internal\n   */\n  hmrRefresh(): void\n  /**\n   * Navigate to the provided href.\n   * Pushes a new history entry.\n   */\n  push(href: string, options?: NavigateOptions): void\n  /**\n   * Navigate to the provided href.\n   * Replaces the current history entry.\n   */\n  replace(href: string, options?: NavigateOptions): void\n  /**\n   * Prefetch the provided href.\n   */\n  prefetch(href: string, options?: PrefetchOptions): void\n}\n\nexport const AppRouterContext = React.createContext<AppRouterInstance | null>(\n  null\n)\nexport const LayoutRouterContext = React.createContext<{\n  parentTree: FlightRouterState\n  parentCacheNode: CacheNode\n  parentSegmentPath: FlightSegmentPath | null\n  url: string\n} | null>(null)\n\nexport const GlobalLayoutRouterContext = React.createContext<{\n  tree: FlightRouterState\n  changeByServerResponse: RouterChangeByServerResponse\n  focusAndScrollRef: FocusAndScrollRef\n  nextUrl: string | null\n}>(null as any)\n\nexport const TemplateContext = React.createContext<React.ReactNode>(null as any)\n\nif (process.env.NODE_ENV !== 'production') {\n  AppRouterContext.displayName = 'AppRouterContext'\n  LayoutRouterContext.displayName = 'LayoutRouterContext'\n  GlobalLayoutRouterContext.displayName = 'GlobalLayoutRouterContext'\n  TemplateContext.displayName = 'TemplateContext'\n}\n\nexport const MissingSlotContext = React.createContext<Set<string>>(new Set())\n", "/**\n * This class is used to detect when all cache reads for a given render are settled.\n * We do this to allow for cache warming the prerender without having to continue rendering\n * the remainder of the page. This feature is really only useful when the dynamicIO flag is on\n * and should only be used in codepaths gated with this feature.\n */\n\nexport class CacheSignal {\n  private count: number\n  private earlyListeners: Array<() => void>\n  private listeners: Array<() => void>\n  private tickPending: boolean\n  private taskPending: boolean\n\n  constructor() {\n    this.count = 0\n    this.earlyListeners = []\n    this.listeners = []\n    this.tickPending = false\n    this.taskPending = false\n  }\n\n  private noMorePendingCaches() {\n    if (!this.tickPending) {\n      this.tickPending = true\n      process.nextTick(() => {\n        this.tickPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.earlyListeners.length; i++) {\n            this.earlyListeners[i]()\n          }\n          this.earlyListeners.length = 0\n        }\n      })\n    }\n    if (!this.taskPending) {\n      this.taskPending = true\n      setTimeout(() => {\n        this.taskPending = false\n        if (this.count === 0) {\n          for (let i = 0; i < this.listeners.length; i++) {\n            this.listeners[i]()\n          }\n          this.listeners.length = 0\n        }\n      }, 0)\n    }\n  }\n\n  /**\n   * This promise waits until there are no more in progress cache reads but no later.\n   * This allows for adding more cache reads after to delay cacheReady.\n   */\n  inputReady() {\n    return new Promise<void>((resolve) => {\n      this.earlyListeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  /**\n   * If there are inflight cache reads this Promise can resolve in a microtask however\n   * if there are no inflight cache reads then we wait at least one task to allow initial\n   * cache reads to be initiated.\n   */\n  cacheReady() {\n    return new Promise<void>((resolve) => {\n      this.listeners.push(resolve)\n      if (this.count === 0) {\n        this.noMorePendingCaches()\n      }\n    })\n  }\n\n  beginRead() {\n    this.count++\n  }\n\n  endRead() {\n    // If this is the last read we need to wait a task before we can claim the cache is settled.\n    // The cache read will likely ping a Server Component which can read from the cache again and this\n    // will play out in a microtask so we need to only resolve pending listeners if we're still at 0\n    // after at least one task.\n    // We only want one task scheduled at a time so when we hit count 1 we don't decrement the counter immediately.\n    // If intervening reads happen before the scheduled task runs they will never observe count 1 preventing reentrency\n    this.count--\n    if (this.count === 0) {\n      this.noMorePendingCaches()\n    }\n  }\n}\n", "// This regex will have fast negatives meaning valid identifiers may not pass\n// this test. However this is only used during static generation to provide hints\n// about why a page bailed out of some or all prerendering and we can use bracket notation\n// for example while `ಠ_ಠ` is a valid identifier it's ok to print `searchParams['ಠ_ಠ']`\n// even if this would have been fine too `searchParams.ಠ_ಠ`\nconst isDefinitelyAValidIdentifier = /^[A-Za-z_$][A-Za-z0-9_$]*$/\n\nexport function describeStringPropertyAccess(target: string, prop: string) {\n  if (isDefinitelyAValidIdentifier.test(prop)) {\n    return `\\`${target}.${prop}\\``\n  }\n  return `\\`${target}[${JSON.stringify(prop)}]\\``\n}\n\nexport function describeHasCheckingStringProperty(\n  target: string,\n  prop: string\n) {\n  const stringifiedProp = JSON.stringify(prop)\n  return `\\`Reflect.has(${target}, ${stringifiedProp})\\`, \\`${stringifiedProp} in ${target}\\`, or similar`\n}\n\nexport const wellKnownProperties = new Set([\n  'hasOwnProperty',\n  'isPrototypeOf',\n  'propertyIsEnumerable',\n  'toString',\n  'valueOf',\n  'toLocaleString',\n\n  // Promise prototype\n  // fallthrough\n  'then',\n  'catch',\n  'finally',\n\n  // React Promise extension\n  // fallthrough\n  'status',\n\n  // React introspection\n  'displayName',\n\n  // Common tested properties\n  // fallthrough\n  'toJSON',\n  '$$typeof',\n  '__esModule',\n])\n", "import * as React from 'react'\n\nconst errorRef: { current: null | Error } = { current: null }\n\n// React.cache is currently only available in canary/experimental React channels.\nconst cache =\n  typeof React.cache === 'function'\n    ? React.cache\n    : (fn: (key: unknown) => void) => fn\n\n// When Dynamic IO is enabled, we record these as errors so that they\n// are captured by the dev overlay as it's more critical to fix these\n// when enabled.\nconst logErrorOrWarn = process.env.__NEXT_DYNAMIC_IO\n  ? console.error\n  : console.warn\n\n// We don't want to dedupe across requests.\n// The developer might've just attempted to fix the warning so we should warn again if it still happens.\nconst flushCurrentErrorIfNew = cache(\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars -- cache key\n  (key: unknown) => {\n    try {\n      logErrorOrWarn(errorRef.current)\n    } finally {\n      errorRef.current = null\n    }\n  }\n)\n\n/**\n * Creates a function that logs an error message that is deduped by the userland\n * callsite.\n * This requires no indirection between the call of this function and the userland\n * callsite i.e. there's only a single library frame above this.\n * Do not use on the Client where sourcemaps and ignore listing might be enabled.\n * Only use that for warnings need a fix independent of the callstack.\n *\n * @param getMessage\n * @returns\n */\nexport function createDedupedByCallsiteServerErrorLoggerDev<Args extends any[]>(\n  getMessage: (...args: Args) => Error\n) {\n  return function logDedupedError(...args: Args) {\n    const message = getMessage(...args)\n\n    if (process.env.NODE_ENV !== 'production') {\n      const callStackFrames = new Error().stack?.split('\\n')\n      if (callStackFrames === undefined || callStackFrames.length < 4) {\n        logErrorOrWarn(message)\n      } else {\n        // Error:\n        //   logDedupedError\n        //   asyncApiBeingAccessedSynchronously\n        //   <userland callsite>\n        // TODO: This breaks if sourcemaps with ignore lists are enabled.\n        const key = callStackFrames[4]\n        errorRef.current = message\n        flushCurrentErrorIfNew(key)\n      }\n    } else {\n      logErrorOrWarn(message)\n    }\n  }\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { FallbackRouteParams } from './fallback-params'\n\nimport { ReflectAdapter } from '../web/spec-extension/adapters/reflect'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  throwToInterruptStaticGeneration,\n  postponeWithTracking,\n  trackSynchronousRequestDataAccessInDev,\n} from '../app-render/dynamic-rendering'\n\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n  type PrerenderStorePPR,\n  type PrerenderStoreLegacy,\n  type PrerenderStoreModern,\n} from '../app-render/work-unit-async-storage.external'\nimport { InvariantError } from '../../shared/lib/invariant-error'\nimport {\n  describeStringPropertyAccess,\n  wellKnownProperties,\n} from '../../shared/lib/utils/reflect-utils'\nimport { makeHangingPromise } from '../dynamic-rendering-utils'\nimport { createDedupedByCallsiteServerErrorLoggerDev } from '../create-deduped-by-callsite-server-error-logger'\nimport { scheduleImmediate } from '../../lib/scheduler'\n\nexport type ParamValue = string | Array<string> | undefined\nexport type Params = Record<string, ParamValue>\n\n/**\n * In this version of Next.js the `params` prop passed to Layouts, Pages, and other Segments is a Promise.\n * However to facilitate migration to this new Promise type you can currently still access params directly on the Promise instance passed to these Segments.\n * The `UnsafeUnwrappedParams` type is available if you need to temporarily access the underlying params without first awaiting or `use`ing the Promise.\n *\n * In a future version of Next.js the `params` prop will be a plain Promise and this type will be removed.\n *\n * Typically instances of `params` can be updated automatically to be treated as a Promise by a codemod published alongside this Next.js version however if you\n * have not yet run the codemod of the codemod cannot detect certain instances of `params` usage you should first try to refactor your code to await `params`.\n *\n * If refactoring is not possible but you still want to be able to access params directly without typescript errors you can cast the params Promise to this type\n *\n * ```tsx\n * type Props = { params: Promise<{ id: string }>}\n *\n * export default async function Layout(props: Props) {\n *  const directParams = (props.params as unknown as UnsafeUnwrappedParams<typeof props.params>)\n *  return ...\n * }\n * ```\n *\n * This type is marked deprecated to help identify it as target for refactoring away.\n *\n * @deprecated\n */\nexport type UnsafeUnwrappedParams<P> =\n  P extends Promise<infer U> ? Omit<U, 'then' | 'status' | 'value'> : never\n\nexport function createParamsFromClient(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\n// generateMetadata always runs in RSC context so it is equivalent to a Server Page Component\nexport type CreateServerParamsForMetadata = typeof createServerParamsForMetadata\nexport const createServerParamsForMetadata = createServerParamsForServerSegment\n\n// routes always runs in RSC context so it is equivalent to a Server Page Component\nexport function createServerParamsForRoute(\n  underlyingParams: Params,\n  workStore: WorkStore\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createServerParamsForServerSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (workUnitStore) {\n    switch (workUnitStore.type) {\n      case 'prerender':\n      case 'prerender-ppr':\n      case 'prerender-legacy':\n        return createPrerenderParams(underlyingParams, workStore, workUnitStore)\n      default:\n      // fallthrough\n    }\n  }\n  return createRenderParams(underlyingParams, workStore)\n}\n\nexport function createPrerenderParamsForClientSegment(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  const prerenderStore = workUnitAsyncStorage.getStore()\n  if (prerenderStore && prerenderStore.type === 'prerender') {\n    const fallbackParams = workStore.fallbackRouteParams\n    if (fallbackParams) {\n      for (let key in underlyingParams) {\n        if (fallbackParams.has(key)) {\n          // This params object has one of more fallback params so we need to consider\n          // the awaiting of this params object \"dynamic\". Since we are in dynamicIO mode\n          // we encode this as a promise that never resolves\n          return makeHangingPromise(prerenderStore.renderSignal, '`params`')\n        }\n      }\n    }\n  }\n  // We're prerendering in a mode that does not abort. We resolve the promise without\n  // any tracking because we're just transporting a value from server to client where the tracking\n  // will be applied.\n  return Promise.resolve(underlyingParams)\n}\n\nfunction createPrerenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStore\n): Promise<Params> {\n  const fallbackParams = workStore.fallbackRouteParams\n  if (fallbackParams) {\n    let hasSomeFallbackParams = false\n    for (const key in underlyingParams) {\n      if (fallbackParams.has(key)) {\n        hasSomeFallbackParams = true\n        break\n      }\n    }\n\n    if (hasSomeFallbackParams) {\n      // params need to be treated as dynamic because we have at least one fallback param\n      if (prerenderStore.type === 'prerender') {\n        // We are in a dynamicIO (PPR or otherwise) prerender\n        return makeAbortingExoticParams(\n          underlyingParams,\n          workStore.route,\n          prerenderStore\n        )\n      }\n      // remaining cases are prerender-ppr and prerender-legacy\n      // We aren't in a dynamicIO prerender but we do have fallback params at this\n      // level so we need to make an erroring exotic params object which will postpone\n      // if you access the fallback params\n      return makeErroringExoticParams(\n        underlyingParams,\n        fallbackParams,\n        workStore,\n        prerenderStore\n      )\n    }\n  }\n\n  // We don't have any fallback params so we have an entirely static safe params object\n  return makeUntrackedExoticParams(underlyingParams)\n}\n\nfunction createRenderParams(\n  underlyingParams: Params,\n  workStore: WorkStore\n): Promise<Params> {\n  if (process.env.NODE_ENV === 'development' && !workStore.isPrefetchRequest) {\n    return makeDynamicallyTrackedExoticParamsWithDevWarnings(\n      underlyingParams,\n      workStore\n    )\n  } else {\n    return makeUntrackedExoticParams(underlyingParams)\n  }\n}\n\ninterface CacheLifetime {}\nconst CachedParams = new WeakMap<CacheLifetime, Promise<Params>>()\n\nfunction makeAbortingExoticParams(\n  underlyingParams: Params,\n  route: string,\n  prerenderStore: PrerenderStoreModern\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const promise = makeHangingPromise<Params>(\n    prerenderStore.renderSignal,\n    '`params`'\n  )\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      Object.defineProperty(promise, prop, {\n        get() {\n          const expression = describeStringPropertyAccess('params', prop)\n          const error = createParamsAccessError(route, expression)\n          abortAndThrowOnSynchronousRequestDataAccess(\n            route,\n            expression,\n            error,\n            prerenderStore\n          )\n        },\n        set(newValue) {\n          Object.defineProperty(promise, prop, {\n            value: newValue,\n            writable: true,\n            enumerable: true,\n          })\n        },\n        enumerable: true,\n        configurable: true,\n      })\n    }\n  })\n\n  return promise\n}\n\nfunction makeErroringExoticParams(\n  underlyingParams: Params,\n  fallbackParams: FallbackRouteParams,\n  workStore: WorkStore,\n  prerenderStore: PrerenderStorePPR | PrerenderStoreLegacy\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  const augmentedUnderlying = { ...underlyingParams }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(augmentedUnderlying)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      if (fallbackParams.has(prop)) {\n        Object.defineProperty(augmentedUnderlying, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          enumerable: true,\n        })\n        Object.defineProperty(promise, prop, {\n          get() {\n            const expression = describeStringPropertyAccess('params', prop)\n            // In most dynamic APIs we also throw if `dynamic = \"error\"` however\n            // for params is only dynamic when we're generating a fallback shell\n            // and even when `dynamic = \"error\"` we still support generating dynamic\n            // fallback shells\n            // TODO remove this comment when dynamicIO is the default since there\n            // will be no `dynamic = \"error\"`\n            if (prerenderStore.type === 'prerender-ppr') {\n              // PPR Prerender (no dynamicIO)\n              postponeWithTracking(\n                workStore.route,\n                expression,\n                prerenderStore.dynamicTracking\n              )\n            } else {\n              // Legacy Prerender\n              throwToInterruptStaticGeneration(\n                expression,\n                workStore,\n                prerenderStore\n              )\n            }\n          },\n          set(newValue) {\n            Object.defineProperty(promise, prop, {\n              value: newValue,\n              writable: true,\n              enumerable: true,\n            })\n          },\n          enumerable: true,\n          configurable: true,\n        })\n      } else {\n        ;(promise as any)[prop] = underlyingParams[prop]\n      }\n    }\n  })\n\n  return promise\n}\n\nfunction makeUntrackedExoticParams(underlyingParams: Params): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = Promise.resolve(underlyingParams)\n  CachedParams.set(underlyingParams, promise)\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n    } else {\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  return promise\n}\n\nfunction makeDynamicallyTrackedExoticParamsWithDevWarnings(\n  underlyingParams: Params,\n  store: WorkStore\n): Promise<Params> {\n  const cachedParams = CachedParams.get(underlyingParams)\n  if (cachedParams) {\n    return cachedParams\n  }\n\n  // We don't use makeResolvedReactPromise here because params\n  // supports copying with spread and we don't want to unnecessarily\n  // instrument the promise with spreadable properties of ReactPromise.\n  const promise = new Promise<Params>((resolve) =>\n    scheduleImmediate(() => resolve(underlyingParams))\n  )\n\n  const proxiedProperties = new Set<string>()\n  const unproxiedProperties: Array<string> = []\n\n  Object.keys(underlyingParams).forEach((prop) => {\n    if (wellKnownProperties.has(prop)) {\n      // These properties cannot be shadowed because they need to be the\n      // true underlying value for Promises to work correctly at runtime\n      unproxiedProperties.push(prop)\n    } else {\n      proxiedProperties.add(prop)\n      ;(promise as any)[prop] = underlyingParams[prop]\n    }\n  })\n\n  const proxiedPromise = new Proxy(promise, {\n    get(target, prop, receiver) {\n      if (typeof prop === 'string') {\n        if (\n          // We are accessing a property that was proxied to the promise instance\n          proxiedProperties.has(prop)\n        ) {\n          const expression = describeStringPropertyAccess('params', prop)\n          syncIODev(store.route, expression)\n        }\n      }\n      return ReflectAdapter.get(target, prop, receiver)\n    },\n    set(target, prop, value, receiver) {\n      if (typeof prop === 'string') {\n        proxiedProperties.delete(prop)\n      }\n      return ReflectAdapter.set(target, prop, value, receiver)\n    },\n    ownKeys(target) {\n      const expression = '`...params` or similar expression'\n      syncIODev(store.route, expression, unproxiedProperties)\n      return Reflect.ownKeys(target)\n    },\n  })\n\n  CachedParams.set(underlyingParams, proxiedPromise)\n  return proxiedPromise\n}\n\nfunction syncIODev(\n  route: string | undefined,\n  expression: string,\n  missingProperties?: Array<string>\n) {\n  const workUnitStore = workUnitAsyncStorage.getStore()\n  if (\n    workUnitStore &&\n    workUnitStore.type === 'request' &&\n    workUnitStore.prerenderPhase === true\n  ) {\n    // When we're rendering dynamically in dev we need to advance out of the\n    // Prerender environment when we read Request data synchronously\n    const requestStore = workUnitStore\n    trackSynchronousRequestDataAccessInDev(requestStore)\n  }\n  // In all cases we warn normally\n  if (missingProperties && missingProperties.length > 0) {\n    warnForIncompleteEnumeration(route, expression, missingProperties)\n  } else {\n    warnForSyncAccess(route, expression)\n  }\n}\n\nconst warnForSyncAccess = createDedupedByCallsiteServerErrorLoggerDev(\n  createParamsAccessError\n)\n\nconst warnForIncompleteEnumeration =\n  createDedupedByCallsiteServerErrorLoggerDev(createIncompleteEnumerationError)\n\nfunction createParamsAccessError(\n  route: string | undefined,\n  expression: string\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction createIncompleteEnumerationError(\n  route: string | undefined,\n  expression: string,\n  missingProperties: Array<string>\n) {\n  const prefix = route ? `Route \"${route}\" ` : 'This route '\n  return new Error(\n    `${prefix}used ${expression}. ` +\n      `\\`params\\` should be awaited before using its properties. ` +\n      `The following properties were not available through enumeration ` +\n      `because they conflict with builtin property names: ` +\n      `${describeListOfPropertyNames(missingProperties)}. ` +\n      `Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`\n  )\n}\n\nfunction describeListOfPropertyNames(properties: Array<string>) {\n  switch (properties.length) {\n    case 0:\n      throw new InvariantError(\n        'Expected describeListOfPropertyNames to be called with a non-empty list of strings.'\n      )\n    case 1:\n      return `\\`${properties[0]}\\``\n    case 2:\n      return `\\`${properties[0]}\\` and \\`${properties[1]}\\``\n    default: {\n      let description = ''\n      for (let i = 0; i < properties.length - 1; i++) {\n        description += `\\`${properties[i]}\\`, `\n      }\n      description += `, and \\`${properties[properties.length - 1]}\\``\n      return description\n    }\n  }\n}\n", "import { RedirectStatusCode } from './redirect-status-code'\nimport {\n  RedirectType,\n  type RedirectError,\n  isRedirectError,\n  REDIRECT_ERROR_CODE,\n} from './redirect-error'\n\nconst actionAsyncStorage =\n  typeof window === 'undefined'\n    ? (\n        require('../../server/app-render/action-async-storage.external') as typeof import('../../server/app-render/action-async-storage.external')\n      ).actionAsyncStorage\n    : undefined\n\nexport function getRedirectError(\n  url: string,\n  type: RedirectType,\n  statusCode: RedirectStatusCode = RedirectStatusCode.TemporaryRedirect\n): RedirectError {\n  const error = new Error(REDIRECT_ERROR_CODE) as RedirectError\n  error.digest = `${REDIRECT_ERROR_CODE};${type};${url};${statusCode};`\n  return error\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 307/303 to the caller.\n * - In a Server Action, type defaults to 'push' and 'replace' elsewhere.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function redirect(\n  /** The URL to redirect to */\n  url: string,\n  type?: RedirectType\n): never {\n  type ??= actionAsyncStorage?.getStore()?.isAction\n    ? RedirectType.push\n    : RedirectType.replace\n\n  throw getRedirectError(url, type, RedirectStatusCode.TemporaryRedirect)\n}\n\n/**\n * This function allows you to redirect the user to another URL. It can be used in\n * [Server Components](https://nextjs.org/docs/app/building-your-application/rendering/server-components),\n * [Route Handlers](https://nextjs.org/docs/app/building-your-application/routing/route-handlers), and\n * [Server Actions](https://nextjs.org/docs/app/building-your-application/data-fetching/server-actions-and-mutations).\n *\n * - In a Server Component, this will insert a meta tag to redirect the user to the target page.\n * - In a Route Handler or Server Action, it will serve a 308/303 to the caller.\n *\n * Read more: [Next.js Docs: `redirect`](https://nextjs.org/docs/app/api-reference/functions/redirect)\n */\nexport function permanentRedirect(\n  /** The URL to redirect to */\n  url: string,\n  type: RedirectType = RedirectType.replace\n): never {\n  throw getRedirectError(url, type, RedirectStatusCode.PermanentRedirect)\n}\n\n/**\n * Returns the encoded URL from the error if it's a RedirectError, null\n * otherwise. Note that this does not validate the URL returned.\n *\n * @param error the error that may be a redirect error\n * @return the url if the error was a redirect error\n */\nexport function getURLFromRedirectError(error: RedirectError): string\nexport function getURLFromRedirectError(error: unknown): string | null {\n  if (!isRedirectError(error)) return null\n\n  // Slices off the beginning of the digest that contains the code and the\n  // separating ';'.\n  return error.digest.split(';').slice(2, -2).join(';')\n}\n\nexport function getRedirectTypeFromError(error: RedirectError): RedirectType {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return error.digest.split(';', 2)[1] as RedirectType\n}\n\nexport function getRedirectStatusCodeFromError(error: RedirectError): number {\n  if (!isRedirectError(error)) {\n    throw new Error('Not a redirect error')\n  }\n\n  return Number(error.digest.split(';').at(-2))\n}\n", "import type { NextConfig } from '../../config-shared'\nimport type { AppRouteRouteDefinition } from '../../route-definitions/app-route-route-definition'\nimport type { AppSegmentConfig } from '../../../build/segment-config/app/app-segment-config'\nimport type { NextRequest } from '../../web/spec-extension/request'\nimport type { PrerenderManifest } from '../../../build'\nimport type { NextURL } from '../../web/next-url'\nimport type { DeepReadonly } from '../../../shared/lib/deep-readonly'\nimport type { WorkUnitStore } from '../../app-render/work-unit-async-storage.external'\n\nimport {\n  RouteModule,\n  type RouteModuleHandleContext,\n  type RouteModuleOptions,\n} from '../route-module'\nimport { createRequestStoreForAPI } from '../../async-storage/request-store'\nimport {\n  createWorkStore,\n  type WorkStoreContext,\n} from '../../async-storage/work-store'\nimport { type HTTP_METHOD, HTTP_METHODS, isHTTPMethod } from '../../web/http'\nimport { getImplicitTags } from '../../lib/implicit-tags'\nimport { patchFetch } from '../../lib/patch-fetch'\nimport { getTracer } from '../../lib/trace/tracer'\nimport { AppRouteRouteHandlersSpan } from '../../lib/trace/constants'\nimport { getPathnameFromAbsolutePath } from './helpers/get-pathname-from-absolute-path'\nimport * as Log from '../../../build/output/log'\nimport { autoImplementMethods } from './helpers/auto-implement-methods'\nimport {\n  appendMutableCookies,\n  type ReadonlyRequestCookies,\n} from '../../web/spec-extension/adapters/request-cookies'\nimport { HeadersAdapter } from '../../web/spec-extension/adapters/headers'\nimport { RequestCookiesAdapter } from '../../web/spec-extension/adapters/request-cookies'\nimport { parsedUrlQueryToParams } from './helpers/parsed-url-query-to-params'\nimport { printDebugThrownValueForProspectiveRender } from '../../app-render/prospective-render-utils'\n\nimport * as serverHooks from '../../../client/components/hooks-server-context'\nimport { DynamicServerError } from '../../../client/components/hooks-server-context'\n\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../../app-render/work-async-storage.external'\nimport {\n  workUnitAsyncStorage,\n  type RequestStore,\n  type PrerenderStore,\n} from '../../app-render/work-unit-async-storage.external'\nimport {\n  actionAsyncStorage,\n  type ActionStore,\n} from '../../app-render/action-async-storage.external'\nimport * as sharedModules from './shared-modules'\nimport { getIsServerAction } from '../../lib/server-action-request-meta'\nimport { RequestCookies } from 'next/dist/compiled/@edge-runtime/cookies'\nimport { cleanURL } from './helpers/clean-url'\nimport { StaticGenBailoutError } from '../../../client/components/static-generation-bailout'\nimport { isStaticGenEnabled } from './helpers/is-static-gen-enabled'\nimport {\n  abortAndThrowOnSynchronousRequestDataAccess,\n  postponeWithTracking,\n  createDynamicTrackingState,\n  getFirstDynamicReason,\n} from '../../app-render/dynamic-rendering'\nimport { ReflectAdapter } from '../../web/spec-extension/adapters/reflect'\nimport type { RenderOptsPartial } from '../../app-render/types'\nimport { CacheSignal } from '../../app-render/cache-signal'\nimport { scheduleImmediate } from '../../../lib/scheduler'\nimport { createServerParamsForRoute } from '../../request/params'\nimport type { AppSegment } from '../../../build/segment-config/app/app-segments'\nimport {\n  getRedirectStatusCodeFromError,\n  getURLFromRedirectError,\n} from '../../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectError,\n} from '../../../client/components/redirect-error'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../../client/components/http-access-fallback/http-access-fallback'\nimport { RedirectStatusCode } from '../../../client/components/redirect-status-code'\nimport { INFINITE_CACHE } from '../../../lib/constants'\n\nexport class WrappedNextRouterError {\n  constructor(\n    public readonly error: RedirectError,\n    public readonly headers?: Headers\n  ) {}\n}\n\n/**\n * The AppRouteModule is the type of the module exported by the bundled App\n * Route module.\n */\nexport type AppRouteModule = typeof import('../../../build/templates/app-route')\n\nexport type AppRouteSharedContext = {\n  buildId: string\n}\n\n/**\n * AppRouteRouteHandlerContext is the context that is passed to the route\n * handler for app routes.\n */\nexport interface AppRouteRouteHandlerContext extends RouteModuleHandleContext {\n  renderOpts: WorkStoreContext['renderOpts'] &\n    Pick<RenderOptsPartial, 'onInstrumentationRequestError'> &\n    CollectedCacheInfo\n  prerenderManifest: DeepReadonly<PrerenderManifest>\n  sharedContext: AppRouteSharedContext\n}\n\ntype CollectedCacheInfo = {\n  collectedTags?: string\n  collectedRevalidate?: number\n  collectedExpire?: number\n  collectedStale?: number\n}\n\n/**\n * AppRouteHandlerFnContext is the context that is passed to the handler as the\n * second argument.\n */\ntype AppRouteHandlerFnContext = {\n  params?: Promise<Record<string, string | string[] | undefined>>\n}\n\n/**\n * Handler function for app routes. If a non-Response value is returned, an error\n * will be thrown.\n */\nexport type AppRouteHandlerFn = (\n  /**\n   * Incoming request object.\n   */\n  req: NextRequest,\n  /**\n   * Context properties on the request (including the parameters if this was a\n   * dynamic route).\n   */\n  ctx: AppRouteHandlerFnContext\n) => unknown\n\n/**\n * AppRouteHandlers describes the handlers for app routes that is provided by\n * the userland module.\n */\nexport type AppRouteHandlers = {\n  [method in HTTP_METHOD]?: AppRouteHandlerFn\n}\n\n/**\n * AppRouteUserlandModule is the userland module that is provided for app\n * routes. This contains all the user generated code.\n */\nexport type AppRouteUserlandModule = AppRouteHandlers &\n  Pick<\n    AppSegmentConfig,\n    'dynamic' | 'revalidate' | 'dynamicParams' | 'fetchCache'\n  > &\n  Pick<AppSegment, 'generateStaticParams'>\n\n/**\n * AppRouteRouteModuleOptions is the options that are passed to the app route\n * module from the bundled code.\n */\nexport interface AppRouteRouteModuleOptions\n  extends RouteModuleOptions<AppRouteRouteDefinition, AppRouteUserlandModule> {\n  readonly resolvedPagePath: string\n  readonly nextConfigOutput: NextConfig['output']\n}\n\n/**\n * AppRouteRouteHandler is the handler for app routes.\n */\nexport class AppRouteRouteModule extends RouteModule<\n  AppRouteRouteDefinition,\n  AppRouteUserlandModule\n> {\n  /**\n   * A reference to the request async storage.\n   */\n  public readonly workUnitAsyncStorage = workUnitAsyncStorage\n\n  /**\n   * A reference to the static generation async storage.\n   */\n  public readonly workAsyncStorage = workAsyncStorage\n\n  /**\n   * An interface to call server hooks which interact with the underlying\n   * storage.\n   */\n  public readonly serverHooks = serverHooks\n\n  public static readonly sharedModules = sharedModules\n\n  /**\n   * A reference to the mutation related async storage, such as mutations of\n   * cookies.\n   */\n  public readonly actionAsyncStorage = actionAsyncStorage\n\n  public readonly resolvedPagePath: string\n  public readonly nextConfigOutput: NextConfig['output'] | undefined\n\n  private readonly methods: Record<HTTP_METHOD, AppRouteHandlerFn>\n  private readonly hasNonStaticMethods: boolean\n  private readonly dynamic: AppRouteUserlandModule['dynamic']\n\n  constructor({\n    userland,\n    definition,\n    resolvedPagePath,\n    nextConfigOutput,\n  }: AppRouteRouteModuleOptions) {\n    super({ userland, definition })\n\n    this.resolvedPagePath = resolvedPagePath\n    this.nextConfigOutput = nextConfigOutput\n\n    // Automatically implement some methods if they aren't implemented by the\n    // userland module.\n    this.methods = autoImplementMethods(userland)\n\n    // Get the non-static methods for this route.\n    this.hasNonStaticMethods = hasNonStaticMethods(userland)\n\n    // Get the dynamic property from the userland module.\n    this.dynamic = this.userland.dynamic\n    if (this.nextConfigOutput === 'export') {\n      if (this.dynamic === 'force-dynamic') {\n        throw new Error(\n          `export const dynamic = \"force-dynamic\" on page \"${definition.pathname}\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else if (!isStaticGenEnabled(this.userland) && this.userland['GET']) {\n        throw new Error(\n          `export const dynamic = \"force-static\"/export const revalidate not configured on route \"${definition.pathname}\" with \"output: export\". See more info here: https://nextjs.org/docs/advanced-features/static-html-export`\n        )\n      } else {\n        this.dynamic = 'error'\n      }\n    }\n\n    // We only warn in development after here, so return if we're not in\n    // development.\n    if (process.env.NODE_ENV === 'development') {\n      // Print error in development if the exported handlers are in lowercase, only\n      // uppercase handlers are supported.\n      const lowercased = HTTP_METHODS.map((method) => method.toLowerCase())\n      for (const method of lowercased) {\n        if (method in this.userland) {\n          Log.error(\n            `Detected lowercase method '${method}' in '${\n              this.resolvedPagePath\n            }'. Export the uppercase '${method.toUpperCase()}' method name to fix this error.`\n          )\n        }\n      }\n\n      // Print error if the module exports a default handler, they must use named\n      // exports for each HTTP method.\n      if ('default' in this.userland) {\n        Log.error(\n          `Detected default export in '${this.resolvedPagePath}'. Export a named export for each HTTP method instead.`\n        )\n      }\n\n      // If there is no methods exported by this module, then return a not found\n      // response.\n      if (!HTTP_METHODS.some((method) => method in this.userland)) {\n        Log.error(\n          `No HTTP methods exported in '${this.resolvedPagePath}'. Export a named export for each HTTP method.`\n        )\n      }\n    }\n  }\n\n  /**\n   * Resolves the handler function for the given method.\n   *\n   * @param method the requested method\n   * @returns the handler function for the given method\n   */\n  private resolve(method: string): AppRouteHandlerFn {\n    // Ensure that the requested method is a valid method (to prevent RCE's).\n    if (!isHTTPMethod(method)) return () => new Response(null, { status: 400 })\n\n    // Return the handler.\n    return this.methods[method]\n  }\n\n  private async do(\n    handler: AppRouteHandlerFn,\n    actionStore: ActionStore,\n    workStore: WorkStore,\n    // @TODO refactor to not take this argument but instead construct the RequestStore\n    // inside this function. Right now we get passed a RequestStore even when\n    // we're going to do a prerender. We should probably just split do up into prexecute and execute\n    requestStore: RequestStore,\n    implicitTags: string[],\n    request: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ) {\n    const isStaticGeneration = workStore.isStaticGeneration\n    const dynamicIOEnabled = !!context.renderOpts.experimental?.dynamicIO\n\n    // Patch the global fetch.\n    patchFetch({\n      workAsyncStorage: this.workAsyncStorage,\n      workUnitAsyncStorage: this.workUnitAsyncStorage,\n    })\n\n    const handlerContext: AppRouteHandlerFnContext = {\n      params: context.params\n        ? createServerParamsForRoute(\n            parsedUrlQueryToParams(context.params),\n            workStore\n          )\n        : undefined,\n    }\n\n    let prerenderStore: null | PrerenderStore = null\n\n    let res: unknown\n    try {\n      if (isStaticGeneration) {\n        const userlandRevalidate = this.userland.revalidate\n        const defaultRevalidate: number =\n          // If the static generation store does not have a revalidate value\n          // set, then we should set it the revalidate value from the userland\n          // module or default to false.\n          userlandRevalidate === false || userlandRevalidate === undefined\n            ? INFINITE_CACHE\n            : userlandRevalidate\n\n        if (dynamicIOEnabled) {\n          /**\n           * When we are attempting to statically prerender the GET handler of a route.ts module\n           * and dynamicIO is on we follow a similar pattern to rendering.\n           *\n           * We first run the handler letting caches fill. If something synchronously dynamic occurs\n           * during this prospective render then we can infer it will happen on every render and we\n           * just bail out of prerendering.\n           *\n           * Next we run the handler again and we check if we get a result back in a microtask.\n           * Next.js expects the return value to be a Response or a Thenable that resolves to a Response.\n           * Unfortunately Response's do not allow for accessing the response body synchronously or in\n           * a microtask so we need to allow one more task to unwrap the response body. This is a slightly\n           * different semantic than what we have when we render and it means that certain tasks can still\n           * execute before a prerender completes such as a carefully timed setImmediate.\n           *\n           * Functionally though IO should still take longer than the time it takes to unwrap the response body\n           * so our heuristic of excluding any IO should be preserved.\n           */\n          const prospectiveController = new AbortController()\n          let prospectiveRenderIsDynamic = false\n          const cacheSignal = new CacheSignal()\n          let dynamicTracking = createDynamicTrackingState(undefined)\n\n          const prospectiveRoutePrerenderStore: PrerenderStore =\n            (prerenderStore = {\n              type: 'prerender',\n              phase: 'action',\n              // This replicates prior behavior where rootParams is empty in routes\n              // TODO we need to make this have the proper rootParams for this route\n              rootParams: {},\n              implicitTags: implicitTags,\n              renderSignal: prospectiveController.signal,\n              controller: prospectiveController,\n              cacheSignal,\n              // During prospective render we don't use a controller\n              // because we need to let all caches fill.\n              dynamicTracking,\n              revalidate: defaultRevalidate,\n              expire: INFINITE_CACHE,\n              stale: INFINITE_CACHE,\n              tags: [...implicitTags],\n              prerenderResumeDataCache: null,\n            })\n\n          let prospectiveResult\n          try {\n            prospectiveResult = this.workUnitAsyncStorage.run(\n              prospectiveRoutePrerenderStore,\n              handler,\n              request,\n              handlerContext\n            )\n          } catch (err) {\n            if (prospectiveController.signal.aborted) {\n              // the route handler called an API which is always dynamic\n              // there is no need to try again\n              prospectiveRenderIsDynamic = true\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          }\n          if (\n            typeof prospectiveResult === 'object' &&\n            prospectiveResult !== null &&\n            typeof (prospectiveResult as any).then === 'function'\n          ) {\n            // The handler returned a Thenable. We'll listen for rejections to determine\n            // if the route is erroring for dynamic reasons.\n            ;(prospectiveResult as any as Promise<unknown>).then(\n              () => {},\n              (err) => {\n                if (prospectiveController.signal.aborted) {\n                  // the route handler called an API which is always dynamic\n                  // there is no need to try again\n                  prospectiveRenderIsDynamic = true\n                } else if (process.env.NEXT_DEBUG_BUILD) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              }\n            )\n          }\n          await cacheSignal.cacheReady()\n\n          if (prospectiveRenderIsDynamic) {\n            // the route handler called an API which is always dynamic\n            // there is no need to try again\n            const dynamicReason = getFirstDynamicReason(dynamicTracking)\n            if (dynamicReason) {\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            } else {\n              console.error(\n                'Expected Next.js to keep track of reason for opting out of static rendering but one was not found. This is a bug in Next.js'\n              )\n              throw new DynamicServerError(\n                `Route ${workStore.route} couldn't be rendered statically because it used a dynamic API. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n              )\n            }\n          }\n\n          // TODO start passing this controller to the route handler. We should expose\n          // it so the handler to abort inflight requests and other operations if we abort\n          // the prerender.\n          const finalController = new AbortController()\n          dynamicTracking = createDynamicTrackingState(undefined)\n\n          const finalRoutePrerenderStore: PrerenderStore = (prerenderStore = {\n            type: 'prerender',\n            phase: 'action',\n            rootParams: {},\n            implicitTags: implicitTags,\n            renderSignal: finalController.signal,\n            controller: finalController,\n            cacheSignal: null,\n            dynamicTracking,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags],\n            prerenderResumeDataCache: null,\n          })\n\n          let responseHandled = false\n          res = await new Promise((resolve, reject) => {\n            scheduleImmediate(async () => {\n              try {\n                const result = await (this.workUnitAsyncStorage.run(\n                  finalRoutePrerenderStore,\n                  handler,\n                  request,\n                  handlerContext\n                ) as Promise<Response>)\n                if (responseHandled) {\n                  // we already rejected in the followup task\n                  return\n                } else if (!(result instanceof Response)) {\n                  // This is going to error but we let that happen below\n                  resolve(result)\n                  return\n                }\n\n                responseHandled = true\n\n                let bodyHandled = false\n                result.arrayBuffer().then((body) => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n\n                    resolve(\n                      new Response(body, {\n                        headers: result.headers,\n                        status: result.status,\n                        statusText: result.statusText,\n                      })\n                    )\n                  }\n                }, reject)\n                scheduleImmediate(() => {\n                  if (!bodyHandled) {\n                    bodyHandled = true\n                    finalController.abort()\n                    reject(createDynamicIOError(workStore.route))\n                  }\n                })\n              } catch (err) {\n                reject(err)\n              }\n            })\n            scheduleImmediate(() => {\n              if (!responseHandled) {\n                responseHandled = true\n                finalController.abort()\n                reject(createDynamicIOError(workStore.route))\n              }\n            })\n          })\n          if (finalController.signal.aborted) {\n            // We aborted from within the execution\n            throw createDynamicIOError(workStore.route)\n          } else {\n            // We didn't abort during the execution. We can abort now as a matter of semantics\n            // though at the moment nothing actually consumes this signal so it won't halt any\n            // inflight work.\n            finalController.abort()\n          }\n        } else {\n          prerenderStore = {\n            type: 'prerender-legacy',\n            phase: 'action',\n            rootParams: {},\n            implicitTags: implicitTags,\n            revalidate: defaultRevalidate,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags],\n          }\n\n          res = await workUnitAsyncStorage.run(\n            prerenderStore,\n            handler,\n            request,\n            handlerContext\n          )\n        }\n      } else {\n        res = await workUnitAsyncStorage.run(\n          requestStore,\n          handler,\n          request,\n          handlerContext\n        )\n      }\n    } catch (err) {\n      if (isRedirectError(err)) {\n        const url = getURLFromRedirectError(err)\n        if (!url) {\n          throw new Error('Invariant: Unexpected redirect url format')\n        }\n\n        // We need to capture any headers that should be sent on\n        // the response.\n        const headers = new Headers({ Location: url })\n\n        // Let's append any cookies that were added by the\n        // cookie API.\n        // TODO leaving the gate here b/c it indicates that we might not actually want to do this\n        // on every `do` call. During prerender there should be no mutableCookies because\n        if (requestStore.type === 'request') {\n          appendMutableCookies(headers, requestStore.mutableCookies)\n        }\n\n        // Return the redirect response.\n        return new Response(null, {\n          // If we're in an action, we want to use a 303 redirect as we don't\n          // want the POST request to follow the redirect, as it could result in\n          // erroneous re-submissions.\n          status: actionStore.isAction\n            ? RedirectStatusCode.SeeOther\n            : getRedirectStatusCodeFromError(err),\n          headers,\n        })\n      } else if (isHTTPAccessFallbackError(err)) {\n        const httpStatus = getAccessFallbackHTTPStatus(err)\n        return new Response(null, { status: httpStatus })\n      }\n\n      throw err\n    }\n\n    // Validate that the response is a valid response object.\n    if (!(res instanceof Response)) {\n      throw new Error(\n        `No response is returned from route handler '${this.resolvedPagePath}'. Ensure you return a \\`Response\\` or a \\`NextResponse\\` in all branches of your handler.`\n      )\n    }\n\n    context.renderOpts.fetchMetrics = workStore.fetchMetrics\n\n    context.renderOpts.pendingWaitUntil = Promise.all([\n      workStore.incrementalCache?.revalidateTag(\n        workStore.revalidatedTags || []\n      ),\n      ...Object.values(workStore.pendingRevalidates || {}),\n    ]).finally(() => {\n      if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n        console.log(\n          'pending revalidates promise finished for:',\n          requestStore.url\n        )\n      }\n    })\n\n    if (prerenderStore) {\n      context.renderOpts.collectedTags = prerenderStore.tags?.join(',')\n      context.renderOpts.collectedRevalidate = prerenderStore.revalidate\n      context.renderOpts.collectedExpire = prerenderStore.expire\n      context.renderOpts.collectedStale = prerenderStore.stale\n    }\n\n    // It's possible cookies were set in the handler, so we need\n    // to merge the modified cookies and the returned response\n    // here.\n    const headers = new Headers(res.headers)\n    if (\n      requestStore.type === 'request' &&\n      appendMutableCookies(headers, requestStore.mutableCookies)\n    ) {\n      return new Response(res.body, {\n        status: res.status,\n        statusText: res.statusText,\n        headers,\n      })\n    }\n\n    return res\n  }\n\n  public async handle(\n    req: NextRequest,\n    context: AppRouteRouteHandlerContext\n  ): Promise<Response> {\n    // Get the handler function for the given method.\n    const handler = this.resolve(req.method)\n\n    // Get the context for the static generation.\n    const staticGenerationContext: WorkStoreContext = {\n      // App Routes don't support unknown route params.\n      fallbackRouteParams: null,\n      page: this.definition.page,\n      renderOpts: context.renderOpts,\n      buildId: context.sharedContext.buildId,\n    }\n\n    // Add the fetchCache option to the renderOpts.\n    staticGenerationContext.renderOpts.fetchCache = this.userland.fetchCache\n\n    const actionStore: ActionStore = {\n      isAppRoute: true,\n      isAction: getIsServerAction(req),\n    }\n\n    const implicitTags = getImplicitTags(\n      this.definition.page,\n      req.nextUrl,\n      // App Routes don't support unknown route params.\n      null\n    )\n\n    const requestStore = createRequestStoreForAPI(\n      req,\n      req.nextUrl,\n      implicitTags,\n      undefined,\n      context.prerenderManifest.preview\n    )\n\n    const workStore = createWorkStore(staticGenerationContext)\n\n    // Run the handler with the request AsyncLocalStorage to inject the helper\n    // support. We set this to `unknown` because the type is not known until\n    // runtime when we do a instanceof check below.\n    const response: unknown = await this.actionAsyncStorage.run(\n      actionStore,\n      () =>\n        this.workUnitAsyncStorage.run(requestStore, () =>\n          this.workAsyncStorage.run(workStore, async () => {\n            // Check to see if we should bail out of static generation based on\n            // having non-static methods.\n            if (this.hasNonStaticMethods) {\n              if (workStore.isStaticGeneration) {\n                const err = new DynamicServerError(\n                  'Route is configured with methods that cannot be statically generated.'\n                )\n                workStore.dynamicUsageDescription = err.message\n                workStore.dynamicUsageStack = err.stack\n                throw err\n              }\n            }\n\n            // We assume we can pass the original request through however we may end up\n            // proxying it in certain circumstances based on execution type and configuration\n            let request = req\n\n            // Update the static generation store based on the dynamic property.\n            switch (this.dynamic) {\n              case 'force-dynamic': {\n                // Routes of generated paths should be dynamic\n                workStore.forceDynamic = true\n                break\n              }\n              case 'force-static':\n                // The dynamic property is set to force-static, so we should\n                // force the page to be static.\n                workStore.forceStatic = true\n                // We also Proxy the request to replace dynamic data on the request\n                // with empty stubs to allow for safely executing as static\n                request = new Proxy(req, forceStaticRequestHandlers)\n                break\n              case 'error':\n                // The dynamic property is set to error, so we should throw an\n                // error if the page is being statically generated.\n                workStore.dynamicShouldError = true\n                if (workStore.isStaticGeneration)\n                  request = new Proxy(req, requireStaticRequestHandlers)\n                break\n              default:\n                // We proxy `NextRequest` to track dynamic access, and potentially bail out of static generation\n                request = proxyNextRequest(req, workStore)\n            }\n\n            // TODO: propagate this pathname from route matcher\n            const route = getPathnameFromAbsolutePath(this.resolvedPagePath)\n\n            const tracer = getTracer()\n\n            // Update the root span attribute for the route.\n            tracer.setRootSpanAttribute('next.route', route)\n\n            return tracer.trace(\n              AppRouteRouteHandlersSpan.runHandler,\n              {\n                spanName: `executing api route (app) ${route}`,\n                attributes: {\n                  'next.route': route,\n                },\n              },\n              async () =>\n                this.do(\n                  handler,\n                  actionStore,\n                  workStore,\n                  requestStore,\n                  implicitTags,\n                  request,\n                  context\n                )\n            )\n          })\n        )\n    )\n\n    // If the handler did't return a valid response, then return the internal\n    // error response.\n    if (!(response instanceof Response)) {\n      // TODO: validate the correct handling behavior, maybe log something?\n      return new Response(null, { status: 500 })\n    }\n\n    if (response.headers.has('x-middleware-rewrite')) {\n      throw new Error(\n        'NextResponse.rewrite() was used in a app route handler, this is not currently supported. Please remove the invocation to continue.'\n      )\n    }\n\n    if (response.headers.get('x-middleware-next') === '1') {\n      // TODO: move this error into the `NextResponse.next()` function.\n      throw new Error(\n        'NextResponse.next() was used in a app route handler, this is not supported. See here for more info: https://nextjs.org/docs/messages/next-response-next-in-app-route-handler'\n      )\n    }\n\n    return response\n  }\n}\n\nexport default AppRouteRouteModule\n\n/**\n * Gets all the method names for handlers that are not considered static.\n *\n * @param handlers the handlers from the userland module\n * @returns the method names that are not considered static or false if all\n *          methods are static\n */\nexport function hasNonStaticMethods(handlers: AppRouteHandlers): boolean {\n  if (\n    // Order these by how common they are to be used\n    handlers.POST ||\n    handlers.PUT ||\n    handlers.DELETE ||\n    handlers.PATCH ||\n    handlers.OPTIONS\n  ) {\n    return true\n  }\n  return false\n}\n\n// These symbols will be used to stash cached values on Proxied requests without requiring\n// additional closures or storage such as WeakMaps.\nconst nextURLSymbol = Symbol('nextUrl')\nconst requestCloneSymbol = Symbol('clone')\nconst urlCloneSymbol = Symbol('clone')\nconst searchParamsSymbol = Symbol('searchParams')\nconst hrefSymbol = Symbol('href')\nconst toStringSymbol = Symbol('toString')\nconst headersSymbol = Symbol('headers')\nconst cookiesSymbol = Symbol('cookies')\n\ntype RequestSymbolTarget = {\n  [headersSymbol]?: Headers\n  [cookiesSymbol]?: RequestCookies | ReadonlyRequestCookies\n  [nextURLSymbol]?: NextURL\n  [requestCloneSymbol]?: () => NextRequest\n}\n\ntype UrlSymbolTarget = {\n  [searchParamsSymbol]?: URLSearchParams\n  [hrefSymbol]?: string\n  [toStringSymbol]?: () => string\n  [urlCloneSymbol]?: () => NextURL\n}\n\n/**\n * The general technique with these proxy handlers is to prioritize keeping them static\n * by stashing computed values on the Proxy itself. This is safe because the Proxy is\n * inaccessible to the consumer since all operations are forwarded\n */\nconst forceStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'headers':\n        return (\n          target[headersSymbol] ||\n          (target[headersSymbol] = HeadersAdapter.seal(new Headers({})))\n        )\n      case 'cookies':\n        return (\n          target[cookiesSymbol] ||\n          (target[cookiesSymbol] = RequestCookiesAdapter.seal(\n            new RequestCookies(new Headers({}))\n          ))\n        )\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            forceStaticNextUrlHandlers\n          ))\n        )\n      case 'url':\n        // we don't need to separately cache this we can just read the nextUrl\n        // and return the href since we know it will have been stripped of any\n        // dynamic parts. We access via the receiver to trigger the get trap\n        return receiver.nextUrl.href\n      case 'geo':\n      case 'ip':\n        return undefined\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              forceStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst forceStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      // URL properties\n      case 'search':\n        return ''\n      case 'searchParams':\n        return (\n          target[searchParamsSymbol] ||\n          (target[searchParamsSymbol] = new URLSearchParams())\n        )\n      case 'href':\n        return (\n          target[hrefSymbol] ||\n          (target[hrefSymbol] = cleanURL(target.href).href)\n        )\n      case 'toJSON':\n      case 'toString':\n        return (\n          target[toStringSymbol] ||\n          (target[toStringSymbol] = () => receiver.href)\n        )\n\n      // NextUrl properties\n      case 'url':\n        // Currently nextURL does not expose url but our Docs indicate that it is an available property\n        // I am forcing this to undefined here to avoid accidentally exposing a dynamic value later if\n        // the underlying nextURL ends up adding this property\n        return undefined\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), forceStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction proxyNextRequest(request: NextRequest, workStore: WorkStore) {\n  const nextUrlHandlers = {\n    get(\n      target: NextURL & UrlSymbolTarget,\n      prop: string | symbol,\n      receiver: any\n    ): unknown {\n      switch (prop) {\n        case 'search':\n        case 'searchParams':\n        case 'url':\n        case 'href':\n        case 'toJSON':\n        case 'toString':\n        case 'origin': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `nextUrl.${prop}`)\n          return ReflectAdapter.get(target, prop, receiver)\n        }\n        case 'clone':\n          return (\n            target[urlCloneSymbol] ||\n            (target[urlCloneSymbol] = () =>\n              new Proxy(target.clone(), nextUrlHandlers))\n          )\n        default:\n          return ReflectAdapter.get(target, prop, receiver)\n      }\n    },\n  }\n\n  const nextRequestHandlers = {\n    get(\n      target: NextRequest & RequestSymbolTarget,\n      prop: string | symbol\n    ): unknown {\n      switch (prop) {\n        case 'nextUrl':\n          return (\n            target[nextURLSymbol] ||\n            (target[nextURLSymbol] = new Proxy(target.nextUrl, nextUrlHandlers))\n          )\n        case 'headers':\n        case 'cookies':\n        case 'url':\n        case 'body':\n        case 'blob':\n        case 'json':\n        case 'text':\n        case 'arrayBuffer':\n        case 'formData': {\n          const workUnitStore = workUnitAsyncStorage.getStore()\n          trackDynamic(workStore, workUnitStore, `request.${prop}`)\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n        }\n        case 'clone':\n          return (\n            target[requestCloneSymbol] ||\n            (target[requestCloneSymbol] = () =>\n              new Proxy(\n                // This is vaguely unsafe but it's required since NextRequest does not implement\n                // clone. The reason we might expect this to work in this context is the Proxy will\n                // respond with static-amenable values anyway somewhat restoring the interface.\n                // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n                // sophisticated to adequately represent themselves in all contexts. A better approach is\n                // to probably embed the static generation logic into the class itself removing the need\n                // for any kind of proxying\n                target.clone() as NextRequest,\n                nextRequestHandlers\n              ))\n          )\n        default:\n          // The receiver arg is intentionally the same as the target to fix an issue with\n          // edge runtime, where attempting to access internal slots with the wrong `this` context\n          // results in an error.\n          return ReflectAdapter.get(target, prop, target)\n      }\n    },\n    // We don't need to proxy set because all the properties we proxy are ready only\n    // and will be ignored\n  }\n\n  return new Proxy(request, nextRequestHandlers)\n}\n\nconst requireStaticRequestHandlers = {\n  get(\n    target: NextRequest & RequestSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'nextUrl':\n        return (\n          target[nextURLSymbol] ||\n          (target[nextURLSymbol] = new Proxy(\n            target.nextUrl,\n            requireStaticNextUrlHandlers\n          ))\n        )\n      case 'headers':\n      case 'cookies':\n      case 'url':\n      case 'body':\n      case 'blob':\n      case 'json':\n      case 'text':\n      case 'arrayBuffer':\n      case 'formData':\n        throw new StaticGenBailoutError(\n          `Route ${target.nextUrl.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`request.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[requestCloneSymbol] ||\n          (target[requestCloneSymbol] = () =>\n            new Proxy(\n              // This is vaguely unsafe but it's required since NextRequest does not implement\n              // clone. The reason we might expect this to work in this context is the Proxy will\n              // respond with static-amenable values anyway somewhat restoring the interface.\n              // @TODO we need to rethink NextRequest and NextURL because they are not sufficientlly\n              // sophisticated to adequately represent themselves in all contexts. A better approach is\n              // to probably embed the static generation logic into the class itself removing the need\n              // for any kind of proxying\n              target.clone() as NextRequest,\n              requireStaticRequestHandlers\n            ))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n  // We don't need to proxy set because all the properties we proxy are ready only\n  // and will be ignored\n}\n\nconst requireStaticNextUrlHandlers = {\n  get(\n    target: NextURL & UrlSymbolTarget,\n    prop: string | symbol,\n    receiver: any\n  ): unknown {\n    switch (prop) {\n      case 'search':\n      case 'searchParams':\n      case 'url':\n      case 'href':\n      case 'toJSON':\n      case 'toString':\n      case 'origin':\n        throw new StaticGenBailoutError(\n          `Route ${target.pathname} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`nextUrl.${prop}\\`.`\n        )\n      case 'clone':\n        return (\n          target[urlCloneSymbol] ||\n          (target[urlCloneSymbol] = () =>\n            new Proxy(target.clone(), requireStaticNextUrlHandlers))\n        )\n      default:\n        return ReflectAdapter.get(target, prop, receiver)\n    }\n  },\n}\n\nfunction createDynamicIOError(route: string) {\n  return new DynamicServerError(\n    `Route ${route} couldn't be rendered statically because it used IO that was not cached. See more info here: https://nextjs.org/docs/messages/dynamic-io`\n  )\n}\n\nexport function trackDynamic(\n  store: WorkStore,\n  workUnitStore: undefined | WorkUnitStore,\n  expression: string\n): void {\n  if (workUnitStore) {\n    if (workUnitStore.type === 'cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside \"use cache\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`\n      )\n    } else if (workUnitStore.type === 'unstable-cache') {\n      throw new Error(\n        `Route ${store.route} used \"${expression}\" inside a function cached with \"unstable_cache(...)\". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use \"${expression}\" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`\n      )\n    }\n  }\n\n  if (store.dynamicShouldError) {\n    throw new StaticGenBailoutError(\n      `Route ${store.route} with \\`dynamic = \"error\"\\` couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`\n    )\n  }\n\n  if (workUnitStore) {\n    if (workUnitStore.type === 'prerender') {\n      // dynamicIO Prerender\n      const error = new Error(\n        `Route ${store.route} used ${expression} without first calling \\`await connection()\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-request`\n      )\n      abortAndThrowOnSynchronousRequestDataAccess(\n        store.route,\n        expression,\n        error,\n        workUnitStore\n      )\n    } else if (workUnitStore.type === 'prerender-ppr') {\n      // PPR Prerender\n      postponeWithTracking(\n        store.route,\n        expression,\n        workUnitStore.dynamicTracking\n      )\n    } else if (workUnitStore.type === 'prerender-legacy') {\n      // legacy Prerender\n      workUnitStore.revalidate = 0\n\n      const err = new DynamicServerError(\n        `Route ${store.route} couldn't be rendered statically because it used \\`${expression}\\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`\n      )\n      store.dynamicUsageDescription = expression\n      store.dynamicUsageStack = err.stack\n\n      throw err\n    } else if (\n      process.env.NODE_ENV === 'development' &&\n      workUnitStore &&\n      workUnitStore.type === 'request'\n    ) {\n      workUnitStore.usedDynamic = true\n    }\n  }\n}\n", "import type { AppRouteModule } from '../module.compiled'\n\n// route handlers are only statically optimized if they define\n// one of these top-level configs manually\n//   - dynamic = 'force-static'\n//   - dynamic = 'error'\n//   - revalidate > 0\n//   - revalidate = false\n//   - generateStaticParams\nexport function isStaticGenEnabled(\n  mod: AppRouteModule['routeModule']['userland']\n) {\n  return (\n    mod.dynamic === 'force-static' ||\n    mod.dynamic === 'error' ||\n    mod.revalidate === false ||\n    (mod.revalidate !== undefined && mod.revalidate > 0) ||\n    typeof mod.generateStaticParams == 'function'\n  )\n}\n", "/**\n * Based on https://github.com/facebook/react/blob/d4e78c42a94be027b4dc7ed2659a5fddfbf9bd4e/packages/react/src/ReactFetch.js\n */\nimport * as React from 'react'\nimport { cloneResponse } from './clone-response'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nconst simpleCacheKey = '[\"GET\",[],null,\"follow\",null,null,null,null]' // generateCacheKey(new Request('https://blank'));\n\nfunction generateCacheKey(request: Request): string {\n  // We pick the fields that goes into the key used to dedupe requests.\n  // We don't include the `cache` field, because we end up using whatever\n  // caching resulted from the first request.\n  // Notably we currently don't consider non-standard (or future) options.\n  // This might not be safe. TODO: warn for non-standard extensions differing.\n  // IF YOU CHANGE THIS UPDATE THE simpleCacheKey ABOVE.\n  return JSON.stringify([\n    request.method,\n    Array.from(request.headers.entries()),\n    request.mode,\n    request.redirect,\n    request.credentials,\n    request.referrer,\n    request.referrerPolicy,\n    request.integrity,\n  ])\n}\n\ntype CacheEntry = [\n  key: string,\n  promise: Promise<Response>,\n  response: Response | null,\n]\n\nexport function createDedupeFetch(originalFetch: typeof fetch) {\n  const getCacheEntries = React.cache(\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- url is the cache key\n    (url: string): CacheEntry[] => []\n  )\n\n  return function dedupeFetch(\n    resource: URL | RequestInfo,\n    options?: RequestInit\n  ): Promise<Response> {\n    if (options && options.signal) {\n      // If we're passed a signal, then we assume that\n      // someone else controls the lifetime of this object and opts out of\n      // caching. It's effectively the opt-out mechanism.\n      // Ideally we should be able to check this on the Request but\n      // it always gets initialized with its own signal so we don't\n      // know if it's supposed to override - unless we also override the\n      // Request constructor.\n      return originalFetch(resource, options)\n    }\n    // Normalize the Request\n    let url: string\n    let cacheKey: string\n    if (typeof resource === 'string' && !options) {\n      // Fast path.\n      cacheKey = simpleCacheKey\n      url = resource\n    } else {\n      // Normalize the request.\n      // if resource is not a string or a URL (its an instance of Request)\n      // then do not instantiate a new Request but instead\n      // reuse the request as to not disturb the body in the event it's a ReadableStream.\n      const request =\n        typeof resource === 'string' || resource instanceof URL\n          ? new Request(resource, options)\n          : resource\n      if (\n        (request.method !== 'GET' && request.method !== 'HEAD') ||\n        request.keepalive\n      ) {\n        // We currently don't dedupe requests that might have side-effects. Those\n        // have to be explicitly cached. We assume that the request doesn't have a\n        // body if it's GET or HEAD.\n        // keepalive gets treated the same as if you passed a custom cache signal.\n        return originalFetch(resource, options)\n      }\n      cacheKey = generateCacheKey(request)\n      url = request.url\n    }\n\n    const cacheEntries = getCacheEntries(url)\n    for (let i = 0, j = cacheEntries.length; i < j; i += 1) {\n      const [key, promise] = cacheEntries[i]\n      if (key === cacheKey) {\n        return promise.then(() => {\n          const response = cacheEntries[i][2]\n          if (!response) throw new InvariantError('No cached response')\n\n          // We're cloning the response using this utility because there exists\n          // a bug in the undici library around response cloning. See the\n          // following pull request for more details:\n          // https://github.com/vercel/next.js/pull/73274\n          const [cloned1, cloned2] = cloneResponse(response)\n          cacheEntries[i][2] = cloned2\n          return cloned1\n        })\n      }\n    }\n\n    // We pass the original arguments here in case normalizing the Request\n    // doesn't include all the options in this environment.\n    const promise = originalFetch(resource, options)\n    const entry: CacheEntry = [cacheKey, promise, null]\n    cacheEntries.push(entry)\n\n    return promise.then((response) => {\n      // We're cloning the response using this utility because there exists\n      // a bug in the undici library around response cloning. See the\n      // following pull request for more details:\n      // https://github.com/vercel/next.js/pull/73274\n      const [cloned1, cloned2] = cloneResponse(response)\n      entry[2] = cloned2\n      return cloned1\n    })\n  }\n}\n", "import type { ParsedUrlQuery } from 'querystring'\n\n/**\n * Converts the query into params.\n *\n * @param query the query to convert to params\n * @returns the params\n */\nexport function parsedUrlQueryToParams(\n  query: ParsedUrlQuery\n): Record<string, string | string[]> {\n  const params: Record<string, string | string[]> = {}\n\n  for (const [key, value] of Object.entries(query)) {\n    if (typeof value === 'undefined') continue\n    params[key] = value\n  }\n\n  return params\n}\n", "import type { IncomingMessage } from 'http'\nimport type { BaseNextRequest } from '../base-http'\nimport type { NextRequest } from '../web/exports'\nimport { ACTION_HEADER } from '../../client/components/app-router-headers'\n\nexport function getServerActionRequestMetadata(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): {\n  actionId: string | null\n  isURLEncodedAction: boolean\n  isMultipartAction: boolean\n  isFetchAction: boolean\n  isServerAction: boolean\n} {\n  let actionId: string | null\n  let contentType: string | null\n\n  if (req.headers instanceof Headers) {\n    actionId = req.headers.get(ACTION_HEADER.toLowerCase()) ?? null\n    contentType = req.headers.get('content-type')\n  } else {\n    actionId = (req.headers[ACTION_HEADER.toLowerCase()] as string) ?? null\n    contentType = req.headers['content-type'] ?? null\n  }\n\n  const isURLEncodedAction = Boolean(\n    req.method === 'POST' && contentType === 'application/x-www-form-urlencoded'\n  )\n  const isMultipartAction = Boolean(\n    req.method === 'POST' && contentType?.startsWith('multipart/form-data')\n  )\n  const isFetchAction = Boolean(\n    actionId !== undefined &&\n      typeof actionId === 'string' &&\n      req.method === 'POST'\n  )\n\n  const isServerAction = Boolean(\n    isFetchAction || isURLEncodedAction || isMultipartAction\n  )\n\n  return {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isServerAction,\n  }\n}\n\nexport function getIsServerAction(\n  req: IncomingMessage | BaseNextRequest | NextRequest\n): boolean {\n  return getServerActionRequestMetadata(req).isServerAction\n}\n", "import type { WorkStore } from '../app-render/work-async-storage.external'\nimport type { IncrementalCache } from '../lib/incremental-cache'\nimport type { RenderOpts } from '../app-render/types'\nimport type { FetchMetric } from '../base-http'\nimport type { RequestLifecycleOpts } from '../base-server'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport type { AppSegmentConfig } from '../../build/segment-config/app/app-segment-config'\nimport type { CacheLife } from '../use-cache/cache-life'\n\nimport { AfterContext } from '../after/after-context'\n\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\n\nexport type WorkStoreContext = {\n  /**\n   * The page that is being rendered. This relates to the path to the page file.\n   */\n  page: string\n\n  /**\n   * The route parameters that are currently unknown.\n   */\n  fallbackRouteParams: FallbackRouteParams | null\n\n  requestEndedState?: { ended?: boolean }\n  isPrefetchRequest?: boolean\n  renderOpts: {\n    cacheLifeProfiles?: { [profile: string]: CacheLife }\n    incrementalCache?: IncrementalCache\n    isOnDemandRevalidate?: boolean\n    fetchCache?: AppSegmentConfig['fetchCache']\n    isServerAction?: boolean\n    pendingWaitUntil?: Promise<any>\n    experimental: Pick<\n      RenderOpts['experimental'],\n      'isRoutePPREnabled' | 'dynamicIO' | 'authInterrupts'\n    >\n\n    /**\n     * Fetch metrics attached in patch-fetch.ts\n     **/\n    fetchMetrics?: FetchMetric[]\n\n    /**\n     * A hack around accessing the store value outside the context of the\n     * request.\n     *\n     * @internal\n     * @deprecated should only be used as a temporary workaround\n     */\n    // TODO: remove this when we resolve accessing the store outside the execution context\n    store?: WorkStore\n  } & Pick<\n    // Pull some properties from RenderOpts so that the docs are also\n    // mirrored.\n    RenderOpts,\n    | 'assetPrefix'\n    | 'supportsDynamicResponse'\n    | 'shouldWaitOnAllReady'\n    | 'isRevalidate'\n    | 'nextExport'\n    | 'isDraftMode'\n    | 'isDebugDynamicAccesses'\n    | 'dev'\n  > &\n    RequestLifecycleOpts &\n    Partial<Pick<RenderOpts, 'reactLoadableManifest'>>\n\n  /**\n   * The build ID of the current build.\n   */\n  buildId: string\n}\n\nexport function createWorkStore({\n  page,\n  fallbackRouteParams,\n  renderOpts,\n  requestEndedState,\n  isPrefetchRequest,\n  buildId,\n}: WorkStoreContext): WorkStore {\n  /**\n   * Rules of Static & Dynamic HTML:\n   *\n   *    1.) We must generate static HTML unless the caller explicitly opts\n   *        in to dynamic HTML support.\n   *\n   *    2.) If dynamic HTML support is requested, we must honor that request\n   *        or throw an error. It is the sole responsibility of the caller to\n   *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n   *\n   *    3.) If the request is in draft mode, we must generate dynamic HTML.\n   *\n   *    4.) If the request is a server action, we must generate dynamic HTML.\n   *\n   * These rules help ensure that other existing features like request caching,\n   * coalescing, and ISR continue working as intended.\n   */\n  const isStaticGeneration =\n    !renderOpts.shouldWaitOnAllReady &&\n    !renderOpts.supportsDynamicResponse &&\n    !renderOpts.isDraftMode &&\n    !renderOpts.isServerAction\n\n  const store: WorkStore = {\n    isStaticGeneration,\n    page,\n    fallbackRouteParams,\n    route: normalizeAppPath(page),\n    incrementalCache:\n      // we fallback to a global incremental cache for edge-runtime locally\n      // so that it can access the fs cache without mocks\n      renderOpts.incrementalCache || (globalThis as any).__incrementalCache,\n    cacheLifeProfiles: renderOpts.cacheLifeProfiles,\n    isRevalidate: renderOpts.isRevalidate,\n    isPrerendering: renderOpts.nextExport,\n    fetchCache: renderOpts.fetchCache,\n    isOnDemandRevalidate: renderOpts.isOnDemandRevalidate,\n\n    isDraftMode: renderOpts.isDraftMode,\n\n    requestEndedState,\n    isPrefetchRequest,\n    buildId,\n    reactLoadableManifest: renderOpts?.reactLoadableManifest || {},\n    assetPrefix: renderOpts?.assetPrefix || '',\n\n    afterContext: createAfterContext(renderOpts),\n    dynamicIOEnabled: renderOpts.experimental.dynamicIO,\n    dev: renderOpts.dev ?? false,\n  }\n\n  // TODO: remove this when we resolve accessing the store outside the execution context\n  renderOpts.store = store\n\n  return store\n}\n\nfunction createAfterContext(renderOpts: RequestLifecycleOpts): AfterContext {\n  const { waitUntil, onClose, onAfterTaskError } = renderOpts\n  return new AfterContext({\n    waitUntil,\n    onClose,\n    onTaskError: onAfterTaskError,\n  })\n}\n", "/**\n * For a given page path, this function ensures that there is a leading slash.\n * If there is not a leading slash, one is added, otherwise it is noop.\n */\nexport function ensureLeadingSlash(path: string) {\n  return path.startsWith('/') ? path : `/${path}`\n}\n", "import { ensureLeadingSlash } from '../../page-path/ensure-leading-slash'\nimport { isGroupSegment } from '../../segment'\n\n/**\n * Normalizes an app route so it represents the actual request path. Essentially\n * performing the following transformations:\n *\n * - `/(dashboard)/user/[id]/page` to `/user/[id]`\n * - `/(dashboard)/account/page` to `/account`\n * - `/user/[id]/page` to `/user/[id]`\n * - `/account/page` to `/account`\n * - `/page` to `/`\n * - `/(dashboard)/user/[id]/route` to `/user/[id]`\n * - `/(dashboard)/account/route` to `/account`\n * - `/user/[id]/route` to `/user/[id]`\n * - `/account/route` to `/account`\n * - `/route` to `/`\n * - `/` to `/`\n *\n * @param route the app route to normalize\n * @returns the normalized pathname\n */\nexport function normalizeAppPath(route: string) {\n  return ensureLeadingSlash(\n    route.split('/').reduce((pathname, segment, index, segments) => {\n      // Empty segments are ignored.\n      if (!segment) {\n        return pathname\n      }\n\n      // Groups are ignored.\n      if (isGroupSegment(segment)) {\n        return pathname\n      }\n\n      // Parallel segments are ignored.\n      if (segment[0] === '@') {\n        return pathname\n      }\n\n      // The last segment (if it's a leaf) should be ignored.\n      if (\n        (segment === 'page' || segment === 'route') &&\n        index === segments.length - 1\n      ) {\n        return pathname\n      }\n\n      return `${pathname}/${segment}`\n    }, '')\n  )\n}\n\n/**\n * Strips the `.rsc` extension if it's in the pathname.\n * Since this function is used on full urls it checks `?` for searchParams handling.\n */\nexport function normalizeRscURL(url: string) {\n  return url.replace(\n    /\\.rsc($|\\?)/,\n    // $1 ensures `?` is preserved\n    '$1'\n  )\n}\n", "/**\n * Get pathname from absolute path.\n *\n * @param absolutePath the absolute path\n * @returns the pathname\n */\nexport function getPathnameFromAbsolutePath(absolutePath: string) {\n  // Remove prefix including app dir\n  let appDir = '/app/'\n  if (!absolutePath.includes(appDir)) {\n    appDir = '\\\\app\\\\'\n  }\n  const [, ...parts] = absolutePath.split(appDir)\n  const relativePath = appDir[0] + parts.join(appDir)\n\n  // remove extension\n  const pathname = relativePath.split('.').slice(0, -1).join('.')\n  return pathname\n}\n", "/**\n * Cleans a URL by stripping the protocol, host, and search params.\n *\n * @param urlString the url to clean\n * @returns the cleaned url\n */\n\nexport function cleanURL(url: string | URL): URL {\n  const u = new URL(url)\n  u.host = 'localhost:3000'\n  u.search = ''\n  u.protocol = 'http'\n  return u\n}\n"], "names": ["module", "exports", "require", "__defProp", "Object", "defineProperty", "__getOwnPropDesc", "getOwnPropertyDescriptor", "__getOwnPropNames", "getOwnPropertyNames", "__hasOwnProp", "prototype", "hasOwnProperty", "src_exports", "string<PERSON><PERSON><PERSON><PERSON>", "c", "_a", "attrs", "path", "expires", "Date", "toUTCString", "maxAge", "domain", "secure", "httpOnly", "sameSite", "partitioned", "priority", "filter", "Boolean", "stringified", "name", "encodeURIComponent", "value", "length", "join", "parse<PERSON><PERSON><PERSON>", "cookie", "map", "Map", "pair", "split", "splitAt", "indexOf", "set", "key", "slice", "decodeURIComponent", "parseSetCookie", "<PERSON><PERSON><PERSON><PERSON>", "string", "attributes", "httponly", "maxage", "samesite", "fromEntries", "value2", "toLowerCase", "replace", "compact", "t", "newT", "Number", "SAME_SITE", "includes", "PRIORITY", "__export", "target", "all", "get", "enumerable", "RequestCookies", "ResponseCookies", "to", "from", "except", "desc", "call", "constructor", "requestHeaders", "_parsed", "_headers", "header", "Symbol", "iterator", "size", "args", "getAll", "Array", "_", "n", "has", "delete", "names", "result", "isArray", "clear", "keys", "for", "JSON", "stringify", "toString", "values", "v", "responseHeaders", "_b", "_c", "getSetCookie", "cookieString", "splitCookiesString", "cookiesString", "start", "ch", "lastComma", "nextStart", "cookiesSeparatorFound", "cookiesStrings", "pos", "skipWhitespace", "test", "char<PERSON>t", "push", "substring", "parsed", "normalizeCookie", "now", "bag", "headers", "serialized", "append", "options", "__nccwpck_require__", "ab", "__dirname", "e", "r", "parse", "o", "a", "s", "decode", "i", "p", "f", "u", "substr", "trim", "undefined", "tryDecode", "serialize", "encode", "isNaN", "isFinite", "Math", "floor", "Events", "EE", "fn", "context", "once", "addListener", "_events", "_eventsCount", "clearEvent", "EventEmitter", "create", "__proto__", "eventNames", "getOwnPropertySymbols", "concat", "listeners", "listenerCount", "emit", "h", "l", "arguments", "removeListener", "apply", "on", "removeAllListeners", "off", "prefixed", "then", "Promise", "_queue", "enqueue", "assign", "run", "default", "splice", "dequeue", "shift", "TimeoutError", "Error", "pTimeout", "Infinity", "setTimeout", "cancel", "clearTimeout", "empty", "_intervalCount", "_intervalEnd", "_pendingCount", "_resolveEmpty", "_resolveIdle", "carryoverConcurrencyCount", "intervalCap", "interval", "concurrency", "autoStart", "queueClass", "_carryoverConcurrencyCount", "_isIntervalIgnored", "_intervalCap", "_interval", "_queueClass", "_timeout", "timeout", "_throwOnTimeout", "throwOnTimeout", "_isPaused", "_doesIntervalAllowAnother", "_doesConcurrentAllowAnother", "_concurrency", "_next", "_tryToStartAnother", "_resolvePromises", "_onResumeInterval", "_onInterval", "_initializeIntervalIfNeeded", "_timeoutId", "_isIntervalPaused", "_intervalId", "clearInterval", "setInterval", "_processQueue", "add", "resolve", "addAll", "pause", "onEmpty", "onIdle", "sizeBy", "pending", "isPaused", "REACT_ELEMENT_TYPE", "REACT_PORTAL_TYPE", "REACT_FRAGMENT_TYPE", "REACT_STRICT_MODE_TYPE", "REACT_PROFILER_TYPE", "REACT_CONSUMER_TYPE", "REACT_CONTEXT_TYPE", "REACT_FORWARD_REF_TYPE", "REACT_SUSPENSE_TYPE", "REACT_SUSPENSE_LIST_TYPE", "REACT_MEMO_TYPE", "REACT_LAZY_TYPE", "REACT_OFFSCREEN_TYPE", "REACT_POSTPONE_TYPE", "REACT_VIEW_TRANSITION_TYPE", "MAYBE_ITERATOR_SYMBOL", "ReactNoopUpdateQueue", "isMounted", "enqueueForceUpdate", "enqueueReplaceState", "enqueueSetState", "emptyObject", "Component", "props", "updater", "refs", "ComponentDummy", "PureComponent", "isReactComponent", "setState", "partialState", "callback", "forceUpdate", "pureComponentPrototype", "isPureReactComponent", "isArrayImpl", "ReactSharedInternals", "H", "A", "T", "S", "V", "ReactElement", "type", "self", "source", "owner", "$$typeof", "ref", "isValidElement", "object", "userProvidedKeyEscapeRegex", "get<PERSON><PERSON><PERSON><PERSON>", "element", "index", "escaper<PERSON><PERSON><PERSON>", "match", "noop$1", "mapChildren", "children", "func", "count", "mapIntoArray", "array", "escapedPrefix", "nameSoFar", "oldElement", "new<PERSON>ey", "maybeIterable", "invokeCallback", "_init", "_payload", "nextNamePrefix", "next", "done", "resolveThenable", "thenable", "status", "reason", "fulfilledValue", "error", "String", "child", "lazyInitializer", "payload", "_status", "ctor", "_result", "moduleObject", "useOptimistic", "passthrough", "reducer", "reportGlobalError", "reportError", "process", "console", "noop", "Children", "for<PERSON>ach", "forEachFunc", "forEachContext", "toArray", "only", "Fragment", "Profiler", "StrictMode", "Suspense", "__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE", "__COMPILER_RUNTIME", "useMemoCache", "cache", "cloneElement", "config", "propName", "<PERSON><PERSON><PERSON><PERSON>", "createContext", "defaultValue", "_currentValue", "_currentValue2", "_threadCount", "Provider", "Consumer", "_context", "createElement", "<PERSON><PERSON><PERSON><PERSON>", "defaultProps", "createRef", "current", "experimental_useEffectEvent", "useEffectEvent", "experimental_useOptimistic", "forwardRef", "render", "lazy", "memo", "compare", "startTransition", "scope", "prevTransition", "currentTransition", "returnValue", "onStartTransitionFinish", "unstable_Activity", "unstable_SuspenseList", "unstable_ViewTransition", "unstable_addTransitionType", "pendingTransitionTypes", "unstable_getCacheForType", "resourceType", "dispatcher", "getCacheForType", "unstable_postpone", "unstable_useCacheRefresh", "useCacheRefresh", "unstable_useSwipeTransition", "previous", "useSwipeTransition", "use", "usable", "useActionState", "action", "initialState", "permalink", "useCallback", "deps", "useContext", "Context", "useDebugValue", "useDeferredValue", "initialValue", "useEffect", "createDeps", "update", "useId", "useImperativeHandle", "useInsertionEffect", "useLayoutEffect", "useMemo", "useReducer", "initialArg", "init", "useRef", "useState", "useSyncExternalStore", "subscribe", "getSnapshot", "getServerSnapshot", "useTransition", "version", "charCodeAt", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "__webpack_modules__", "getter", "__esModule", "d", "definition", "obj", "prop", "toStringTag", "globalThis", "RouteModule", "userland", "ACTION_HEADER", "FLIGHT_HEADERS", "ReflectAdapter", "receiver", "Reflect", "bind", "deleteProperty", "ReadonlyHeadersError", "callable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Headers", "Proxy", "lowercased", "original", "find", "seal", "existing", "merge", "callbackfn", "thisArg", "entries", "ReadonlyRequestCookiesError", "RequestCookiesAdapter", "cookies", "SYMBOL_MODIFY_COOKIE_VALUES", "appendMutableCookies", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getModifiedCookieValues", "modified", "resCookies", "returnedCookies", "MutableRequestCookiesAdapter", "wrap", "onUpdateCookies", "responseCookies", "modifiedV<PERSON>ues", "modifiedCookies", "Set", "updateResponseCookies", "workStore", "workAsyncStorage", "getStore", "pathWasRevalidated", "allCookies", "serializedCookies", "tempCookies", "wrappedCookies", "ensureCookiesAreStillMutable", "callingExpression", "requestStore", "getExpectedRequestStore", "phase", "NEXT_CACHE_IMPLICIT_TAG_ID", "WEBPACK_LAYERS_NAMES", "shared", "reactServerComponents", "serverSideRendering", "<PERSON><PERSON><PERSON><PERSON>", "apiNode", "apiEdge", "middleware", "instrument", "edgeAsset", "appPagesBrowser", "pagesDirBrowser", "pagesDirEdge", "pagesDirNode", "GROUP", "builtinReact", "serverOnly", "neutralTarget", "clientOnly", "bundled", "appPages", "BaseServerSpan", "LoadComponentsSpan", "NextServerSpan", "NextNodeServerSpan", "StartServerSpan", "RenderSpan", "AppRenderSpan", "RouterSpan", "NodeSpan", "AppRouteRouteHandlersSpan", "ResolveMetadataSpan", "MiddlewareSpan", "COOKIE_NAME_PRERENDER_BYPASS", "DraftModeProvider", "previewProps", "req", "isOnDemandRevalidate", "checkIsOnDemandRevalidate", "previewModeId", "revalidateOnlyGenerated", "cookieValue", "isEnabled", "_previewModeId", "_mutableCookies", "enable", "disable", "mergeMiddlewareCookies", "existingCookies", "setCookieValue", "InvariantError", "message", "endsWith", "withExecuteRevalidates", "store", "savedRevalidationState", "cloneRevalidationState", "newRevalidates", "diffRevalidationState", "prev", "curr", "prevTags", "revalidatedTags", "prevRevalidateWrites", "pendingRevalidateWrites", "tag", "pendingRevalidates", "promise", "executeRevalidates", "incrementalCache", "revalidateTag", "sharedAsyncLocalStorageNotAvailableError", "FakeAsyncLocalStorage", "exit", "enterWith", "maybeGlobalAsyncLocalStorage", "AsyncLocalStorage", "AfterContext", "waitUntil", "onClose", "onTaskError", "workUnitStores", "callback<PERSON><PERSON><PERSON>", "PromiseQueue", "after", "task", "errorWaitUntilNotAvailable", "catch", "reportTaskError", "addCallback", "workUnitStore", "workUnitAsyncStorage", "afterTaskStore", "afterTaskAsyncStorage", "rootTaskSpawnPhase", "runCallbacksOnClosePromise", "runCallbacksOnClose", "wrappedCallback", "runCallbacks", "taskKind", "handlerError", "cause", "HTTP_METHODS", "getDerivedTags", "derivedTags", "pathname", "startsWith", "pathnameParts", "curPathname", "DYNAMIC_ERROR_CODE", "DynamicServerError", "description", "digest", "isDynamicServerError", "err", "StaticGenBailoutError", "code", "HangingPromiseRejectionError", "expression", "makeHangingPromise", "signal", "hanging<PERSON>romise", "reject", "addEventListener", "ignoreReject", "hasPostpone", "React", "createDynamicTrackingState", "isDebugDynamicAccesses", "dynamicAccesses", "syncDynamicExpression", "syncDynamicErrorWithStack", "markCurrentScopeAsDynamic", "forceDynamic", "forceStatic", "dynamicShouldError", "route", "postponeWithTracking", "dynamicTracking", "revalidate", "dynamicUsageDescription", "dynamicUsageStack", "stack", "throwToInterruptStaticGeneration", "prerenderStore", "abortAndThrowOnSynchronousRequestDataAccess", "errorWithStack", "validating", "syncDynamicLogged", "abortOnSynchronousDynamicDataAccess", "createPrerenderInterruptedError", "controller", "abort", "assertPostpone", "createPostponeReason", "isDynamicPostponeReason", "cloneResponse", "body", "body1", "body2", "tee", "cloned1", "Response", "statusText", "url", "cloned2", "CachedRouteKind", "IncrementalCacheKind", "removeTrailingSlash", "parsePath", "hashIndex", "queryIndex", "<PERSON><PERSON><PERSON><PERSON>", "query", "hash", "addPathPrefix", "prefix", "addPathSuffix", "suffix", "pathHasPrefix", "Uint8Array", "TextEncoder", "WeakMap", "normalizeLocalePath", "locales", "detectedLocale", "lowercasedLocales", "locale", "segments", "segment", "REGEX_LOCALHOST_HOSTNAME", "parseURL", "base", "URL", "Internal", "NextURL", "input", "baseOrOpts", "opts", "basePath", "analyze", "info", "getNextPathnameInfo", "i18n", "trailingSlash", "nextConfig", "removePathPrefix", "withoutPrefix", "pathnameNoDataPrefix", "paths", "buildId", "parseData", "i18nProvider", "env", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "hostname", "getHostname", "host", "domainLocale", "detectDomainLocale", "domainItems", "item", "defaultLocale", "some", "domains", "formatPathname", "addLocale", "ignorePrefix", "lower", "forceLocale", "formatSearch", "search", "searchParams", "port", "protocol", "href", "origin", "password", "username", "toJSON", "clone", "Request", "scheduleImmediate", "setImmediate", "cb", "NEXT_PATCH_SYMBOL", "trackFetchMetric", "ctx", "requestEndedState", "ended", "NEXT_DEBUG_BUILD", "NEXT_SSG_FETCH_METRICS", "isStaticGeneration", "fetchMetrics", "end", "performance", "<PERSON><PERSON><PERSON><PERSON>", "idx", "nextFetchId", "stdout", "enabled", "NO_COLOR", "FORCE_COLOR", "isTTY", "CI", "TERM", "replaceClose", "str", "close", "nextIndex", "formatter", "open", "bold", "red", "green", "yellow", "magenta", "white", "maxSize", "calculateSize", "sizes", "totalSize", "warn", "touch", "evictIfNecessary", "evictLeastRecentlyUsed", "lruKey", "lruSize", "reset", "remove", "currentSize", "AUTOMATIC_ROUTE_METHODS", "handleMethodNotAllowedResponse", "ALLOWED_CODES", "NOT_FOUND", "FORBIDDEN", "UNAUTHORIZED", "isHTTPAccessFallbackError", "httpStatus", "RedirectStatusCode", "isRedirectError", "errorCode", "destination", "statusCode", "at", "printDebugThrownValueForProspectiveRender", "thrownValue", "getDigestForWellKnownError", "originalErrorStack", "stackStart", "AppRouterContext", "LayoutRouterContext", "GlobalLayoutRouterContext", "TemplateContext", "MissingSlotContext", "CacheSignal", "earlyListeners", "tickPending", "taskPending", "noMorePendingCaches", "nextTick", "inputReady", "cacheReady", "beginRead", "endRead", "isDefinitelyAValidIdentifier", "describeStringPropertyAccess", "wellKnownProperties", "errorRef", "logErrorOrWarn", "__NEXT_DYNAMIC_IO", "createDedupedByCallsiteServerErrorLoggerDev", "getMessage", "C<PERSON>d<PERSON><PERSON><PERSON>", "makeUntrackedExoticParams", "underlyingParams", "cachedParams", "createParamsAccessError", "missingProperties", "describeListOfPropertyNames", "properties", "WrappedNextRouterError", "AppRouteRouteModule", "sharedModules", "resolvedPagePath", "nextConfigOutput", "serverHooks", "actionAsyncStorage", "methods", "autoImplementMethods", "handlers", "reduce", "acc", "method", "implemented", "GET", "HEAD", "allow", "Allow", "sort", "OPTIONS", "hasNonStaticMethods", "dynamic", "isStaticGenEnabled", "mod", "generateStaticParams", "do", "handler", "actionStore", "implicitTags", "request", "trackingState", "res", "dynamicIOEnabled", "renderOpts", "experimental", "dynamicIO", "patchFetch", "createDedupeFetch", "originalFetch", "getCacheEntries", "resource", "cache<PERSON>ey", "keepalive", "mode", "redirect", "credentials", "referrer", "referrerPolicy", "integrity", "cacheEntries", "j", "response", "entry", "fetch", "createPatchedFetcher", "originFetch", "patched", "fetchUrl", "toUpperCase", "isInternal", "internal", "hideSpan", "NEXT_OTEL_FETCH_DISABLED", "fetchStart", "cacheSignal", "getTracer", "trace", "internalFetch", "kind", "SpanKind", "CLIENT", "spanName", "getRequestMeta", "cacheWarning", "cacheReasonOverride", "finalRevalidate", "isDraftMode", "isRequestInput", "field", "getNextField", "currentFetchRevalidate", "tags", "validateTags", "validTags", "invalidTags", "log", "revalidateStore", "collectedTags", "pageFetchCacheMode", "fetchCache", "isUsingNoStore", "isUnstableNoStore", "currentFetchCacheConfig", "cacheReason", "hasExplicitFetchCacheOptOut", "noFetchConfigAndForceDynamic", "validateRevalidate", "revalidateVal", "normalizedRevalidate", "initHeaders", "hasUnCacheableHeader", "isUnCacheableMethod", "hasNoExplicitCacheConfig", "autoNoCache", "isPrerendering", "renderSignal", "isCacheableRevalidate", "useCacheOrRequestStore", "serverComponentsHmrCache", "generate<PERSON>ache<PERSON>ey", "fetchIdx", "handleUnlock", "doOriginalFetch", "isStale", "requestInputFields", "reqInput", "reqOptions", "_ogBody", "otherInput", "clonedInit", "fetchType", "cacheStatus", "bodyBuffer", "arrayBuffer", "fetchedData", "<PERSON><PERSON><PERSON>", "FETCH", "data", "finally", "isForegroundRevalidate", "isHmrRefreshCache", "cachedFetchData", "isHmrRefresh", "lock", "softTags", "isRevalidate", "pendingRevalidate", "hasNextConfig", "pendingRevalidateKey", "revalidatedResult", "pendingResponse", "responses", "__nextPatched", "__nextGetStaticStore", "_nextOriginalFetch", "handlerContext", "params", "createServerParamsForRoute", "createPrerenderParams", "fallbackP<PERSON><PERSON>", "fallbackRouteParams", "hasSomeFallbackParams", "makeAbortingExoticParams", "newValue", "writable", "configurable", "makeErroringExoticParams", "augmentedUnderlying", "parsedUrlQueryToParams", "userlandRevalidate", "defaultRevalidate", "prospectiveResult", "prospectiveController", "AbortController", "prospectiveRenderIsDynamic", "prospectiveRoutePrerenderStore", "rootParams", "expire", "stale", "prerenderResumeDataCache", "aborted", "__NEXT_VERBOSE_LOGGING", "dynamicReason", "finalController", "finalRoutePrerenderStore", "responseHandled", "bodyHandled", "createDynamicIOError", "Location", "isAction", "<PERSON><PERSON><PERSON>", "getRedirectStatusCodeFromError", "pendingWaitUntil", "NEXT_PRIVATE_DEBUG_CACHE", "collectedRevalidate", "collectedExpire", "collectedStale", "handle", "staticGenerationContext", "page", "sharedContext", "isAppRoute", "getServerActionRequestMetadata", "actionId", "contentType", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isServerAction", "getImplicitTags", "newTags", "nextUrl", "createRequestStoreImpl", "renderResumeDataCache", "defaultOnUpdateCookies", "<PERSON><PERSON><PERSON><PERSON>", "getHeaders", "cleaned", "requestCookies", "getMutableCookies", "userspaceMutableCookies", "wrapWithMutableAccessCheck", "draftMode", "__serverComponentsHmrCache", "prerenderManifest", "preview", "createWorkStore", "isPrefetchRequest", "shouldWaitOnAllReady", "supportsDynamicResponse", "__incrementalCache", "cacheLifeProfiles", "nextExport", "reactLoadableManifest", "assetPrefix", "afterContext", "createAfterContext", "onAfterTaskError", "dev", "forceStaticRequestHandlers", "requireStaticRequestHandlers", "proxyNextRequest", "nextUrlHandlers", "trackDynamic", "urlCloneSymbol", "nextRequestHandlers", "nextURLSymbol", "requestCloneSymbol", "getPathnameFromAbsolutePath", "absolutePath", "appDir", "parts", "relativePath", "tracer", "setRootSpanAttribute", "<PERSON><PERSON><PERSON><PERSON>", "POST", "PUT", "DELETE", "PATCH", "searchParamsSymbol", "hrefSymbol", "toStringSymbol", "headersSymbol", "cookiesSymbol", "forceStaticNextUrlHandlers", "URLSearchParams", "cleanURL", "requireStaticNextUrlHandlers"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86]}