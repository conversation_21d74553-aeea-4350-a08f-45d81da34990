declare const styles = "\n  [data-nextjs-dialog-root] {\n    --next-dialog-radius: var(--rounded-xl);\n    --next-dialog-footer-height: var(--size-48);\n    --next-dialog-max-width: 960px;\n    --next-dialog-row-padding: 16px;\n    --next-dialog-container-padding: 12px;\n\n    display: flex;\n    flex-direction: column-reverse;\n    width: 100%;\n    max-height: calc(100% - 56px);\n    max-width: var(--next-dialog-max-width);\n    margin-right: auto;\n    margin-left: auto;\n    scale: 0.98;\n    opacity: 0;\n    transition-property: scale, opacity;\n    transition-duration: var(--transition-duration);\n    transition-timing-function: var(--timing-overlay);\n\n    &[data-rendered='true'] {\n      opacity: 1;\n      scale: 1;\n    }\n  }\n\n  [data-nextjs-dialog] {\n    outline: none;\n    overflow: hidden;\n  }\n  [data-nextjs-dialog]::-webkit-scrollbar {\n    width: 6px;\n    border-radius: 0 0 1rem 1rem;\n    margin-bottom: 1rem;\n  }\n  [data-nextjs-dialog]::-webkit-scrollbar-button {\n    display: none;\n  }\n  [data-nextjs-dialog]::-webkit-scrollbar-track {\n    border-radius: 0 0 1rem 1rem;\n    background-color: var(--color-background-100);\n  }\n  [data-nextjs-dialog]::-webkit-scrollbar-thumb {\n    border-radius: 1rem;\n    background-color: var(--color-gray-500);\n  }\n\n  \n  [data-nextjs-dialog-sizer] {\n    overflow: hidden;\n    border-radius: inherit;\n  }\n\n  [data-nextjs-dialog-backdrop] {\n    opacity: 0;\n    transition: opacity var(--transition-duration) var(--timing-overlay);\n  }\n\n  [data-nextjs-dialog-overlay][data-rendered='true']\n    [data-nextjs-dialog-backdrop] {\n    opacity: 1;\n  }\n\n  [data-nextjs-dialog-content] {\n    overflow-y: auto;\n    border: none;\n    margin: 0;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n    padding: 16px 12px;\n  }\n\n  /* Account for the footer height, when present */\n  [data-nextjs-dialog][data-has-footer='true'] [data-nextjs-dialog-body] {\n    margin-bottom: var(--next-dialog-footer-height);\n  }\n\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-header] {\n    flex-shrink: 0;\n    margin-bottom: 8px;\n  }\n\n  [data-nextjs-dialog-content] > [data-nextjs-dialog-body] {\n    position: relative;\n    flex: 1 1 auto;\n  }\n\n  [data-nextjs-dialog-footer] {\n    /* Subtract border width */\n    width: calc(100% - 2px);\n    /* \n      We make this element fixed to anchor it to the bottom during the height transition.\n      If you make this relative it will jump during the transition and not collapse or expand smoothly.\n      If you make this absolute it will remain stuck at its initial position when scrolling the dialog.\n    */\n    position: fixed;\n    bottom: 1px;\n    min-height: var(--next-dialog-footer-height);\n    border-radius: 0 0 var(--next-dialog-radius) var(--next-dialog-radius);\n    overflow: hidden;\n\n    > * {\n      min-height: var(--next-dialog-footer-height);\n    }\n  }\n\n  @media (max-height: 812px) {\n    [data-nextjs-dialog-overlay] {\n      max-height: calc(100% - 15px);\n    }\n  }\n\n  @media (min-width: 576px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 540px;\n    }\n  }\n\n  @media (min-width: 768px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 720px;\n    }\n  }\n\n  @media (min-width: 992px) {\n    [data-nextjs-dialog-root] {\n      --next-dialog-max-width: 960px;\n    }\n  }\n";
export { styles };
