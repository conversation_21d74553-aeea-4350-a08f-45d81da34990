{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/app-dev-overlay.tsx"], "sourcesContent": ["import type { OverlayState } from '../shared'\nimport type { GlobalErrorComponent } from '../../error-boundary'\n\nimport { useEffect, useState } from 'react'\nimport { AppDevOverlayErrorBoundary } from './app-dev-overlay-error-boundary'\nimport { FontStyles } from '../font/font-styles'\nimport { DevOverlay } from '../ui/dev-overlay'\nimport { handleClientError } from '../../errors/use-error-handler'\nimport { isNextRouterError } from '../../is-next-router-error'\n\nfunction readSsrError(): Error | null {\n  if (typeof document === 'undefined') {\n    return null\n  }\n\n  const ssrErrorTemplateTag = document.querySelector(\n    'template[data-next-error-message]'\n  )\n  if (ssrErrorTemplateTag) {\n    const message: string = ssrErrorTemplateTag.getAttribute(\n      'data-next-error-message'\n    )!\n    const stack = ssrErrorTemplateTag.getAttribute('data-next-error-stack')\n    const digest = ssrErrorTemplateTag.getAttribute('data-next-error-digest')\n    const error = new Error(message)\n    if (digest) {\n      ;(error as any).digest = digest\n    }\n    // Skip Next.js SSR'd internal errors that which will be handled by the error boundaries.\n    if (isNextRouterError(error)) {\n      return null\n    }\n    error.stack = stack || ''\n    return error\n  }\n\n  return null\n}\n\n// Needs to be in the same error boundary as the shell.\n// If it commits, we know we recovered from an SSR error.\n// If it doesn't commit, we errored again and React will take care of error reporting.\nfunction ReplaySsrOnlyErrors() {\n  if (process.env.NODE_ENV !== 'production') {\n    // Need to read during render. The attributes will be gone after commit.\n    const ssrError = readSsrError()\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      if (ssrError !== null) {\n        // TODO(veil): Produces wrong Owner Stack\n        // TODO(veil): Mark as recoverable error\n        // TODO(veil): console.error\n        handleClientError(ssrError, [])\n      }\n    }, [ssrError])\n  }\n\n  return null\n}\n\nexport function AppDevOverlay({\n  state,\n  globalError,\n  children,\n}: {\n  state: OverlayState\n  globalError: [GlobalErrorComponent, React.ReactNode]\n  children: React.ReactNode\n}) {\n  const [isErrorOverlayOpen, setIsErrorOverlayOpen] = useState(false)\n\n  return (\n    <>\n      <AppDevOverlayErrorBoundary\n        globalError={globalError}\n        onError={setIsErrorOverlayOpen}\n      >\n        <ReplaySsrOnlyErrors />\n        {children}\n      </AppDevOverlayErrorBoundary>\n\n      {/* Fonts can only be loaded outside the Shadow DOM. */}\n      <FontStyles />\n      <DevOverlay\n        state={state}\n        isErrorOverlayOpen={isErrorOverlayOpen}\n        setIsErrorOverlayOpen={setIsErrorOverlayOpen}\n      />\n    </>\n  )\n}\n"], "names": ["useEffect", "useState", "AppDevOverlayErrorBoundary", "FontStyles", "DevOverlay", "handleClientError", "isNextRouterError", "readSsrError", "document", "ssrErrorTemplateTag", "querySelector", "message", "getAttribute", "stack", "digest", "error", "Error", "ReplaySsrOnlyErrors", "process", "env", "NODE_ENV", "ssrError", "AppDevOverlay", "state", "globalError", "children", "isErrorOverlayOpen", "setIsErrorOverlayOpen", "onError"], "mappings": ";AAGA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,QAAO;AAC3C,SAASC,0BAA0B,QAAQ,mCAAkC;AAC7E,SAASC,UAAU,QAAQ,sBAAqB;AAChD,SAASC,UAAU,QAAQ,oBAAmB;AAC9C,SAASC,iBAAiB,QAAQ,iCAAgC;AAClE,SAASC,iBAAiB,QAAQ,6BAA4B;AAE9D,SAASC;IACP,IAAI,OAAOC,aAAa,aAAa;QACnC,OAAO;IACT;IAEA,MAAMC,sBAAsBD,SAASE,aAAa,CAChD;IAEF,IAAID,qBAAqB;QACvB,MAAME,UAAkBF,oBAAoBG,YAAY,CACtD;QAEF,MAAMC,QAAQJ,oBAAoBG,YAAY,CAAC;QAC/C,MAAME,SAASL,oBAAoBG,YAAY,CAAC;QAChD,MAAMG,QAAQ,qBAAkB,CAAlB,IAAIC,MAAML,UAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiB;QAC/B,IAAIG,QAAQ;;YACRC,MAAcD,MAAM,GAAGA;QAC3B;QACA,yFAAyF;QACzF,IAAIR,kBAAkBS,QAAQ;YAC5B,OAAO;QACT;QACAA,MAAMF,KAAK,GAAGA,SAAS;QACvB,OAAOE;IACT;IAEA,OAAO;AACT;AAEA,uDAAuD;AACvD,yDAAyD;AACzD,sFAAsF;AACtF,SAASE;IACP,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,wEAAwE;QACxE,MAAMC,WAAWd;QACjB,sDAAsD;QACtDP,UAAU;YACR,IAAIqB,aAAa,MAAM;gBACrB,yCAAyC;gBACzC,wCAAwC;gBACxC,4BAA4B;gBAC5BhB,kBAAkBgB,UAAU,EAAE;YAChC;QACF,GAAG;YAACA;SAAS;IACf;IAEA,OAAO;AACT;AAEA,OAAO,SAASC,cAAc,KAQ7B;IAR6B,IAAA,EAC5BC,KAAK,EACLC,WAAW,EACXC,QAAQ,EAKT,GAR6B;IAS5B,MAAM,CAACC,oBAAoBC,sBAAsB,GAAG1B,SAAS;IAE7D,qBACE;;0BACE,MAACC;gBACCsB,aAAaA;gBACbI,SAASD;;kCAET,KAACV;oBACAQ;;;0BAIH,KAACtB;0BACD,KAACC;gBACCmB,OAAOA;gBACPG,oBAAoBA;gBACpBC,uBAAuBA;;;;AAI/B"}