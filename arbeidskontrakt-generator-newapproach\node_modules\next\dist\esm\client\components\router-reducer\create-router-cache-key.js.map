{"version": 3, "sources": ["../../../../src/client/components/router-reducer/create-router-cache-key.ts"], "sourcesContent": ["import type { Segment } from '../../../server/app-render/types'\nimport { PAGE_SEGMENT_KEY } from '../../../shared/lib/segment'\n\nexport function createRouterCacheKey(\n  segment: Segment,\n  withoutSearchParameters: boolean = false\n) {\n  // if the segment is an array, it means it's a dynamic segment\n  // for example, ['lang', 'en', 'd']. We need to convert it to a string to store it as a cache node key.\n  if (Array.isArray(segment)) {\n    return `${segment[0]}|${segment[1]}|${segment[2]}`\n  }\n\n  // Page segments might have search parameters, ie __PAGE__?foo=bar\n  // When `withoutSearchParameters` is true, we only want to return the page segment\n  if (withoutSearchParameters && segment.startsWith(PAGE_SEGMENT_KEY)) {\n    return PAGE_SEGMENT_KEY\n  }\n\n  return segment\n}\n"], "names": ["PAGE_SEGMENT_KEY", "createRouterCache<PERSON>ey", "segment", "withoutSearchParameters", "Array", "isArray", "startsWith"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,8BAA6B;AAE9D,OAAO,SAASC,qBACdC,OAAgB,EAChBC,uBAAwC;IAAxCA,IAAAA,oCAAAA,0BAAmC;IAEnC,8DAA8D;IAC9D,uGAAuG;IACvG,IAAIC,MAAMC,OAAO,CAACH,UAAU;QAC1B,OAAO,AAAGA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE,GAAC,MAAGA,OAAO,CAAC,EAAE;IAClD;IAEA,kEAAkE;IAClE,kFAAkF;IAClF,IAAIC,2BAA2BD,QAAQI,UAAU,CAACN,mBAAmB;QACnE,OAAOA;IACT;IAEA,OAAOE;AACT"}