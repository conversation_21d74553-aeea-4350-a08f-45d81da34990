{"version": 3, "sources": ["../../src/client/app-link-gc.ts"], "sourcesContent": ["export function linkGc() {\n  // TODO-APP: Remove this logic when <PERSON>loat has GC built-in in development.\n  if (process.env.NODE_ENV !== 'production') {\n    const callback = (mutationList: MutationRecord[]) => {\n      for (const mutation of mutationList) {\n        if (mutation.type === 'childList') {\n          for (const node of mutation.addedNodes) {\n            if (\n              'tagName' in node &&\n              (node as HTMLLinkElement).tagName === 'LINK'\n            ) {\n              const link = node as HTMLLinkElement\n              if (link.dataset.precedence?.startsWith('next')) {\n                const href = link.getAttribute('href')\n                if (href) {\n                  const [resource, version] = href.split('?v=', 2)\n                  if (version) {\n                    const currentOrigin = window.location.origin\n                    const allLinks = [\n                      ...document.querySelectorAll(\n                        'link[href^=\"' + resource + '\"]'\n                      ),\n                      // It's possible that the resource is a full URL or only pathname,\n                      // so we need to remove the alternative href as well.\n                      ...document.querySelectorAll(\n                        'link[href^=\"' +\n                          (resource.startsWith(currentOrigin)\n                            ? resource.slice(currentOrigin.length)\n                            : currentOrigin + resource) +\n                          '\"]'\n                      ),\n                    ] as HTMLLinkElement[]\n\n                    for (const otherLink of allLinks) {\n                      if (otherLink.dataset.precedence?.startsWith('next')) {\n                        const otherHref = otherLink.getAttribute('href')\n                        if (otherHref) {\n                          const [, otherVersion] = otherHref.split('?v=', 2)\n                          if (!otherVersion || +otherVersion < +version) {\n                            // Delay the removal of the stylesheet to avoid FOUC\n                            // caused by `@font-face` rules, as they seem to be\n                            // a couple of ticks delayed between the old and new\n                            // styles being swapped even if the font is cached.\n                            setTimeout(() => {\n                              otherLink.remove()\n                            }, 5)\n                            const preloadLink = document.querySelector(\n                              `link[rel=\"preload\"][as=\"style\"][href=\"${otherHref}\"]`\n                            )\n                            if (preloadLink) {\n                              preloadLink.remove()\n                            }\n                          }\n                        }\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    // Create an observer instance linked to the callback function\n    const observer = new MutationObserver(callback)\n    observer.observe(document.head, {\n      childList: true,\n    })\n  }\n}\n"], "names": ["linkGc", "process", "env", "NODE_ENV", "callback", "mutationList", "mutation", "type", "node", "addedNodes", "tagName", "link", "dataset", "precedence", "startsWith", "href", "getAttribute", "resource", "version", "split", "<PERSON><PERSON><PERSON><PERSON>", "window", "location", "origin", "allLinks", "document", "querySelectorAll", "slice", "length", "otherLink", "otherHref", "otherVersion", "setTimeout", "remove", "preloadLink", "querySelector", "observer", "MutationObserver", "observe", "head", "childList"], "mappings": "AAAA,OAAO,SAASA;IACd,yEAAyE;IACzE,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,MAAMC,WAAW,CAACC;YAChB,KAAK,MAAMC,YAAYD,aAAc;gBACnC,IAAIC,SAASC,IAAI,KAAK,aAAa;oBACjC,KAAK,MAAMC,QAAQF,SAASG,UAAU,CAAE;wBACtC,IACE,aAAaD,QACb,AAACA,KAAyBE,OAAO,KAAK,QACtC;gCAEIC;4BADJ,MAAMA,OAAOH;4BACb,KAAIG,2BAAAA,KAAKC,OAAO,CAACC,UAAU,qBAAvBF,yBAAyBG,UAAU,CAAC,SAAS;gCAC/C,MAAMC,OAAOJ,KAAKK,YAAY,CAAC;gCAC/B,IAAID,MAAM;oCACR,MAAM,CAACE,UAAUC,QAAQ,GAAGH,KAAKI,KAAK,CAAC,OAAO;oCAC9C,IAAID,SAAS;wCACX,MAAME,gBAAgBC,OAAOC,QAAQ,CAACC,MAAM;wCAC5C,MAAMC,WAAW;+CACZC,SAASC,gBAAgB,CAC1B,iBAAiBT,WAAW;4CAE9B,kEAAkE;4CAClE,qDAAqD;+CAClDQ,SAASC,gBAAgB,CAC1B,iBACGT,CAAAA,SAASH,UAAU,CAACM,iBACjBH,SAASU,KAAK,CAACP,cAAcQ,MAAM,IACnCR,gBAAgBH,QAAO,IAC3B;yCAEL;wCAED,KAAK,MAAMY,aAAaL,SAAU;gDAC5BK;4CAAJ,KAAIA,gCAAAA,UAAUjB,OAAO,CAACC,UAAU,qBAA5BgB,8BAA8Bf,UAAU,CAAC,SAAS;gDACpD,MAAMgB,YAAYD,UAAUb,YAAY,CAAC;gDACzC,IAAIc,WAAW;oDACb,MAAM,GAAGC,aAAa,GAAGD,UAAUX,KAAK,CAAC,OAAO;oDAChD,IAAI,CAACY,gBAAgB,CAACA,eAAe,CAACb,SAAS;wDAC7C,oDAAoD;wDACpD,mDAAmD;wDACnD,oDAAoD;wDACpD,mDAAmD;wDACnDc,WAAW;4DACTH,UAAUI,MAAM;wDAClB,GAAG;wDACH,MAAMC,cAAcT,SAASU,aAAa,CACxC,AAAC,2CAAwCL,YAAU;wDAErD,IAAII,aAAa;4DACfA,YAAYD,MAAM;wDACpB;oDACF;gDACF;4CACF;wCACF;oCACF;gCACF;4BACF;wBACF;oBACF;gBACF;YACF;QACF;QAEA,8DAA8D;QAC9D,MAAMG,WAAW,IAAIC,iBAAiBjC;QACtCgC,SAASE,OAAO,CAACb,SAASc,IAAI,EAAE;YAC9BC,WAAW;QACb;IACF;AACF"}