{"version": 3, "sources": ["../../../../../src/build/webpack/loaders/next-app-loader/create-app-route-code.ts"], "sourcesContent": ["import path from 'path'\nimport { stringify } from 'querystring'\nimport { WEBPACK_RESOURCE_QUERIES } from '../../../../lib/constants'\nimport { isMetadataRoute } from '../../../../lib/metadata/is-metadata-route'\nimport type { NextConfig } from '../../../../server/config-shared'\nimport { AppBundlePathNormalizer } from '../../../../server/normalizers/built/app/app-bundle-path-normalizer'\nimport { AppPathnameNormalizer } from '../../../../server/normalizers/built/app/app-pathname-normalizer'\nimport { loadEntrypoint } from '../../../load-entrypoint'\nimport type { PageExtensions } from '../../../page-extensions-type'\nimport { getFilenameAndExtension } from '../next-metadata-route-loader'\n\nexport async function createAppRouteCode({\n  name,\n  page,\n  pagePath,\n  resolveAppRoute,\n  pageExtensions,\n  nextConfigOutput,\n}: {\n  name: string\n  page: string\n  pagePath: string\n  resolveAppRoute: (\n    pathname: string\n  ) => Promise<string | undefined> | string | undefined\n  pageExtensions: PageExtensions\n  nextConfigOutput: NextConfig['output']\n}): Promise<string> {\n  // routePath is the path to the route handler file,\n  // but could be aliased e.g. private-next-app-dir/favicon.ico\n  const routePath = pagePath.replace(/[\\\\/]/, '/')\n\n  // This, when used with the resolver will give us the pathname to the built\n  // route handler file.\n  let resolvedPagePath = await resolveAppRoute(routePath)\n  if (!resolvedPagePath) {\n    throw new Error(\n      `Invariant: could not resolve page path for ${name} at ${routePath}`\n    )\n  }\n\n  // If this is a metadata route, then we need to use the metadata loader for\n  // the route to ensure that the route is generated.\n  const fileBaseName = path.parse(resolvedPagePath).name\n  if (isMetadataRoute(name) && fileBaseName !== 'route') {\n    const { ext } = getFilenameAndExtension(resolvedPagePath)\n    const isDynamicRouteExtension = pageExtensions.includes(ext)\n\n    resolvedPagePath = `next-metadata-route-loader?${stringify({\n      filePath: resolvedPagePath,\n      isDynamicRouteExtension: isDynamicRouteExtension ? '1' : '0',\n    })}!?${WEBPACK_RESOURCE_QUERIES.metadataRoute}`\n  }\n\n  const pathname = new AppPathnameNormalizer().normalize(page)\n  const bundlePath = new AppBundlePathNormalizer().normalize(page)\n\n  return await loadEntrypoint(\n    'app-route',\n    {\n      VAR_USERLAND: resolvedPagePath,\n      VAR_DEFINITION_PAGE: page,\n      VAR_DEFINITION_PATHNAME: pathname,\n      VAR_DEFINITION_FILENAME: fileBaseName,\n      VAR_DEFINITION_BUNDLE_PATH: bundlePath,\n      VAR_RESOLVED_PAGE_PATH: resolvedPagePath,\n    },\n    {\n      nextConfigOutput: JSON.stringify(nextConfigOutput),\n    }\n  )\n}\n"], "names": ["path", "stringify", "WEBPACK_RESOURCE_QUERIES", "isMetadataRoute", "AppBundlePathNormalizer", "AppPathnameNormalizer", "loadEntrypoint", "getFilenameAndExtension", "createAppRouteCode", "name", "page", "pagePath", "resolveAppRoute", "pageExtensions", "nextConfigOutput", "routePath", "replace", "resolvedPagePath", "Error", "fileBaseName", "parse", "ext", "isDynamicRouteExtension", "includes", "filePath", "metadataRoute", "pathname", "normalize", "bundlePath", "VAR_USERLAND", "VAR_DEFINITION_PAGE", "VAR_DEFINITION_PATHNAME", "VAR_DEFINITION_FILENAME", "VAR_DEFINITION_BUNDLE_PATH", "VAR_RESOLVED_PAGE_PATH", "JSON"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,SAAS,QAAQ,cAAa;AACvC,SAASC,wBAAwB,QAAQ,4BAA2B;AACpE,SAASC,eAAe,QAAQ,6CAA4C;AAE5E,SAASC,uBAAuB,QAAQ,sEAAqE;AAC7G,SAASC,qBAAqB,QAAQ,mEAAkE;AACxG,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,SAASC,uBAAuB,QAAQ,gCAA+B;AAEvE,OAAO,eAAeC,mBAAmB,EACvCC,IAAI,EACJC,IAAI,EACJC,QAAQ,EACRC,eAAe,EACfC,cAAc,EACdC,gBAAgB,EAUjB;IACC,mDAAmD;IACnD,6DAA6D;IAC7D,MAAMC,YAAYJ,SAASK,OAAO,CAAC,SAAS;IAE5C,2EAA2E;IAC3E,sBAAsB;IACtB,IAAIC,mBAAmB,MAAML,gBAAgBG;IAC7C,IAAI,CAACE,kBAAkB;QACrB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,2CAA2C,EAAET,KAAK,IAAI,EAAEM,WAAW,GADhE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,2EAA2E;IAC3E,mDAAmD;IACnD,MAAMI,eAAenB,KAAKoB,KAAK,CAACH,kBAAkBR,IAAI;IACtD,IAAIN,gBAAgBM,SAASU,iBAAiB,SAAS;QACrD,MAAM,EAAEE,GAAG,EAAE,GAAGd,wBAAwBU;QACxC,MAAMK,0BAA0BT,eAAeU,QAAQ,CAACF;QAExDJ,mBAAmB,CAAC,2BAA2B,EAAEhB,UAAU;YACzDuB,UAAUP;YACVK,yBAAyBA,0BAA0B,MAAM;QAC3D,GAAG,EAAE,EAAEpB,yBAAyBuB,aAAa,EAAE;IACjD;IAEA,MAAMC,WAAW,IAAIrB,wBAAwBsB,SAAS,CAACjB;IACvD,MAAMkB,aAAa,IAAIxB,0BAA0BuB,SAAS,CAACjB;IAE3D,OAAO,MAAMJ,eACX,aACA;QACEuB,cAAcZ;QACda,qBAAqBpB;QACrBqB,yBAAyBL;QACzBM,yBAAyBb;QACzBc,4BAA4BL;QAC5BM,wBAAwBjB;IAC1B,GACA;QACEH,kBAAkBqB,KAAKlC,SAAS,CAACa;IACnC;AAEJ"}