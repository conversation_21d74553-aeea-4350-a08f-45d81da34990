"use client"

import type { FormData } from "@/types/contract"

interface ContractPreviewProps {
  formData: FormData
}

export default function ContractPreview({ formData }: ContractPreviewProps) {
  const formatDate = (dateString: string) => {
    if (!dateString) return ""
    const date = new Date(dateString)
    return date.toLocaleDateString("no-NO", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    })
  }

  const contractHTML = `
    <!DOCTYPE html>
    <html lang="no">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Arbeidskontrakt - ${formData.employeeName}</title>
      <style>
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
        
        body {
          font-family: 'Calibri', Arial, sans-serif;
          font-size: 11pt;
          line-height: 1.4;
          color: #000;
          max-width: 210mm;
          margin: 0 auto;
          padding: 20mm;
          background: white;
        }
        
        .header {
          text-align: center;
          margin-bottom: 30px;
          border-bottom: 2px solid #22c55e;
          padding-bottom: 20px;
        }
        
        .company-name {
          font-size: 18pt;
          font-weight: bold;
          color: #22c55e;
          margin-bottom: 5px;
        }
        
        .document-title {
          font-size: 16pt;
          font-weight: bold;
          margin-top: 15px;
        }
        
        .section {
          margin-bottom: 20px;
        }
        
        .section-title {
          font-size: 12pt;
          font-weight: bold;
          color: #22c55e;
          margin-bottom: 10px;
          border-bottom: 1px solid #22c55e;
          padding-bottom: 2px;
        }
        
        .info-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 20px;
          margin-bottom: 15px;
        }
        
        .info-item {
          margin-bottom: 8px;
        }
        
        .label {
          font-weight: bold;
          display: inline-block;
          min-width: 120px;
        }
        
        .signature-section {
          margin-top: 40px;
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 40px;
        }
        
        .signature-box {
          border-top: 1px solid #000;
          padding-top: 5px;
          text-align: center;
          margin-top: 40px;
        }
        
        .legal-text {
          font-size: 10pt;
          margin-top: 20px;
          padding: 10px;
          background-color: #f9f9f9;
          border-left: 3px solid #22c55e;
        }
        
        ul {
          margin: 10px 0;
          padding-left: 20px;
        }
        
        li {
          margin-bottom: 5px;
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 15px;">
          <div style="width: 40px; height: 40px; background-color: #22c55e; border-radius: 8px; margin-right: 15px; position: relative; overflow: hidden;">
            <svg viewBox="0 0 40 40" style="width: 100%; height: 100%;">
              <path d="M0 40 L14 18 L25 25 L40 10 L40 40 Z" fill="white" opacity="0.9"/>
              <path d="M0 40 L10 22 L20 28 L32 14 L40 18 L40 40 Z" fill="white" opacity="0.7"/>
            </svg>
          </div>
          <div>
            <div class="company-name">${formData.companyName}</div>
            <div style="font-size: 10pt; color: #666; margin-top: 2px;">Anleggsgartner & maskinentreprenør</div>
          </div>
        </div>
        <div>Org.nr: ${formData.companyOrgNumber}</div>
        <div>${formData.companyAddress}</div>
        <div class="document-title">ARBEIDSKONTRAKT</div>
      </div>

      <div class="section">
        <div class="section-title">1. PARTENES IDENTITET</div>
        <div class="info-grid">
          <div>
            <div class="info-item">
              <span class="label">Arbeidsgiver:</span> ${formData.companyName}
            </div>
            <div class="info-item">
              <span class="label">Org.nr:</span> ${formData.companyOrgNumber}
            </div>
            <div class="info-item">
              <span class="label">Adresse:</span> ${formData.companyAddress}
            </div>
          </div>
          <div>
            <div class="info-item">
              <span class="label">Arbeidstaker:</span> ${formData.employeeName}
            </div>
            <div class="info-item">
              <span class="label">Fødselsdato:</span> ${formatDate(formData.employeeBirthDate)}
            </div>
            <div class="info-item">
              <span class="label">Adresse:</span> ${formData.employeeAddress}
            </div>
          </div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">2. ARBEIDSSTED OG ARBEIDSOPPGAVER</div>
        <div class="info-item">
          <span class="label">Arbeidssted:</span> Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt (AML § 14-6 (1) bokstav b)
        </div>
        <div class="info-item">
          <span class="label">Stillingsbetegnelse:</span> ${formData.position} (AML § 14-6 (1) bokstav c)
        </div>
        <div class="info-item">
          <span class="label">Arbeidsoppgaver:</span> Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten (AML § 14-6 (1) bokstav c)
        </div>
      </div>

      <div class="section">
        <div class="section-title">3. ANSETTELSESFORHOLD</div>
        <div class="info-item">
          <span class="label">Tiltredelsesdato:</span> ${formatDate(formData.startDate)} (AML § 14-6 (1) bokstav d)
        </div>
        <div class="info-item">
          <span class="label">Ansettelsestype:</span> ${formData.isTemporary ? "Midlertidig ansettelse" : "Fast ansettelse"}
        </div>
        ${
          formData.isTemporary && formData.temporaryEndDate
            ? `
        <div class="info-item">
          <span class="label">Varighet:</span> Til ${formatDate(formData.temporaryEndDate)} (AML § 14-6 (1) bokstav e)
        </div>
        `
            : ""
        }
        ${
          formData.isTemporary && formData.temporaryReason
            ? `
        <div class="info-item">
          <span class="label">Grunnlag:</span> ${formData.temporaryReason} (AML § 14-6 (1) bokstav e)
        </div>
        `
            : ""
        }
        ${
          formData.probationPeriod
            ? `
        <div class="info-item">
          <span class="label">Prøvetid:</span> ${formData.probationMonths} måneder med 14 dagers gjensidig oppsigelsesfrist (AML § 15-3 (7) og § 15-6 (4)) (AML § 14-6 (1) bokstav e)
        </div>
        `
            : ""
        }
      </div>

      <div class="section">
        <div class="section-title">4. ARBEIDSTID OG LØNN</div>
        <div class="info-item">
          <span class="label">Arbeidstid:</span> ${formData.workingHoursPerWeek} t/uke, normalt ${formData.workingTime} (AML § 14-6 (1) bokstav f)
        </div>
        <div class="info-item">
          <span class="label">Pauser:</span> ${formData.breakTime} (AML § 14-6 (1) bokstav f)
        </div>
        <div class="info-item">
          <span class="label">Timesats:</span> kr ${formData.hourlyRate},- (AML § 14-6 (1) bokstav g)
        </div>
        <div class="info-item">
          <span class="label">Overtidstillegg:</span> Minst ${formData.overtimeRate}% av timelønn (AML § 10-6 (11)) (AML § 14-6 (1) bokstav g)
        </div>
        <div class="info-item">
          <span class="label">Utbetaling:</span> Den ${formData.paymentDay}. hver måned til kontonummer ${formData.accountNumber} (elektronisk) (AML § 14-6 (1) bokstav g)
        </div>
        <div class="info-item">
          <span class="label">Kjøregodtgjørelse:</span> ${formData.travelAllowance} ved bruk av egen bil i tjenesten
        </div>
        ${
          formData.ownTools
            ? `
<div class="info-item">
  <span class="label">Håndverktøygodtgjørelse:</span> kr 1,85 per time ved bruk av eget håndverktøy
</div>
`
            : ""
        }
      </div>

      <div class="section">
        <div class="section-title">5. FERIE OG PERMISJON</div>
        <div class="info-item">
          <span class="label">Ferie:</span> 5 uker pr. år (Ferieloven § 5) (AML § 14-6 (1) bokstav h)
        </div>
        <div class="info-item">
          <span class="label">Feriepenger:</span> 12% av feriepengegrunnlaget for 5 uker ferie (Ferieloven § 10 og § 11) (AML § 14-6 (1) bokstav h)
        </div>
        <div class="info-item">
          <span class="label">Betalt fravær:</span> Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom. Rett til annet betalt fravær i henhold til folketrygdloven og arbeidsmiljøloven (AML § 14-6 (1) bokstav m)
        </div>
      </div>

      <div class="section">
        <div class="section-title">6. OPPSIGELSE OG ENDRINGER</div>
        <div class="info-item">
          <span class="label">Oppsigelsesfrister:</span> ${formData.noticePeriod} (fristene øker med ansiennitet og alder iht. AML § 15-3) (AML § 14-6 (1) bokstav i)
        </div>
        <div class="info-item">
          <span class="label">Fremgangsmåte:</span> Oppsigelse skal være skriftlig. Henvisning til AML kapittel 15 (Opphør av arbeidsforhold) og kapittel 17 (Tvisteløsning) (AML § 14-6 (1) bokstav i og n)
        </div>
        <div class="info-item">
          <span class="label">Endringer i arbeidsplan:</span> ${formData.notificationRules} (AML § 14-6 (1) bokstav n)
        </div>
      </div>

      <div class="section">
        <div class="section-title">7. PENSJON OG FORSIKRING</div>
        <div class="info-item">
          <span class="label">Pensjon:</span> ${formData.pensionProvider} (org.nr ${formData.pensionOrgNumber}) (AML § 14-6 (1) bokstav k)
        </div>
        <div class="info-item">
          <span class="label">Yrkesskadeforsikring:</span> ${formData.insuranceProvider} (org.nr ${formData.insuranceOrgNumber}) (AML § 1-4)
        </div>
      </div>

      <div class="section">
        <div class="section-title">8. KOMPETANSE OG UTVIKLING</div>
        <div class="info-item">
          <span class="label">Kompetanseutvikling:</span> Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen (AML § 14-6 (1) bokstav l)
        </div>
      </div>

      <div class="section">
        <div class="section-title">9. TARIFFAVTALE</div>
        <div class="info-item">
          <span class="label">Status:</span> Ingen tariffavtale er gjeldende pr. dags dato (AML § 14-6 (1) bokstav j)
        </div>
      </div>

      <div class="legal-text">
        <strong>Juridisk grunnlag:</strong> Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle krav til obligatoriske opplysninger i arbeidskontrakter. Kontrakten skal foreligge skriftlig snarest mulig og senest syv dager etter arbeidsforholdets begynnelse for grunnleggende opplysninger, og senest en måned for øvrige bestemmelser.
      </div>

      <div class="signature-section">
        <div>
          <div class="signature-box">
            Dato og sted
          </div>
        </div>
        <div>
          <div class="signature-box">
            Dato og sted
          </div>
        </div>
      </div>

      <div class="signature-section">
        <div>
          <div class="signature-box">
            ${formData.companyName}<br>
            (Arbeidsgiver)
          </div>
        </div>
        <div>
          <div class="signature-box">
            ${formData.employeeName}<br>
            (Arbeidstaker)
          </div>
        </div>
      </div>
    </body>
    </html>
  `

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden">
      <div
        className="contract-preview"
        dangerouslySetInnerHTML={{ __html: contractHTML }}
        style={{
          fontFamily: "Calibri, Arial, sans-serif",
          fontSize: "11pt",
          lineHeight: "1.4",
          color: "#000",
          padding: "20mm",
          maxWidth: "210mm",
          margin: "0 auto",
          background: "white",
        }}
      />
    </div>
  )
}
