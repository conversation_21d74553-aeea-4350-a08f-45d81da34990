{"version": 3, "sources": ["../../../src/server/app-render/action-handler.ts"], "sourcesContent": ["import type { IncomingHttpHeaders, OutgoingHttpHeaders } from 'node:http'\nimport type { SizeLimit } from '../../types'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { AppRenderContext, GenerateFlight } from './app-render'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\n\nimport {\n  RSC_HEADER,\n  RSC_CONTENT_TYPE_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  ACTION_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getRedirectTypeFromError,\n  getURLFromRedirectError,\n} from '../../client/components/redirect'\nimport {\n  isRedirectError,\n  type RedirectType,\n} from '../../client/components/redirect-error'\nimport RenderResult from '../render-result'\nimport type { WorkStore } from '../app-render/work-async-storage.external'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  filterReqHeaders,\n  actionsForbiddenHeaders,\n} from '../lib/server-ipc/utils'\nimport { getModifiedCookieValues } from '../web/spec-extension/adapters/request-cookies'\n\nimport {\n  NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n  NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n} from '../../lib/constants'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { isCsrfOriginAllowed } from './csrf-protection'\nimport { warn } from '../../build/output/log'\nimport { RequestCookies, ResponseCookies } from '../web/spec-extension/cookies'\nimport { HeadersAdapter } from '../web/spec-extension/adapters/headers'\nimport { fromNodeOutgoingHttpHeaders } from '../web/utils'\nimport { selectWorkerForForwarding } from './action-utils'\nimport { isNodeNextRequest, isWebNextRequest } from '../base-http/helpers'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { synchronizeMutableCookies } from '../async-storage/request-store'\nimport type { TemporaryReferenceSet } from 'react-server-dom-webpack/server.edge'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\n\nfunction formDataFromSearchQueryString(query: string) {\n  const searchParams = new URLSearchParams(query)\n  const formData = new FormData()\n  for (const [key, value] of searchParams) {\n    formData.append(key, value)\n  }\n  return formData\n}\n\nfunction nodeHeadersToRecord(\n  headers: IncomingHttpHeaders | OutgoingHttpHeaders\n) {\n  const record: Record<string, string> = {}\n  for (const [key, value] of Object.entries(headers)) {\n    if (value !== undefined) {\n      record[key] = Array.isArray(value) ? value.join(', ') : `${value}`\n    }\n  }\n  return record\n}\n\nfunction getForwardedHeaders(\n  req: BaseNextRequest,\n  res: BaseNextResponse\n): Headers {\n  // Get request headers and cookies\n  const requestHeaders = req.headers\n  const requestCookies = new RequestCookies(HeadersAdapter.from(requestHeaders))\n\n  // Get response headers and cookies\n  const responseHeaders = res.getHeaders()\n  const responseCookies = new ResponseCookies(\n    fromNodeOutgoingHttpHeaders(responseHeaders)\n  )\n\n  // Merge request and response headers\n  const mergedHeaders = filterReqHeaders(\n    {\n      ...nodeHeadersToRecord(requestHeaders),\n      ...nodeHeadersToRecord(responseHeaders),\n    },\n    actionsForbiddenHeaders\n  ) as Record<string, string>\n\n  // Merge cookies into requestCookies, so responseCookies always take precedence\n  // and overwrite/delete those from requestCookies.\n  responseCookies.getAll().forEach((cookie) => {\n    if (typeof cookie.value === 'undefined') {\n      requestCookies.delete(cookie.name)\n    } else {\n      requestCookies.set(cookie)\n    }\n  })\n\n  // Update the 'cookie' header with the merged cookies\n  mergedHeaders['cookie'] = requestCookies.toString()\n\n  // Remove headers that should not be forwarded\n  delete mergedHeaders['transfer-encoding']\n\n  return new Headers(mergedHeaders)\n}\n\nasync function addRevalidationHeader(\n  res: BaseNextResponse,\n  {\n    workStore,\n    requestStore,\n  }: {\n    workStore: WorkStore\n    requestStore: RequestStore\n  }\n) {\n  await Promise.all([\n    workStore.incrementalCache?.revalidateTag(workStore.revalidatedTags || []),\n    ...Object.values(workStore.pendingRevalidates || {}),\n    ...(workStore.pendingRevalidateWrites || []),\n  ])\n\n  // If a tag was revalidated, the client router needs to invalidate all the\n  // client router cache as they may be stale. And if a path was revalidated, the\n  // client needs to invalidate all subtrees below that path.\n\n  // To keep the header size small, we use a tuple of\n  // [[revalidatedPaths], isTagRevalidated ? 1 : 0, isCookieRevalidated ? 1 : 0]\n  // instead of a JSON object.\n\n  // TODO-APP: Currently the prefetch cache doesn't have subtree information,\n  // so we need to invalidate the entire cache if a path was revalidated.\n  // TODO-APP: Currently paths are treated as tags, so the second element of the tuple\n  // is always empty.\n\n  const isTagRevalidated = workStore.revalidatedTags?.length ? 1 : 0\n  const isCookieRevalidated = getModifiedCookieValues(\n    requestStore.mutableCookies\n  ).length\n    ? 1\n    : 0\n\n  res.setHeader(\n    'x-action-revalidated',\n    JSON.stringify([[], isTagRevalidated, isCookieRevalidated])\n  )\n}\n\n/**\n * Forwards a server action request to a separate worker. Used when the requested action is not available in the current worker.\n */\nasync function createForwardedActionResponse(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  host: Host,\n  workerPathname: string,\n  basePath: string,\n  workStore: WorkStore\n) {\n  if (!host) {\n    throw new Error(\n      'Invariant: Missing `host` header from a forwarded Server Actions request.'\n    )\n  }\n\n  const forwardedHeaders = getForwardedHeaders(req, res)\n\n  // indicate that this action request was forwarded from another worker\n  // we use this to skip rendering the flight tree so that we don't update the UI\n  // with the response from the forwarded worker\n  forwardedHeaders.set('x-action-forwarded', '1')\n\n  const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n  // For standalone or the serverful mode, use the internal origin directly\n  // other than the host headers from the request.\n  const origin = process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${host.value}`\n\n  const fetchUrl = new URL(`${origin}${basePath}${workerPathname}`)\n\n  try {\n    let body: BodyInit | ReadableStream<Uint8Array> | undefined\n    if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME === 'edge' &&\n      isWebNextRequest(req)\n    ) {\n      if (!req.body) {\n        throw new Error('Invariant: missing request body.')\n      }\n\n      body = req.body\n    } else if (\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req)\n    ) {\n      body = req.stream()\n    } else {\n      throw new Error('Invariant: Unknown request type.')\n    }\n\n    // Forward the request to the new worker\n    const response = await fetch(fetchUrl, {\n      method: 'POST',\n      body,\n      duplex: 'half',\n      headers: forwardedHeaders,\n      redirect: 'manual',\n      next: {\n        // @ts-ignore\n        internal: 1,\n      },\n    })\n\n    if (\n      response.headers.get('content-type')?.startsWith(RSC_CONTENT_TYPE_HEADER)\n    ) {\n      // copy the headers from the redirect response to the response we're sending\n      for (const [key, value] of response.headers) {\n        if (!actionsForbiddenHeaders.includes(key)) {\n          res.setHeader(key, value)\n        }\n      }\n\n      return new FlightRenderResult(response.body!)\n    } else {\n      // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n      response.body?.cancel()\n    }\n  } catch (err) {\n    // we couldn't stream the forwarded response, so we'll just return an empty response\n    console.error(`failed to forward action response`, err)\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n/**\n * Returns the parsed redirect URL if we deem that it is hosted by us.\n *\n * We handle both relative and absolute redirect URLs.\n *\n * In case the redirect URL is not relative to the application we return `null`.\n */\nfunction getAppRelativeRedirectUrl(\n  basePath: string,\n  host: Host,\n  redirectUrl: string\n): URL | null {\n  if (redirectUrl.startsWith('/') || redirectUrl.startsWith('.')) {\n    // Make sure we are appending the basePath to relative URLS\n    return new URL(`${basePath}${redirectUrl}`, 'http://n')\n  }\n\n  const parsedRedirectUrl = new URL(redirectUrl)\n\n  if (host?.value !== parsedRedirectUrl.host) {\n    return null\n  }\n\n  // At this point the hosts are the same, just confirm we\n  // are routing to a path underneath the `basePath`\n  return parsedRedirectUrl.pathname.startsWith(basePath)\n    ? parsedRedirectUrl\n    : null\n}\n\nasync function createRedirectRenderResult(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  originalHost: Host,\n  redirectUrl: string,\n  redirectType: RedirectType,\n  basePath: string,\n  workStore: WorkStore\n) {\n  res.setHeader('x-action-redirect', `${redirectUrl};${redirectType}`)\n\n  // If we're redirecting to another route of this Next.js application, we'll\n  // try to stream the response from the other worker path. When that works,\n  // we can save an extra roundtrip and avoid a full page reload.\n  // When the redirect URL starts with a `/` or is to the same host, under the\n  // `basePath` we treat it as an app-relative redirect;\n  const appRelativeRedirectUrl = getAppRelativeRedirectUrl(\n    basePath,\n    originalHost,\n    redirectUrl\n  )\n\n  if (appRelativeRedirectUrl) {\n    if (!originalHost) {\n      throw new Error(\n        'Invariant: Missing `host` header from a forwarded Server Actions request.'\n      )\n    }\n\n    const forwardedHeaders = getForwardedHeaders(req, res)\n    forwardedHeaders.set(RSC_HEADER, '1')\n\n    const proto = workStore.incrementalCache?.requestProtocol || 'https'\n\n    // For standalone or the serverful mode, use the internal origin directly\n    // other than the host headers from the request.\n    const origin =\n      process.env.__NEXT_PRIVATE_ORIGIN || `${proto}://${originalHost.value}`\n\n    const fetchUrl = new URL(\n      `${origin}${appRelativeRedirectUrl.pathname}${appRelativeRedirectUrl.search}`\n    )\n\n    if (workStore.revalidatedTags) {\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATED_TAGS_HEADER,\n        workStore.revalidatedTags.join(',')\n      )\n      forwardedHeaders.set(\n        NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER,\n        workStore.incrementalCache?.prerenderManifest?.preview?.previewModeId ||\n          ''\n      )\n    }\n\n    // Ensures that when the path was revalidated we don't return a partial response on redirects\n    forwardedHeaders.delete(NEXT_ROUTER_STATE_TREE_HEADER)\n    // When an action follows a redirect, it's no longer handling an action: it's just a normal RSC request\n    // to the requested URL. We should remove the `next-action` header so that it's not treated as an action\n    forwardedHeaders.delete(ACTION_HEADER)\n\n    try {\n      const response = await fetch(fetchUrl, {\n        method: 'GET',\n        headers: forwardedHeaders,\n        next: {\n          // @ts-ignore\n          internal: 1,\n        },\n      })\n\n      if (\n        response.headers\n          .get('content-type')\n          ?.startsWith(RSC_CONTENT_TYPE_HEADER)\n      ) {\n        // copy the headers from the redirect response to the response we're sending\n        for (const [key, value] of response.headers) {\n          if (!actionsForbiddenHeaders.includes(key)) {\n            res.setHeader(key, value)\n          }\n        }\n\n        return new FlightRenderResult(response.body!)\n      } else {\n        // Since we aren't consuming the response body, we cancel it to avoid memory leaks\n        response.body?.cancel()\n      }\n    } catch (err) {\n      // we couldn't stream the redirect response, so we'll just do a normal redirect\n      console.error(`failed to get redirect response`, err)\n    }\n  }\n\n  return RenderResult.fromStatic('{}')\n}\n\n// Used to compare Host header and Origin header.\nconst enum HostType {\n  XForwardedHost = 'x-forwarded-host',\n  Host = 'host',\n}\ntype Host =\n  | {\n      type: HostType.XForwardedHost\n      value: string\n    }\n  | {\n      type: HostType.Host\n      value: string\n    }\n  | undefined\n\n/**\n * Ensures the value of the header can't create long logs.\n */\nfunction limitUntrustedHeaderValueForLogs(value: string) {\n  return value.length > 100 ? value.slice(0, 100) + '...' : value\n}\n\nexport function parseHostHeader(\n  headers: IncomingHttpHeaders,\n  originDomain?: string\n) {\n  const forwardedHostHeader = headers['x-forwarded-host']\n  const forwardedHostHeaderValue =\n    forwardedHostHeader && Array.isArray(forwardedHostHeader)\n      ? forwardedHostHeader[0]\n      : forwardedHostHeader?.split(',')?.[0]?.trim()\n  const hostHeader = headers['host']\n\n  if (originDomain) {\n    return forwardedHostHeaderValue === originDomain\n      ? {\n          type: HostType.XForwardedHost,\n          value: forwardedHostHeaderValue,\n        }\n      : hostHeader === originDomain\n        ? {\n            type: HostType.Host,\n            value: hostHeader,\n          }\n        : undefined\n  }\n\n  return forwardedHostHeaderValue\n    ? {\n        type: HostType.XForwardedHost,\n        value: forwardedHostHeaderValue,\n      }\n    : hostHeader\n      ? {\n          type: HostType.Host,\n          value: hostHeader,\n        }\n      : undefined\n}\n\ntype ServerModuleMap = Record<\n  string,\n  {\n    id: string\n    chunks: string[]\n    name: string\n  }\n>\n\ntype ServerActionsConfig = {\n  bodySizeLimit?: SizeLimit\n  allowedOrigins?: string[]\n}\n\nexport async function handleAction({\n  req,\n  res,\n  ComponentMod,\n  serverModuleMap,\n  generateFlight,\n  workStore,\n  requestStore,\n  serverActions,\n  ctx,\n}: {\n  req: BaseNextRequest\n  res: BaseNextResponse\n  ComponentMod: AppPageModule\n  serverModuleMap: ServerModuleMap\n  generateFlight: GenerateFlight\n  workStore: WorkStore\n  requestStore: RequestStore\n  serverActions?: ServerActionsConfig\n  ctx: AppRenderContext\n}): Promise<\n  | undefined\n  | {\n      type: 'not-found'\n    }\n  | {\n      type: 'done'\n      result: RenderResult | undefined\n      formState?: any\n    }\n> {\n  const contentType = req.headers['content-type']\n  const { serverActionsManifest, page } = ctx.renderOpts\n\n  const {\n    actionId,\n    isURLEncodedAction,\n    isMultipartAction,\n    isFetchAction,\n    isServerAction,\n  } = getServerActionRequestMetadata(req)\n\n  // If it's not a Server Action, skip handling.\n  if (!isServerAction) {\n    return\n  }\n\n  if (workStore.isStaticGeneration) {\n    throw new Error(\n      \"Invariant: server actions can't be handled during static rendering\"\n    )\n  }\n\n  let temporaryReferences: TemporaryReferenceSet | undefined\n\n  const finalizeAndGenerateFlight: GenerateFlight = (...args) => {\n    // When we switch to the render phase, cookies() will return\n    // `workUnitStore.cookies` instead of `workUnitStore.userspaceMutableCookies`.\n    // We want the render to see any cookie writes that we performed during the action,\n    // so we need to update the immutable cookies to reflect the changes.\n    synchronizeMutableCookies(requestStore)\n    requestStore.phase = 'render'\n    return generateFlight(...args)\n  }\n\n  requestStore.phase = 'action'\n\n  // When running actions the default is no-store, you can still `cache: 'force-cache'`\n  workStore.fetchCache = 'default-no-store'\n\n  const originDomain =\n    typeof req.headers['origin'] === 'string'\n      ? new URL(req.headers['origin']).host\n      : undefined\n  const host = parseHostHeader(req.headers)\n\n  let warning: string | undefined = undefined\n\n  function warnBadServerActionRequest() {\n    if (warning) {\n      warn(warning)\n    }\n  }\n  // This is to prevent CSRF attacks. If `x-forwarded-host` is set, we need to\n  // ensure that the request is coming from the same host.\n  if (!originDomain) {\n    // This might be an old browser that doesn't send `host` header. We ignore\n    // this case.\n    warning = 'Missing `origin` header from a forwarded Server Actions request.'\n  } else if (!host || originDomain !== host.value) {\n    // If the customer sets a list of allowed origins, we'll allow the request.\n    // These are considered safe but might be different from forwarded host set\n    // by the infra (i.e. reverse proxies).\n    if (isCsrfOriginAllowed(originDomain, serverActions?.allowedOrigins)) {\n      // Ignore it\n    } else {\n      if (host) {\n        // This seems to be an CSRF attack. We should not proceed the action.\n        console.error(\n          `\\`${\n            host.type\n          }\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            host.value\n          )}\\` does not match \\`origin\\` header with value \\`${limitUntrustedHeaderValueForLogs(\n            originDomain\n          )}\\` from a forwarded Server Actions request. Aborting the action.`\n        )\n      } else {\n        // This is an attack. We should not proceed the action.\n        console.error(\n          `\\`x-forwarded-host\\` or \\`host\\` headers are not provided. One of these is needed to compare the \\`origin\\` header from a forwarded Server Actions request. Aborting the action.`\n        )\n      }\n\n      const error = new Error('Invalid Server Actions request.')\n\n      if (isFetchAction) {\n        res.statusCode = 500\n        await Promise.all([\n          workStore.incrementalCache?.revalidateTag(\n            workStore.revalidatedTags || []\n          ),\n          ...Object.values(workStore.pendingRevalidates || {}),\n          ...(workStore.pendingRevalidateWrites || []),\n        ])\n\n        const promise = Promise.reject(error)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            actionResult: promise,\n            // if the page was not revalidated, we can skip the rendering the flight tree\n            skipFlight: !workStore.pathWasRevalidated,\n            temporaryReferences,\n          }),\n        }\n      }\n\n      throw error\n    }\n  }\n\n  // ensure we avoid caching server actions unexpectedly\n  res.setHeader(\n    'Cache-Control',\n    'no-cache, no-store, max-age=0, must-revalidate'\n  )\n\n  let boundActionArguments: unknown[] = []\n\n  const { actionAsyncStorage } = ComponentMod\n\n  let actionResult: RenderResult | undefined\n  let formState: any | undefined\n  let actionModId: string | undefined\n  const actionWasForwarded = Boolean(req.headers['x-action-forwarded'])\n\n  if (actionId) {\n    const forwardedWorker = selectWorkerForForwarding(\n      actionId,\n      page,\n      serverActionsManifest\n    )\n\n    // If forwardedWorker is truthy, it means there isn't a worker for the action\n    // in the current handler, so we forward the request to a worker that has the action.\n    if (forwardedWorker) {\n      return {\n        type: 'done',\n        result: await createForwardedActionResponse(\n          req,\n          res,\n          host,\n          forwardedWorker,\n          ctx.renderOpts.basePath,\n          workStore\n        ),\n      }\n    }\n  }\n\n  try {\n    await actionAsyncStorage.run({ isAction: true }, async () => {\n      if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME === 'edge' &&\n        isWebNextRequest(req)\n      ) {\n        if (!req.body) {\n          throw new Error('invariant: Missing request body.')\n        }\n\n        // TODO: add body limit\n\n        // Use react-server-dom-webpack/server.edge\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeAction,\n          decodeFormState,\n        } = ComponentMod\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        if (isMultipartAction) {\n          // TODO-APP: Add streaming support\n          const formData = await req.request.formData()\n          if (isFetchAction) {\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              const actionReturnedState = await workUnitAsyncStorage.run(\n                requestStore,\n                action\n              )\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n\n              requestStore.phase = 'render'\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          const reader = req.body.getReader()\n          while (true) {\n            const { done, value } = await reader.read()\n            if (done) {\n              break\n            }\n\n            chunks.push(value)\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else if (\n        // The type check here ensures that `req` is correctly typed, and the\n        // environment variable check provides dead code elimination.\n        process.env.NEXT_RUNTIME !== 'edge' &&\n        isNodeNextRequest(req)\n      ) {\n        // Use react-server-dom-webpack/server.node which supports streaming\n        const {\n          createTemporaryReferenceSet,\n          decodeReply,\n          decodeReplyFromBusboy,\n          decodeAction,\n          decodeFormState,\n        } = require(\n          `./react-server.node`\n        ) as typeof import('./react-server.node')\n\n        temporaryReferences = createTemporaryReferenceSet()\n\n        const { Transform } =\n          require('node:stream') as typeof import('node:stream')\n\n        const defaultBodySizeLimit = '1 MB'\n        const bodySizeLimit =\n          serverActions?.bodySizeLimit ?? defaultBodySizeLimit\n        const bodySizeLimitBytes =\n          bodySizeLimit !== defaultBodySizeLimit\n            ? (\n                require('next/dist/compiled/bytes') as typeof import('bytes')\n              ).parse(bodySizeLimit)\n            : 1024 * 1024 // 1 MB\n\n        let size = 0\n        const body = req.body.pipe(\n          new Transform({\n            transform(chunk, encoding, callback) {\n              size += Buffer.byteLength(chunk, encoding)\n              if (size > bodySizeLimitBytes) {\n                const { ApiError } = require('../api-utils')\n\n                callback(\n                  new ApiError(\n                    413,\n                    `Body exceeded ${bodySizeLimit} limit.\n                To configure the body size limit for Server Actions, see: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit`\n                  )\n                )\n                return\n              }\n\n              callback(null, chunk)\n            },\n          })\n        )\n\n        if (isMultipartAction) {\n          if (isFetchAction) {\n            const busboy = (require('busboy') as typeof import('busboy'))({\n              defParamCharset: 'utf8',\n              headers: req.headers,\n              limits: { fieldSize: bodySizeLimitBytes },\n            })\n\n            body.pipe(busboy)\n\n            boundActionArguments = await decodeReplyFromBusboy(\n              busboy,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            // React doesn't yet publish a busboy version of decodeAction\n            // so we polyfill the parsing of FormData.\n            const fakeRequest = new Request('http://localhost', {\n              method: 'POST',\n              // @ts-expect-error\n              headers: { 'Content-Type': contentType },\n              body: new ReadableStream({\n                start: (controller) => {\n                  body.on('data', (chunk) => {\n                    controller.enqueue(new Uint8Array(chunk))\n                  })\n                  body.on('end', () => {\n                    controller.close()\n                  })\n                  body.on('error', (err) => {\n                    controller.error(err)\n                  })\n                },\n              }),\n              duplex: 'half',\n            })\n            const formData = await fakeRequest.formData()\n            const action = await decodeAction(formData, serverModuleMap)\n            if (typeof action === 'function') {\n              // Only warn if it's a server action, otherwise skip for other post requests\n              warnBadServerActionRequest()\n\n              const actionReturnedState = await workUnitAsyncStorage.run(\n                requestStore,\n                action\n              )\n\n              formState = await decodeFormState(\n                actionReturnedState,\n                formData,\n                serverModuleMap\n              )\n\n              requestStore.phase = 'render'\n            }\n\n            // Skip the fetch path\n            return\n          }\n        } else {\n          try {\n            actionModId = getActionModIdOrError(actionId, serverModuleMap)\n          } catch (err) {\n            if (actionId !== null) {\n              console.error(err)\n            }\n            return {\n              type: 'not-found',\n            }\n          }\n\n          const chunks: Buffer[] = []\n          for await (const chunk of req.body) {\n            chunks.push(Buffer.from(chunk))\n          }\n\n          const actionData = Buffer.concat(chunks).toString('utf-8')\n\n          if (isURLEncodedAction) {\n            const formData = formDataFromSearchQueryString(actionData)\n            boundActionArguments = await decodeReply(\n              formData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          } else {\n            boundActionArguments = await decodeReply(\n              actionData,\n              serverModuleMap,\n              { temporaryReferences }\n            )\n          }\n        }\n      } else {\n        throw new Error('Invariant: Unknown request type.')\n      }\n\n      // actions.js\n      // app/page.js\n      //   action worker1\n      //     appRender1\n\n      // app/foo/page.js\n      //   action worker2\n      //     appRender\n\n      // / -> fire action -> POST / -> appRender1 -> modId for the action file\n      // /foo -> fire action -> POST /foo -> appRender2 -> modId for the action file\n\n      try {\n        actionModId =\n          actionModId ?? getActionModIdOrError(actionId, serverModuleMap)\n      } catch (err) {\n        if (actionId !== null) {\n          console.error(err)\n        }\n        return {\n          type: 'not-found',\n        }\n      }\n\n      const actionMod = (await ComponentMod.__next_app__.require(\n        actionModId\n      )) as Record<string, (...args: unknown[]) => Promise<unknown>>\n      const actionHandler =\n        actionMod[\n          // `actionId` must exist if we got here, as otherwise we would have thrown an error above\n          actionId!\n        ]\n\n      const returnVal = await workUnitAsyncStorage.run(requestStore, () =>\n        actionHandler.apply(null, boundActionArguments)\n      )\n\n      // For form actions, we need to continue rendering the page.\n      if (isFetchAction) {\n        await addRevalidationHeader(res, {\n          workStore,\n          requestStore,\n        })\n\n        actionResult = await finalizeAndGenerateFlight(req, ctx, requestStore, {\n          actionResult: Promise.resolve(returnVal),\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        })\n      }\n    })\n\n    return {\n      type: 'done',\n      result: actionResult,\n      formState,\n    }\n  } catch (err) {\n    if (isRedirectError(err)) {\n      const redirectUrl = getURLFromRedirectError(err)\n      const redirectType = getRedirectTypeFromError(err)\n\n      await addRevalidationHeader(res, {\n        workStore,\n        requestStore,\n      })\n\n      // if it's a fetch action, we'll set the status code for logging/debugging purposes\n      // but we won't set a Location header, as the redirect will be handled by the client router\n      res.statusCode = RedirectStatusCode.SeeOther\n\n      if (isFetchAction) {\n        return {\n          type: 'done',\n          result: await createRedirectRenderResult(\n            req,\n            res,\n            host,\n            redirectUrl,\n            redirectType,\n            ctx.renderOpts.basePath,\n            workStore\n          ),\n        }\n      }\n\n      res.setHeader('Location', redirectUrl)\n      return {\n        type: 'done',\n        result: RenderResult.fromStatic(''),\n      }\n    } else if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n\n      await addRevalidationHeader(res, {\n        workStore,\n        requestStore,\n      })\n\n      if (isFetchAction) {\n        const promise = Promise.reject(err)\n        try {\n          // we need to await the promise to trigger the rejection early\n          // so that it's already handled by the time we call\n          // the RSC runtime. Otherwise, it will throw an unhandled\n          // promise rejection error in the renderer.\n          await promise\n        } catch {\n          // swallow error, it's gonna be handled on the client\n        }\n        return {\n          type: 'done',\n          result: await finalizeAndGenerateFlight(req, ctx, requestStore, {\n            skipFlight: false,\n            actionResult: promise,\n            temporaryReferences,\n          }),\n        }\n      }\n      return {\n        type: 'not-found',\n      }\n    }\n\n    if (isFetchAction) {\n      res.statusCode = 500\n      await Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ])\n      const promise = Promise.reject(err)\n      try {\n        // we need to await the promise to trigger the rejection early\n        // so that it's already handled by the time we call\n        // the RSC runtime. Otherwise, it will throw an unhandled\n        // promise rejection error in the renderer.\n        await promise\n      } catch {\n        // swallow error, it's gonna be handled on the client\n      }\n\n      requestStore.phase = 'render'\n      return {\n        type: 'done',\n        result: await generateFlight(req, ctx, requestStore, {\n          actionResult: promise,\n          // if the page was not revalidated, or if the action was forwarded from another worker, we can skip the rendering the flight tree\n          skipFlight: !workStore.pathWasRevalidated || actionWasForwarded,\n          temporaryReferences,\n        }),\n      }\n    }\n\n    throw err\n  }\n}\n\n/**\n * Attempts to find the module ID for the action from the module map. When this fails, it could be a deployment skew where\n * the action came from a different deployment. It could also simply be an invalid POST request that is not a server action.\n * In either case, we'll throw an error to be handled by the caller.\n */\nfunction getActionModIdOrError(\n  actionId: string | null,\n  serverModuleMap: ServerModuleMap\n): string {\n  try {\n    // if we're missing the action ID header, we can't do any further processing\n    if (!actionId) {\n      throw new Error(\"Invariant: Missing 'next-action' header.\")\n    }\n\n    const actionModId = serverModuleMap?.[actionId]?.id\n\n    if (!actionModId) {\n      throw new Error(\n        \"Invariant: Couldn't find action module ID from module map.\"\n      )\n    }\n\n    return actionModId\n  } catch (err) {\n    throw new Error(\n      `Failed to find Server Action \"${actionId}\". This request might be from an older or newer deployment. ${\n        err instanceof Error ? `Original error: ${err.message}` : ''\n      }\\nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action`\n    )\n  }\n}\n"], "names": ["handleAction", "parseHostHeader", "formDataFromSearchQueryString", "query", "searchParams", "URLSearchParams", "formData", "FormData", "key", "value", "append", "nodeHeadersToRecord", "headers", "record", "Object", "entries", "undefined", "Array", "isArray", "join", "getForwardedHeaders", "req", "res", "requestHeaders", "requestCookies", "RequestCookies", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "from", "responseHeaders", "getHeaders", "responseCookies", "ResponseCookies", "fromNodeOutgoingHttpHeaders", "mergedHeaders", "filterReqHeaders", "actionsForbiddenHeaders", "getAll", "for<PERSON>ach", "cookie", "delete", "name", "set", "toString", "Headers", "addRevalidationHeader", "workStore", "requestStore", "Promise", "all", "incrementalCache", "revalidateTag", "revalidatedTags", "values", "pendingRevalidates", "pendingRevalidateWrites", "isTagRevalidated", "length", "isCookieRevalidated", "getModifiedCookieValues", "mutableCookies", "<PERSON><PERSON><PERSON><PERSON>", "JSON", "stringify", "createForwardedActionResponse", "host", "workerPathname", "basePath", "Error", "forwardedHeaders", "proto", "requestProtocol", "origin", "process", "env", "__NEXT_PRIVATE_ORIGIN", "fetchUrl", "URL", "response", "body", "NEXT_RUNTIME", "isWebNextRequest", "isNodeNextRequest", "stream", "fetch", "method", "duplex", "redirect", "next", "internal", "get", "startsWith", "RSC_CONTENT_TYPE_HEADER", "includes", "FlightRenderResult", "cancel", "err", "console", "error", "RenderResult", "fromStatic", "getAppRelativeRedirectUrl", "redirectUrl", "parsedRedirectUrl", "pathname", "createRedirectRenderResult", "originalHost", "redirectType", "appRelativeRedirectUrl", "RSC_HEADER", "search", "NEXT_CACHE_REVALIDATED_TAGS_HEADER", "NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER", "prerenderManifest", "preview", "previewModeId", "NEXT_ROUTER_STATE_TREE_HEADER", "ACTION_HEADER", "limitUntrustedHeaderValueForLogs", "slice", "originDomain", "forwarded<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardedHostHeaderValue", "split", "trim", "<PERSON><PERSON><PERSON><PERSON>", "type", "ComponentMod", "serverModuleMap", "generateFlight", "serverActions", "ctx", "contentType", "serverActionsManifest", "page", "renderOpts", "actionId", "isURLEncodedAction", "isMultipartAction", "isFetchAction", "isServerAction", "getServerActionRequestMetadata", "isStaticGeneration", "temporaryReferences", "finalizeAndGenerateFlight", "args", "synchronizeMutableCookies", "phase", "fetchCache", "warning", "warnBadServerActionRequest", "warn", "isCsrfOriginAllowed", "<PERSON><PERSON><PERSON><PERSON>", "statusCode", "promise", "reject", "result", "actionResult", "skipFlight", "pathWasRevalidated", "boundActionArguments", "actionAsyncStorage", "formState", "actionModId", "actionWasForwarded", "Boolean", "forwarded<PERSON><PERSON><PERSON>", "selectWorkerForForwarding", "run", "isAction", "createTemporaryReferenceSet", "decodeReply", "decodeAction", "decodeFormState", "request", "action", "actionReturnedState", "workUnitAsyncStorage", "getActionModIdOrError", "chunks", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "push", "actionData", "<PERSON><PERSON><PERSON>", "concat", "decodeReplyFromBusboy", "require", "Transform", "defaultBodySizeLimit", "bodySizeLimit", "bodySizeLimitBytes", "parse", "size", "pipe", "transform", "chunk", "encoding", "callback", "byteLength", "ApiError", "busboy", "def<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "limits", "fieldSize", "fakeRequest", "Request", "ReadableStream", "start", "controller", "on", "enqueue", "Uint8Array", "close", "actionMod", "__next_app__", "actionHandler", "returnVal", "apply", "resolve", "isRedirectError", "getURLFromRedirectError", "getRedirectTypeFromError", "RedirectStatusCode", "<PERSON><PERSON><PERSON>", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "id", "message"], "mappings": ";;;;;;;;;;;;;;;IAkcsBA,YAAY;eAAZA;;IApDNC,eAAe;eAAfA;;;kCAlYT;oCAIA;0BAIA;+BAIA;qEACkB;oCAEU;uBAI5B;gCACiC;2BAKjC;yCACwC;gCACX;qBACf;yBAC2B;yBACjB;wBACa;6BACF;yBACU;oCACjB;8BACO;8CAEL;;;;;;AAErC,SAASC,8BAA8BC,KAAa;IAClD,MAAMC,eAAe,IAAIC,gBAAgBF;IACzC,MAAMG,WAAW,IAAIC;IACrB,KAAK,MAAM,CAACC,KAAKC,MAAM,IAAIL,aAAc;QACvCE,SAASI,MAAM,CAACF,KAAKC;IACvB;IACA,OAAOH;AACT;AAEA,SAASK,oBACPC,OAAkD;IAElD,MAAMC,SAAiC,CAAC;IACxC,KAAK,MAAM,CAACL,KAAKC,MAAM,IAAIK,OAAOC,OAAO,CAACH,SAAU;QAClD,IAAIH,UAAUO,WAAW;YACvBH,MAAM,CAACL,IAAI,GAAGS,MAAMC,OAAO,CAACT,SAASA,MAAMU,IAAI,CAAC,QAAQ,GAAGV,OAAO;QACpE;IACF;IACA,OAAOI;AACT;AAEA,SAASO,oBACPC,GAAoB,EACpBC,GAAqB;IAErB,kCAAkC;IAClC,MAAMC,iBAAiBF,IAAIT,OAAO;IAClC,MAAMY,iBAAiB,IAAIC,uBAAc,CAACC,uBAAc,CAACC,IAAI,CAACJ;IAE9D,mCAAmC;IACnC,MAAMK,kBAAkBN,IAAIO,UAAU;IACtC,MAAMC,kBAAkB,IAAIC,wBAAe,CACzCC,IAAAA,mCAA2B,EAACJ;IAG9B,qCAAqC;IACrC,MAAMK,gBAAgBC,IAAAA,uBAAgB,EACpC;QACE,GAAGvB,oBAAoBY,eAAe;QACtC,GAAGZ,oBAAoBiB,gBAAgB;IACzC,GACAO,8BAAuB;IAGzB,+EAA+E;IAC/E,kDAAkD;IAClDL,gBAAgBM,MAAM,GAAGC,OAAO,CAAC,CAACC;QAChC,IAAI,OAAOA,OAAO7B,KAAK,KAAK,aAAa;YACvCe,eAAee,MAAM,CAACD,OAAOE,IAAI;QACnC,OAAO;YACLhB,eAAeiB,GAAG,CAACH;QACrB;IACF;IAEA,qDAAqD;IACrDL,aAAa,CAAC,SAAS,GAAGT,eAAekB,QAAQ;IAEjD,8CAA8C;IAC9C,OAAOT,aAAa,CAAC,oBAAoB;IAEzC,OAAO,IAAIU,QAAQV;AACrB;AAEA,eAAeW,sBACbtB,GAAqB,EACrB,EACEuB,SAAS,EACTC,YAAY,EAIb;QAGCD,6BAkBuBA;IAnBzB,MAAME,QAAQC,GAAG,CAAC;SAChBH,8BAAAA,UAAUI,gBAAgB,qBAA1BJ,4BAA4BK,aAAa,CAACL,UAAUM,eAAe,IAAI,EAAE;WACtErC,OAAOsC,MAAM,CAACP,UAAUQ,kBAAkB,IAAI,CAAC;WAC9CR,UAAUS,uBAAuB,IAAI,EAAE;KAC5C;IAED,0EAA0E;IAC1E,+EAA+E;IAC/E,2DAA2D;IAE3D,mDAAmD;IACnD,8EAA8E;IAC9E,4BAA4B;IAE5B,2EAA2E;IAC3E,uEAAuE;IACvE,oFAAoF;IACpF,mBAAmB;IAEnB,MAAMC,mBAAmBV,EAAAA,6BAAAA,UAAUM,eAAe,qBAAzBN,2BAA2BW,MAAM,IAAG,IAAI;IACjE,MAAMC,sBAAsBC,IAAAA,uCAAuB,EACjDZ,aAAaa,cAAc,EAC3BH,MAAM,GACJ,IACA;IAEJlC,IAAIsC,SAAS,CACX,wBACAC,KAAKC,SAAS,CAAC;QAAC,EAAE;QAAEP;QAAkBE;KAAoB;AAE9D;AAEA;;CAEC,GACD,eAAeM,8BACb1C,GAAoB,EACpBC,GAAqB,EACrB0C,IAAU,EACVC,cAAsB,EACtBC,QAAgB,EAChBrB,SAAoB;QAeNA;IAbd,IAAI,CAACmB,MAAM;QACT,MAAM,qBAEL,CAFK,IAAIG,MACR,8EADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,mBAAmBhD,oBAAoBC,KAAKC;IAElD,sEAAsE;IACtE,+EAA+E;IAC/E,8CAA8C;IAC9C8C,iBAAiB3B,GAAG,CAAC,sBAAsB;IAE3C,MAAM4B,QAAQxB,EAAAA,8BAAAA,UAAUI,gBAAgB,qBAA1BJ,4BAA4ByB,eAAe,KAAI;IAE7D,yEAAyE;IACzE,gDAAgD;IAChD,MAAMC,SAASC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAEL,KAAKvD,KAAK,EAAE;IAE9E,MAAMkE,WAAW,IAAIC,IAAI,GAAGL,SAASL,WAAWD,gBAAgB;IAEhE,IAAI;YAsCAY;QArCF,IAAIC;QACJ,IACE,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAAC3D,MACjB;YACA,IAAI,CAACA,IAAIyD,IAAI,EAAE;gBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEAW,OAAOzD,IAAIyD,IAAI;QACjB,OAAO,IACL,qEAAqE;QACrE,6DAA6D;QAC7DN,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAAC5D,MAClB;YACAyD,OAAOzD,IAAI6D,MAAM;QACnB,OAAO;YACL,MAAM,qBAA6C,CAA7C,IAAIf,MAAM,qCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA4C;QACpD;QAEA,wCAAwC;QACxC,MAAMU,WAAW,MAAMM,MAAMR,UAAU;YACrCS,QAAQ;YACRN;YACAO,QAAQ;YACRzE,SAASwD;YACTkB,UAAU;YACVC,MAAM;gBACJ,aAAa;gBACbC,UAAU;YACZ;QACF;QAEA,KACEX,wBAAAA,SAASjE,OAAO,CAAC6E,GAAG,CAAC,oCAArBZ,sBAAsCa,UAAU,CAACC,yCAAuB,GACxE;YACA,4EAA4E;YAC5E,KAAK,MAAM,CAACnF,KAAKC,MAAM,IAAIoE,SAASjE,OAAO,CAAE;gBAC3C,IAAI,CAACuB,8BAAuB,CAACyD,QAAQ,CAACpF,MAAM;oBAC1Cc,IAAIsC,SAAS,CAACpD,KAAKC;gBACrB;YACF;YAEA,OAAO,IAAIoF,sCAAkB,CAAChB,SAASC,IAAI;QAC7C,OAAO;gBACL,kFAAkF;YAClFD;aAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;QACvB;IACF,EAAE,OAAOC,KAAK;QACZ,oFAAoF;QACpFC,QAAQC,KAAK,CAAC,CAAC,iCAAiC,CAAC,EAAEF;IACrD;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAEA;;;;;;CAMC,GACD,SAASC,0BACPlC,QAAgB,EAChBF,IAAU,EACVqC,WAAmB;IAEnB,IAAIA,YAAYX,UAAU,CAAC,QAAQW,YAAYX,UAAU,CAAC,MAAM;QAC9D,2DAA2D;QAC3D,OAAO,IAAId,IAAI,GAAGV,WAAWmC,aAAa,EAAE;IAC9C;IAEA,MAAMC,oBAAoB,IAAI1B,IAAIyB;IAElC,IAAIrC,CAAAA,wBAAAA,KAAMvD,KAAK,MAAK6F,kBAAkBtC,IAAI,EAAE;QAC1C,OAAO;IACT;IAEA,wDAAwD;IACxD,kDAAkD;IAClD,OAAOsC,kBAAkBC,QAAQ,CAACb,UAAU,CAACxB,YACzCoC,oBACA;AACN;AAEA,eAAeE,2BACbnF,GAAoB,EACpBC,GAAqB,EACrBmF,YAAkB,EAClBJ,WAAmB,EACnBK,YAA0B,EAC1BxC,QAAgB,EAChBrB,SAAoB;IAEpBvB,IAAIsC,SAAS,CAAC,qBAAqB,GAAGyC,YAAY,CAAC,EAAEK,cAAc;IAEnE,2EAA2E;IAC3E,0EAA0E;IAC1E,+DAA+D;IAC/D,4EAA4E;IAC5E,sDAAsD;IACtD,MAAMC,yBAAyBP,0BAC7BlC,UACAuC,cACAJ;IAGF,IAAIM,wBAAwB;YAUZ9D;QATd,IAAI,CAAC4D,cAAc;YACjB,MAAM,qBAEL,CAFK,IAAItC,MACR,8EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMC,mBAAmBhD,oBAAoBC,KAAKC;QAClD8C,iBAAiB3B,GAAG,CAACmE,4BAAU,EAAE;QAEjC,MAAMvC,QAAQxB,EAAAA,8BAAAA,UAAUI,gBAAgB,qBAA1BJ,4BAA4ByB,eAAe,KAAI;QAE7D,yEAAyE;QACzE,gDAAgD;QAChD,MAAMC,SACJC,QAAQC,GAAG,CAACC,qBAAqB,IAAI,GAAGL,MAAM,GAAG,EAAEoC,aAAahG,KAAK,EAAE;QAEzE,MAAMkE,WAAW,IAAIC,IACnB,GAAGL,SAASoC,uBAAuBJ,QAAQ,GAAGI,uBAAuBE,MAAM,EAAE;QAG/E,IAAIhE,UAAUM,eAAe,EAAE;gBAO3BN,uDAAAA,+CAAAA;YANFuB,iBAAiB3B,GAAG,CAClBqE,6CAAkC,EAClCjE,UAAUM,eAAe,CAAChC,IAAI,CAAC;YAEjCiD,iBAAiB3B,GAAG,CAClBsE,iDAAsC,EACtClE,EAAAA,+BAAAA,UAAUI,gBAAgB,sBAA1BJ,gDAAAA,6BAA4BmE,iBAAiB,sBAA7CnE,wDAAAA,8CAA+CoE,OAAO,qBAAtDpE,sDAAwDqE,aAAa,KACnE;QAEN;QAEA,6FAA6F;QAC7F9C,iBAAiB7B,MAAM,CAAC4E,+CAA6B;QACrD,uGAAuG;QACvG,wGAAwG;QACxG/C,iBAAiB7B,MAAM,CAAC6E,+BAAa;QAErC,IAAI;gBAWAvC;YAVF,MAAMA,WAAW,MAAMM,MAAMR,UAAU;gBACrCS,QAAQ;gBACRxE,SAASwD;gBACTmB,MAAM;oBACJ,aAAa;oBACbC,UAAU;gBACZ;YACF;YAEA,KACEX,wBAAAA,SAASjE,OAAO,CACb6E,GAAG,CAAC,oCADPZ,sBAEIa,UAAU,CAACC,yCAAuB,GACtC;gBACA,4EAA4E;gBAC5E,KAAK,MAAM,CAACnF,KAAKC,MAAM,IAAIoE,SAASjE,OAAO,CAAE;oBAC3C,IAAI,CAACuB,8BAAuB,CAACyD,QAAQ,CAACpF,MAAM;wBAC1Cc,IAAIsC,SAAS,CAACpD,KAAKC;oBACrB;gBACF;gBAEA,OAAO,IAAIoF,sCAAkB,CAAChB,SAASC,IAAI;YAC7C,OAAO;oBACL,kFAAkF;gBAClFD;iBAAAA,iBAAAA,SAASC,IAAI,qBAAbD,eAAeiB,MAAM;YACvB;QACF,EAAE,OAAOC,KAAK;YACZ,+EAA+E;YAC/EC,QAAQC,KAAK,CAAC,CAAC,+BAA+B,CAAC,EAAEF;QACnD;IACF;IAEA,OAAOG,qBAAY,CAACC,UAAU,CAAC;AACjC;AAkBA;;CAEC,GACD,SAASkB,iCAAiC5G,KAAa;IACrD,OAAOA,MAAM+C,MAAM,GAAG,MAAM/C,MAAM6G,KAAK,CAAC,GAAG,OAAO,QAAQ7G;AAC5D;AAEO,SAASR,gBACdW,OAA4B,EAC5B2G,YAAqB;QAMfC,6BAAAA;IAJN,MAAMA,sBAAsB5G,OAAO,CAAC,mBAAmB;IACvD,MAAM6G,2BACJD,uBAAuBvG,MAAMC,OAAO,CAACsG,uBACjCA,mBAAmB,CAAC,EAAE,GACtBA,wCAAAA,6BAAAA,oBAAqBE,KAAK,CAAC,0BAA3BF,8BAAAA,0BAAiC,CAAC,EAAE,qBAApCA,4BAAsCG,IAAI;IAChD,MAAMC,aAAahH,OAAO,CAAC,OAAO;IAElC,IAAI2G,cAAc;QAChB,OAAOE,6BAA6BF,eAChC;YACEM,IAAI;YACJpH,OAAOgH;QACT,IACAG,eAAeL,eACb;YACEM,IAAI;YACJpH,OAAOmH;QACT,IACA5G;IACR;IAEA,OAAOyG,2BACH;QACEI,IAAI;QACJpH,OAAOgH;IACT,IACAG,aACE;QACEC,IAAI;QACJpH,OAAOmH;IACT,IACA5G;AACR;AAgBO,eAAehB,aAAa,EACjCqB,GAAG,EACHC,GAAG,EACHwG,YAAY,EACZC,eAAe,EACfC,cAAc,EACdnF,SAAS,EACTC,YAAY,EACZmF,aAAa,EACbC,GAAG,EAWJ;IAWC,MAAMC,cAAc9G,IAAIT,OAAO,CAAC,eAAe;IAC/C,MAAM,EAAEwH,qBAAqB,EAAEC,IAAI,EAAE,GAAGH,IAAII,UAAU;IAEtD,MAAM,EACJC,QAAQ,EACRC,kBAAkB,EAClBC,iBAAiB,EACjBC,aAAa,EACbC,cAAc,EACf,GAAGC,IAAAA,uDAA8B,EAACvH;IAEnC,8CAA8C;IAC9C,IAAI,CAACsH,gBAAgB;QACnB;IACF;IAEA,IAAI9F,UAAUgG,kBAAkB,EAAE;QAChC,MAAM,qBAEL,CAFK,IAAI1E,MACR,uEADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI2E;IAEJ,MAAMC,4BAA4C,CAAC,GAAGC;QACpD,4DAA4D;QAC5D,8EAA8E;QAC9E,mFAAmF;QACnF,qEAAqE;QACrEC,IAAAA,uCAAyB,EAACnG;QAC1BA,aAAaoG,KAAK,GAAG;QACrB,OAAOlB,kBAAkBgB;IAC3B;IAEAlG,aAAaoG,KAAK,GAAG;IAErB,qFAAqF;IACrFrG,UAAUsG,UAAU,GAAG;IAEvB,MAAM5B,eACJ,OAAOlG,IAAIT,OAAO,CAAC,SAAS,KAAK,WAC7B,IAAIgE,IAAIvD,IAAIT,OAAO,CAAC,SAAS,EAAEoD,IAAI,GACnChD;IACN,MAAMgD,OAAO/D,gBAAgBoB,IAAIT,OAAO;IAExC,IAAIwI,UAA8BpI;IAElC,SAASqI;QACP,IAAID,SAAS;YACXE,IAAAA,SAAI,EAACF;QACP;IACF;IACA,4EAA4E;IAC5E,wDAAwD;IACxD,IAAI,CAAC7B,cAAc;QACjB,0EAA0E;QAC1E,aAAa;QACb6B,UAAU;IACZ,OAAO,IAAI,CAACpF,QAAQuD,iBAAiBvD,KAAKvD,KAAK,EAAE;QAC/C,2EAA2E;QAC3E,2EAA2E;QAC3E,uCAAuC;QACvC,IAAI8I,IAAAA,mCAAmB,EAAChC,cAAcU,iCAAAA,cAAeuB,cAAc,GAAG;QACpE,YAAY;QACd,OAAO;YACL,IAAIxF,MAAM;gBACR,qEAAqE;gBACrEgC,QAAQC,KAAK,CACX,CAAC,EAAE,EACDjC,KAAK6D,IAAI,CACV,uBAAuB,EAAER,iCACxBrD,KAAKvD,KAAK,EACV,iDAAiD,EAAE4G,iCACnDE,cACA,gEAAgE,CAAC;YAEvE,OAAO;gBACL,uDAAuD;gBACvDvB,QAAQC,KAAK,CACX,CAAC,gLAAgL,CAAC;YAEtL;YAEA,MAAMA,QAAQ,qBAA4C,CAA5C,IAAI9B,MAAM,oCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAA2C;YAEzD,IAAIuE,eAAe;oBAGf7F;gBAFFvB,IAAImI,UAAU,GAAG;gBACjB,MAAM1G,QAAQC,GAAG,CAAC;qBAChBH,8BAAAA,UAAUI,gBAAgB,qBAA1BJ,4BAA4BK,aAAa,CACvCL,UAAUM,eAAe,IAAI,EAAE;uBAE9BrC,OAAOsC,MAAM,CAACP,UAAUQ,kBAAkB,IAAI,CAAC;uBAC9CR,UAAUS,uBAAuB,IAAI,EAAE;iBAC5C;gBAED,MAAMoG,UAAU3G,QAAQ4G,MAAM,CAAC1D;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAMyD;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBAEA,OAAO;oBACL7B,MAAM;oBACN+B,QAAQ,MAAMb,0BAA0B1H,KAAK6G,KAAKpF,cAAc;wBAC9D+G,cAAcH;wBACd,6EAA6E;wBAC7EI,YAAY,CAACjH,UAAUkH,kBAAkB;wBACzCjB;oBACF;gBACF;YACF;YAEA,MAAM7C;QACR;IACF;IAEA,sDAAsD;IACtD3E,IAAIsC,SAAS,CACX,iBACA;IAGF,IAAIoG,uBAAkC,EAAE;IAExC,MAAM,EAAEC,kBAAkB,EAAE,GAAGnC;IAE/B,IAAI+B;IACJ,IAAIK;IACJ,IAAIC;IACJ,MAAMC,qBAAqBC,QAAQhJ,IAAIT,OAAO,CAAC,qBAAqB;IAEpE,IAAI2H,UAAU;QACZ,MAAM+B,kBAAkBC,IAAAA,sCAAyB,EAC/ChC,UACAF,MACAD;QAGF,6EAA6E;QAC7E,qFAAqF;QACrF,IAAIkC,iBAAiB;YACnB,OAAO;gBACLzC,MAAM;gBACN+B,QAAQ,MAAM7F,8BACZ1C,KACAC,KACA0C,MACAsG,iBACApC,IAAII,UAAU,CAACpE,QAAQ,EACvBrB;YAEJ;QACF;IACF;IAEA,IAAI;QACF,MAAMoH,mBAAmBO,GAAG,CAAC;YAAEC,UAAU;QAAK,GAAG;YAC/C,IACE,qEAAqE;YACrE,6DAA6D;YAC7DjG,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BC,IAAAA,yBAAgB,EAAC3D,MACjB;gBACA,IAAI,CAACA,IAAIyD,IAAI,EAAE;oBACb,MAAM,qBAA6C,CAA7C,IAAIX,MAAM,qCAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4C;gBACpD;gBAEA,uBAAuB;gBAEvB,2CAA2C;gBAC3C,MAAM,EACJuG,2BAA2B,EAC3BC,WAAW,EACXC,YAAY,EACZC,eAAe,EAChB,GAAG/C;gBAEJgB,sBAAsB4B;gBAEtB,IAAIjC,mBAAmB;oBACrB,kCAAkC;oBAClC,MAAMnI,WAAW,MAAMe,IAAIyJ,OAAO,CAACxK,QAAQ;oBAC3C,IAAIoI,eAAe;wBACjBsB,uBAAuB,MAAMW,YAC3BrK,UACAyH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,MAAMiC,SAAS,MAAMH,aAAatK,UAAUyH;wBAC5C,IAAI,OAAOgD,WAAW,YAAY;4BAChC,4EAA4E;4BAC5E1B;4BAEA,MAAM2B,sBAAsB,MAAMC,kDAAoB,CAACT,GAAG,CACxD1H,cACAiI;4BAGFb,YAAY,MAAMW,gBAChBG,qBACA1K,UACAyH;4BAGFjF,aAAaoG,KAAK,GAAG;wBACvB;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFiB,cAAce,sBAAsB3C,UAAUR;oBAChD,EAAE,OAAOhC,KAAK;wBACZ,IAAIwC,aAAa,MAAM;4BACrBvC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACL8B,MAAM;wBACR;oBACF;oBAEA,MAAMsD,SAAmB,EAAE;oBAC3B,MAAMC,SAAS/J,IAAIyD,IAAI,CAACuG,SAAS;oBACjC,MAAO,KAAM;wBACX,MAAM,EAAEC,IAAI,EAAE7K,KAAK,EAAE,GAAG,MAAM2K,OAAOG,IAAI;wBACzC,IAAID,MAAM;4BACR;wBACF;wBAEAH,OAAOK,IAAI,CAAC/K;oBACd;oBAEA,MAAMgL,aAAaC,OAAOC,MAAM,CAACR,QAAQzI,QAAQ,CAAC;oBAElD,IAAI8F,oBAAoB;wBACtB,MAAMlI,WAAWJ,8BAA8BuL;wBAC/CzB,uBAAuB,MAAMW,YAC3BrK,UACAyH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLkB,uBAAuB,MAAMW,YAC3Bc,YACA1D,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO,IACL,qEAAqE;YACrE,6DAA6D;YAC7DtE,QAAQC,GAAG,CAACM,YAAY,KAAK,UAC7BE,IAAAA,0BAAiB,EAAC5D,MAClB;gBACA,oEAAoE;gBACpE,MAAM,EACJqJ,2BAA2B,EAC3BC,WAAW,EACXiB,qBAAqB,EACrBhB,YAAY,EACZC,eAAe,EAChB,GAAGgB,QACF,CAAC,mBAAmB,CAAC;gBAGvB/C,sBAAsB4B;gBAEtB,MAAM,EAAEoB,SAAS,EAAE,GACjBD,QAAQ;gBAEV,MAAME,uBAAuB;gBAC7B,MAAMC,gBACJ/D,CAAAA,iCAAAA,cAAe+D,aAAa,KAAID;gBAClC,MAAME,qBACJD,kBAAkBD,uBACd,AACEF,QAAQ,4BACRK,KAAK,CAACF,iBACR,OAAO,KAAK,OAAO;;gBAEzB,IAAIG,OAAO;gBACX,MAAMrH,OAAOzD,IAAIyD,IAAI,CAACsH,IAAI,CACxB,IAAIN,UAAU;oBACZO,WAAUC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ;wBACjCL,QAAQT,OAAOe,UAAU,CAACH,OAAOC;wBACjC,IAAIJ,OAAOF,oBAAoB;4BAC7B,MAAM,EAAES,QAAQ,EAAE,GAAGb,QAAQ;4BAE7BW,SACE,qBAIC,CAJD,IAAIE,SACF,KACA,CAAC,cAAc,EAAEV,cAAc;8JAC2G,CAAC,GAH7I,qBAAA;uCAAA;4CAAA;8CAAA;4BAIA;4BAEF;wBACF;wBAEAQ,SAAS,MAAMF;oBACjB;gBACF;gBAGF,IAAI7D,mBAAmB;oBACrB,IAAIC,eAAe;wBACjB,MAAMiE,SAAS,AAACd,QAAQ,UAAsC;4BAC5De,iBAAiB;4BACjBhM,SAASS,IAAIT,OAAO;4BACpBiM,QAAQ;gCAAEC,WAAWb;4BAAmB;wBAC1C;wBAEAnH,KAAKsH,IAAI,CAACO;wBAEV3C,uBAAuB,MAAM4B,sBAC3Be,QACA5E,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACL,6DAA6D;wBAC7D,0CAA0C;wBAC1C,MAAMiE,cAAc,IAAIC,QAAQ,oBAAoB;4BAClD5H,QAAQ;4BACR,mBAAmB;4BACnBxE,SAAS;gCAAE,gBAAgBuH;4BAAY;4BACvCrD,MAAM,IAAImI,eAAe;gCACvBC,OAAO,CAACC;oCACNrI,KAAKsI,EAAE,CAAC,QAAQ,CAACd;wCACfa,WAAWE,OAAO,CAAC,IAAIC,WAAWhB;oCACpC;oCACAxH,KAAKsI,EAAE,CAAC,OAAO;wCACbD,WAAWI,KAAK;oCAClB;oCACAzI,KAAKsI,EAAE,CAAC,SAAS,CAACrH;wCAChBoH,WAAWlH,KAAK,CAACF;oCACnB;gCACF;4BACF;4BACAV,QAAQ;wBACV;wBACA,MAAM/E,WAAW,MAAMyM,YAAYzM,QAAQ;wBAC3C,MAAMyK,SAAS,MAAMH,aAAatK,UAAUyH;wBAC5C,IAAI,OAAOgD,WAAW,YAAY;4BAChC,4EAA4E;4BAC5E1B;4BAEA,MAAM2B,sBAAsB,MAAMC,kDAAoB,CAACT,GAAG,CACxD1H,cACAiI;4BAGFb,YAAY,MAAMW,gBAChBG,qBACA1K,UACAyH;4BAGFjF,aAAaoG,KAAK,GAAG;wBACvB;wBAEA,sBAAsB;wBACtB;oBACF;gBACF,OAAO;oBACL,IAAI;wBACFiB,cAAce,sBAAsB3C,UAAUR;oBAChD,EAAE,OAAOhC,KAAK;wBACZ,IAAIwC,aAAa,MAAM;4BACrBvC,QAAQC,KAAK,CAACF;wBAChB;wBACA,OAAO;4BACL8B,MAAM;wBACR;oBACF;oBAEA,MAAMsD,SAAmB,EAAE;oBAC3B,WAAW,MAAMmB,SAASjL,IAAIyD,IAAI,CAAE;wBAClCqG,OAAOK,IAAI,CAACE,OAAO/J,IAAI,CAAC2K;oBAC1B;oBAEA,MAAMb,aAAaC,OAAOC,MAAM,CAACR,QAAQzI,QAAQ,CAAC;oBAElD,IAAI8F,oBAAoB;wBACtB,MAAMlI,WAAWJ,8BAA8BuL;wBAC/CzB,uBAAuB,MAAMW,YAC3BrK,UACAyH,iBACA;4BAAEe;wBAAoB;oBAE1B,OAAO;wBACLkB,uBAAuB,MAAMW,YAC3Bc,YACA1D,iBACA;4BAAEe;wBAAoB;oBAE1B;gBACF;YACF,OAAO;gBACL,MAAM,qBAA6C,CAA7C,IAAI3E,MAAM,qCAAV,qBAAA;2BAAA;gCAAA;kCAAA;gBAA4C;YACpD;YAEA,aAAa;YACb,cAAc;YACd,mBAAmB;YACnB,iBAAiB;YAEjB,kBAAkB;YAClB,mBAAmB;YACnB,gBAAgB;YAEhB,wEAAwE;YACxE,8EAA8E;YAE9E,IAAI;gBACFgG,cACEA,eAAee,sBAAsB3C,UAAUR;YACnD,EAAE,OAAOhC,KAAK;gBACZ,IAAIwC,aAAa,MAAM;oBACrBvC,QAAQC,KAAK,CAACF;gBAChB;gBACA,OAAO;oBACL8B,MAAM;gBACR;YACF;YAEA,MAAM2F,YAAa,MAAM1F,aAAa2F,YAAY,CAAC5B,OAAO,CACxD1B;YAEF,MAAMuD,gBACJF,SAAS,CACP,yFAAyF;YACzFjF,SACD;YAEH,MAAMoF,YAAY,MAAM1C,kDAAoB,CAACT,GAAG,CAAC1H,cAAc,IAC7D4K,cAAcE,KAAK,CAAC,MAAM5D;YAG5B,4DAA4D;YAC5D,IAAItB,eAAe;gBACjB,MAAM9F,sBAAsBtB,KAAK;oBAC/BuB;oBACAC;gBACF;gBAEA+G,eAAe,MAAMd,0BAA0B1H,KAAK6G,KAAKpF,cAAc;oBACrE+G,cAAc9G,QAAQ8K,OAAO,CAACF;oBAC9B,iIAAiI;oBACjI7D,YAAY,CAACjH,UAAUkH,kBAAkB,IAAIK;oBAC7CtB;gBACF;YACF;QACF;QAEA,OAAO;YACLjB,MAAM;YACN+B,QAAQC;YACRK;QACF;IACF,EAAE,OAAOnE,KAAK;QACZ,IAAI+H,IAAAA,8BAAe,EAAC/H,MAAM;YACxB,MAAMM,cAAc0H,IAAAA,iCAAuB,EAAChI;YAC5C,MAAMW,eAAesH,IAAAA,kCAAwB,EAACjI;YAE9C,MAAMnD,sBAAsBtB,KAAK;gBAC/BuB;gBACAC;YACF;YAEA,mFAAmF;YACnF,2FAA2F;YAC3FxB,IAAImI,UAAU,GAAGwE,sCAAkB,CAACC,QAAQ;YAE5C,IAAIxF,eAAe;gBACjB,OAAO;oBACLb,MAAM;oBACN+B,QAAQ,MAAMpD,2BACZnF,KACAC,KACA0C,MACAqC,aACAK,cACAwB,IAAII,UAAU,CAACpE,QAAQ,EACvBrB;gBAEJ;YACF;YAEAvB,IAAIsC,SAAS,CAAC,YAAYyC;YAC1B,OAAO;gBACLwB,MAAM;gBACN+B,QAAQ1D,qBAAY,CAACC,UAAU,CAAC;YAClC;QACF,OAAO,IAAIgI,IAAAA,6CAAyB,EAACpI,MAAM;YACzCzE,IAAImI,UAAU,GAAG2E,IAAAA,+CAA2B,EAACrI;YAE7C,MAAMnD,sBAAsBtB,KAAK;gBAC/BuB;gBACAC;YACF;YAEA,IAAI4F,eAAe;gBACjB,MAAMgB,UAAU3G,QAAQ4G,MAAM,CAAC5D;gBAC/B,IAAI;oBACF,8DAA8D;oBAC9D,mDAAmD;oBACnD,yDAAyD;oBACzD,2CAA2C;oBAC3C,MAAM2D;gBACR,EAAE,OAAM;gBACN,qDAAqD;gBACvD;gBACA,OAAO;oBACL7B,MAAM;oBACN+B,QAAQ,MAAMb,0BAA0B1H,KAAK6G,KAAKpF,cAAc;wBAC9DgH,YAAY;wBACZD,cAAcH;wBACdZ;oBACF;gBACF;YACF;YACA,OAAO;gBACLjB,MAAM;YACR;QACF;QAEA,IAAIa,eAAe;gBAGf7F;YAFFvB,IAAImI,UAAU,GAAG;YACjB,MAAM1G,QAAQC,GAAG,CAAC;iBAChBH,+BAAAA,UAAUI,gBAAgB,qBAA1BJ,6BAA4BK,aAAa,CACvCL,UAAUM,eAAe,IAAI,EAAE;mBAE9BrC,OAAOsC,MAAM,CAACP,UAAUQ,kBAAkB,IAAI,CAAC;mBAC9CR,UAAUS,uBAAuB,IAAI,EAAE;aAC5C;YACD,MAAMoG,UAAU3G,QAAQ4G,MAAM,CAAC5D;YAC/B,IAAI;gBACF,8DAA8D;gBAC9D,mDAAmD;gBACnD,yDAAyD;gBACzD,2CAA2C;gBAC3C,MAAM2D;YACR,EAAE,OAAM;YACN,qDAAqD;YACvD;YAEA5G,aAAaoG,KAAK,GAAG;YACrB,OAAO;gBACLrB,MAAM;gBACN+B,QAAQ,MAAM5B,eAAe3G,KAAK6G,KAAKpF,cAAc;oBACnD+G,cAAcH;oBACd,iIAAiI;oBACjII,YAAY,CAACjH,UAAUkH,kBAAkB,IAAIK;oBAC7CtB;gBACF;YACF;QACF;QAEA,MAAM/C;IACR;AACF;AAEA;;;;CAIC,GACD,SAASmF,sBACP3C,QAAuB,EACvBR,eAAgC;IAEhC,IAAI;YAMkBA;QALpB,4EAA4E;QAC5E,IAAI,CAACQ,UAAU;YACb,MAAM,qBAAqD,CAArD,IAAIpE,MAAM,6CAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAoD;QAC5D;QAEA,MAAMgG,cAAcpC,oCAAAA,4BAAAA,eAAiB,CAACQ,SAAS,qBAA3BR,0BAA6BsG,EAAE;QAEnD,IAAI,CAAClE,aAAa;YAChB,MAAM,qBAEL,CAFK,IAAIhG,MACR,+DADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,OAAOgG;IACT,EAAE,OAAOpE,KAAK;QACZ,MAAM,qBAIL,CAJK,IAAI5B,MACR,CAAC,8BAA8B,EAAEoE,SAAS,4DAA4D,EACpGxC,eAAe5B,QAAQ,CAAC,gBAAgB,EAAE4B,IAAIuI,OAAO,EAAE,GAAG,GAC3D,0EAA0E,CAAC,GAHxE,qBAAA;mBAAA;wBAAA;0BAAA;QAIN;IACF;AACF"}