{"version": 3, "sources": ["../../src/client/app-next-dev.ts"], "sourcesContent": ["// TODO-APP: hydration warning\n\nimport './app-webpack'\nimport { appBootstrap } from './app-bootstrap'\nimport { initializeDevBuildIndicatorForAppRouter } from './dev/dev-build-indicator/initialize-for-app-router'\n\nappBootstrap(() => {\n  const { hydrate } = require('./app-index')\n  hydrate()\n  initializeDevBuildIndicatorForAppRouter()\n})\n"], "names": ["appBootstrap", "initializeDevBuildIndicatorForAppRouter", "hydrate", "require"], "mappings": "AAAA,8BAA8B;AAE9B,OAAO,gBAAe;AACtB,SAASA,YAAY,QAAQ,kBAAiB;AAC9C,SAASC,uCAAuC,QAAQ,sDAAqD;AAE7GD,aAAa;IACX,MAAM,EAAEE,OAAO,EAAE,GAAGC,QAAQ;IAC5BD;IACAD;AACF"}