{"version": 3, "sources": ["../../../src/export/helpers/create-incremental-cache.ts"], "sourcesContent": ["import path from 'path'\nimport { IncrementalCache } from '../../server/lib/incremental-cache'\nimport { hasNextSupport } from '../../server/ci-info'\nimport { nodeFs } from '../../server/lib/node-fs-methods'\nimport { interopDefault } from '../../lib/interop-default'\nimport { formatDynamicImportPath } from '../../lib/format-dynamic-import-path'\nimport {\n  initializeCacheHandlers,\n  setCacheHandler,\n} from '../../server/use-cache/handlers'\n\nexport async function createIncrementalCache({\n  cacheHandler,\n  cacheMaxMemorySize,\n  fetchCacheKeyPrefix,\n  distDir,\n  dir,\n  flushToDisk,\n  cacheHandlers,\n  requestHeaders,\n}: {\n  cacheHandler?: string\n  cacheMaxMemorySize?: number\n  fetchCacheKeyPrefix?: string\n  distDir: string\n  dir: string\n  flushToDisk?: boolean\n  requestHeaders?: Record<string, string | string[] | undefined>\n  cacheHandlers?: Record<string, string | undefined>\n}) {\n  // Custom cache handler overrides.\n  let CacheHandler: any\n  if (cacheHandler) {\n    CacheHandler = interopDefault(\n      await import(formatDynamicImportPath(dir, cacheHandler)).then(\n        (mod) => mod.default || mod\n      )\n    )\n  }\n\n  if (cacheHandlers && initializeCacheHandlers()) {\n    for (const [kind, handler] of Object.entries(cacheHandlers)) {\n      if (!handler) continue\n\n      setCacheHandler(\n        kind,\n        interopDefault(\n          await import(formatDynamicImportPath(dir, handler)).then(\n            (mod) => mod.default || mod\n          )\n        )\n      )\n    }\n  }\n\n  const incrementalCache = new IncrementalCache({\n    dev: false,\n    requestHeaders: requestHeaders || {},\n    flushToDisk,\n    maxMemoryCacheSize: cacheMaxMemorySize,\n    fetchCacheKeyPrefix,\n    getPrerenderManifest: () => ({\n      version: 4,\n      routes: {},\n      dynamicRoutes: {},\n      preview: {\n        previewModeEncryptionKey: '',\n        previewModeId: '',\n        previewModeSigningKey: '',\n      },\n      notFoundRoutes: [],\n    }),\n    fs: nodeFs,\n    serverDistDir: path.join(distDir, 'server'),\n    CurCacheHandler: CacheHandler,\n    minimalMode: hasNextSupport,\n  })\n\n  ;(globalThis as any).__incrementalCache = incrementalCache\n\n  return incrementalCache\n}\n"], "names": ["path", "IncrementalCache", "hasNextSupport", "nodeFs", "interopDefault", "formatDynamicImportPath", "initializeCacheHandlers", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "createIncrementalCache", "cache<PERSON><PERSON><PERSON>", "cacheMaxMemorySize", "fetchCacheKeyPrefix", "distDir", "dir", "flushToDisk", "cacheHandlers", "requestHeaders", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "then", "mod", "default", "kind", "handler", "Object", "entries", "incrementalCache", "dev", "maxMemoryCacheSize", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "preview", "previewModeEncryptionKey", "previewModeId", "previewModeSigningKey", "notFoundRoutes", "fs", "serverDistDir", "join", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "minimalMode", "globalThis", "__incrementalCache"], "mappings": "AAAA,OAAOA,UAAU,OAAM;AACvB,SAASC,gBAAgB,QAAQ,qCAAoC;AACrE,SAASC,cAAc,QAAQ,uBAAsB;AACrD,SAASC,MAAM,QAAQ,mCAAkC;AACzD,SAASC,cAAc,QAAQ,4BAA2B;AAC1D,SAASC,uBAAuB,QAAQ,uCAAsC;AAC9E,SACEC,uBAAuB,EACvBC,eAAe,QACV,kCAAiC;AAExC,OAAO,eAAeC,uBAAuB,EAC3CC,YAAY,EACZC,kBAAkB,EAClBC,mBAAmB,EACnBC,OAAO,EACPC,GAAG,EACHC,WAAW,EACXC,aAAa,EACbC,cAAc,EAUf;IACC,kCAAkC;IAClC,IAAIC;IACJ,IAAIR,cAAc;QAChBQ,eAAeb,eACb,MAAM,MAAM,CAACC,wBAAwBQ,KAAKJ,eAAeS,IAAI,CAC3D,CAACC,MAAQA,IAAIC,OAAO,IAAID;IAG9B;IAEA,IAAIJ,iBAAiBT,2BAA2B;QAC9C,KAAK,MAAM,CAACe,MAAMC,QAAQ,IAAIC,OAAOC,OAAO,CAACT,eAAgB;YAC3D,IAAI,CAACO,SAAS;YAEdf,gBACEc,MACAjB,eACE,MAAM,MAAM,CAACC,wBAAwBQ,KAAKS,UAAUJ,IAAI,CACtD,CAACC,MAAQA,IAAIC,OAAO,IAAID;QAIhC;IACF;IAEA,MAAMM,mBAAmB,IAAIxB,iBAAiB;QAC5CyB,KAAK;QACLV,gBAAgBA,kBAAkB,CAAC;QACnCF;QACAa,oBAAoBjB;QACpBC;QACAiB,sBAAsB,IAAO,CAAA;gBAC3BC,SAAS;gBACTC,QAAQ,CAAC;gBACTC,eAAe,CAAC;gBAChBC,SAAS;oBACPC,0BAA0B;oBAC1BC,eAAe;oBACfC,uBAAuB;gBACzB;gBACAC,gBAAgB,EAAE;YACpB,CAAA;QACAC,IAAIlC;QACJmC,eAAetC,KAAKuC,IAAI,CAAC3B,SAAS;QAClC4B,iBAAiBvB;QACjBwB,aAAavC;IACf;IAEEwC,WAAmBC,kBAAkB,GAAGlB;IAE1C,OAAOA;AACT"}