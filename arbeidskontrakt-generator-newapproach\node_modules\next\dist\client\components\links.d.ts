import type { FlightRouterState } from '../../server/app-render/types';
import type { AppRouterInstance } from '../../shared/lib/app-router-context.shared-runtime';
import { PrefetchKind } from './router-reducer/router-reducer-types';
type LinkElement = HTMLAnchorElement | SVGAElement | HTMLFormElement;
export declare function mountLinkInstance(element: LinkElement, href: string, router: AppRouterInstance, kind: PrefetchKind.AUTO | PrefetchKind.FULL): void;
export declare function unmountLinkInstance(element: LinkElement): void;
export declare function onLinkVisibilityChanged(element: LinkElement, isVisible: boolean): void;
export declare function onNavigationIntent(element: HTMLAnchorElement | SVGAElement): void;
export declare function pingVisibleLinks(nextUrl: string | null, tree: FlightRouterState): void;
export {};
