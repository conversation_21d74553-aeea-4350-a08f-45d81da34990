{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/code-frame/code-frame.tsx"], "sourcesContent": ["import type { StackFrame } from 'next/dist/compiled/stacktrace-parser'\n\nimport Anser from 'next/dist/compiled/anser'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\n\nimport { useMemo } from 'react'\nimport { HotlinkedText } from '../hot-linked-text'\nimport { getFrameSource } from '../../../utils/stack-frame'\nimport { useOpenInEditor } from '../../utils/use-open-in-editor'\nimport { ExternalIcon } from '../../icons/external'\nimport { FileIcon } from '../../icons/file'\n\nexport type CodeFrameProps = { stackFrame: StackFrame; codeFrame: string }\n\nexport function CodeFrame({ stackFrame, codeFrame }: CodeFrameProps) {\n  // Strip leading spaces out of the code frame:\n  const formattedFrame = useMemo<string>(() => {\n    const lines = codeFrame.split(/\\r?\\n/g)\n\n    // Find the minimum length of leading spaces after `|` in the code frame\n    const miniLeadingSpacesLength = lines\n      .map((line) =>\n        /^>? +\\d+ +\\| [ ]+/.exec(stripAnsi(line)) === null\n          ? null\n          : /^>? +\\d+ +\\| ( *)/.exec(stripAnsi(line))\n      )\n      .filter(Boolean)\n      .map((v) => v!.pop()!)\n      .reduce((c, n) => (isNaN(c) ? n.length : Math.min(c, n.length)), NaN)\n\n    // When the minimum length of leading spaces is greater than 1, remove them\n    // from the code frame to help the indentation looks better when there's a lot leading spaces.\n    if (miniLeadingSpacesLength > 1) {\n      return lines\n        .map((line, a) =>\n          ~(a = line.indexOf('|'))\n            ? line.substring(0, a) +\n              line.substring(a).replace(`^\\\\ {${miniLeadingSpacesLength}}`, '')\n            : line\n        )\n        .join('\\n')\n    }\n    return lines.join('\\n')\n  }, [codeFrame])\n\n  const decoded = useMemo(() => {\n    return Anser.ansiToJson(formattedFrame, {\n      json: true,\n      use_classes: true,\n      remove_empty: true,\n    })\n  }, [formattedFrame])\n\n  const open = useOpenInEditor({\n    file: stackFrame.file,\n    lineNumber: stackFrame.lineNumber,\n    column: stackFrame.column,\n  })\n\n  const fileExtension = stackFrame?.file?.split('.').pop()\n\n  // TODO: make the caret absolute\n  return (\n    <div data-nextjs-codeframe>\n      <div className=\"code-frame-header\">\n        {/* TODO: This is <div> in `Terminal` component.\n        Changing now will require multiple test snapshots updates.\n        Leaving as <div> as is trivial and does not affect the UI.\n        Change when the new redbox matcher `toDisplayRedbox` is used.\n        */}\n        <p className=\"code-frame-link\">\n          <span className=\"code-frame-icon\">\n            <FileIcon lang={fileExtension} />\n          </span>\n          <span data-text>\n            {getFrameSource(stackFrame)} @{' '}\n            <HotlinkedText text={stackFrame.methodName} />\n          </span>\n          <button\n            aria-label=\"Open in editor\"\n            data-with-open-in-editor-link-source-file\n            onClick={open}\n          >\n            <span className=\"code-frame-icon\" data-icon=\"right\">\n              <ExternalIcon width={16} height={16} />\n            </span>\n          </button>\n        </p>\n      </div>\n      <pre className=\"code-frame-pre\">\n        {decoded.map((entry, index) => (\n          <span\n            key={`frame-${index}`}\n            style={{\n              color: entry.fg ? `var(--color-${entry.fg})` : undefined,\n              ...(entry.decoration === 'bold'\n                ? // TODO(jiwon): This used to be 800, but the symbols like `─┬─` are\n                  // having longer width than expected on Geist Mono font-weight\n                  // above 600, hence a temporary fix is to use 500 for bold.\n                  { fontWeight: 500 }\n                : entry.decoration === 'italic'\n                  ? { fontStyle: 'italic' }\n                  : undefined),\n            }}\n          >\n            {entry.content}\n          </span>\n        ))}\n      </pre>\n    </div>\n  )\n}\n\nexport const CODE_FRAME_STYLES = `\n  [data-nextjs-codeframe] {\n    background-color: var(--color-background-200);\n    overflow: hidden;\n    color: var(--color-gray-1000);\n    text-overflow: ellipsis;\n    border: 1px solid var(--color-gray-400);\n    border-radius: 8px;\n    font-family: var(--font-stack-monospace);\n    font-size: var(--size-12);\n    line-height: var(--size-16);\n    margin: 8px 0;\n\n    svg {\n      width: var(--size-16);\n      height: var(--size-16);\n    }\n  }\n\n  .code-frame-link,\n  .code-frame-pre {\n    padding: 12px;\n  }\n\n  .code-frame-link svg {\n    flex-shrink: 0;\n  }\n\n  .code-frame-link [data-text] {\n    display: inline-flex;\n    text-align: left;\n    margin: auto 6px;\n  }\n\n  .code-frame-pre {\n    white-space: pre-wrap;\n  }\n\n  .code-frame-header {\n    width: 100%;\n    transition: background 100ms ease-out;\n    border-radius: 8px 8px 0 0;\n    border-bottom: 1px solid var(--color-gray-400);\n  }\n\n  [data-with-open-in-editor-link-source-file] {\n    padding: 4px;\n    margin: -4px 0 -4px auto;\n    border-radius: var(--rounded-full);\n    margin-left: auto;\n\n    &:focus-visible {\n      outline: var(--focus-ring);\n      outline-offset: -2px;\n    }\n\n    &:hover {\n      background: var(--color-gray-100);\n    }\n  }\n\n  [data-nextjs-codeframe]::selection,\n  [data-nextjs-codeframe] *::selection {\n    background-color: var(--color-ansi-selection);\n  }\n\n  [data-nextjs-codeframe] *:not(a) {\n    color: inherit;\n    background-color: transparent;\n    font-family: var(--font-stack-monospace);\n  }\n\n  [data-nextjs-codeframe] > * {\n    margin: 0;\n  }\n\n  .code-frame-link {\n    display: flex;\n    margin: 0;\n    outline: 0;\n  }\n  .code-frame-link [data-icon='right'] {\n    margin-left: auto;\n  }\n\n  [data-nextjs-codeframe] div > pre {\n    overflow: hidden;\n    display: inline-block;\n  }\n\n  [data-nextjs-codeframe] svg {\n    color: var(--color-gray-900);\n  }\n`\n"], "names": ["<PERSON><PERSON>", "stripAnsi", "useMemo", "HotlinkedText", "getFrameSource", "useOpenInEditor", "ExternalIcon", "FileIcon", "CodeFrame", "stackFrame", "codeFrame", "formattedFrame", "lines", "split", "miniLeadingSpacesLength", "map", "line", "exec", "filter", "Boolean", "v", "pop", "reduce", "c", "n", "isNaN", "length", "Math", "min", "NaN", "a", "indexOf", "substring", "replace", "join", "decoded", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "json", "use_classes", "remove_empty", "open", "file", "lineNumber", "column", "fileExtension", "div", "data-nextjs-codeframe", "className", "p", "span", "lang", "data-text", "text", "methodName", "button", "aria-label", "data-with-open-in-editor-link-source-file", "onClick", "data-icon", "width", "height", "pre", "entry", "index", "style", "color", "fg", "undefined", "decoration", "fontWeight", "fontStyle", "content", "CODE_FRAME_STYLES"], "mappings": ";AAEA,OAAOA,WAAW,2BAA0B;AAC5C,OAAOC,eAAe,gCAA+B;AAErD,SAASC,OAAO,QAAQ,QAAO;AAC/B,SAASC,aAAa,QAAQ,qBAAoB;AAClD,SAASC,cAAc,QAAQ,6BAA4B;AAC3D,SAASC,eAAe,QAAQ,iCAAgC;AAChE,SAASC,YAAY,QAAQ,uBAAsB;AACnD,SAASC,QAAQ,QAAQ,mBAAkB;AAI3C,OAAO,SAASC,UAAU,KAAyC;IAAzC,IAAA,EAAEC,UAAU,EAAEC,SAAS,EAAkB,GAAzC;QA6CFD;IA5CtB,8CAA8C;IAC9C,MAAME,iBAAiBT,QAAgB;QACrC,MAAMU,QAAQF,UAAUG,KAAK,CAAC;QAE9B,wEAAwE;QACxE,MAAMC,0BAA0BF,MAC7BG,GAAG,CAAC,CAACC,OACJ,oBAAoBC,IAAI,CAAChB,UAAUe,WAAW,OAC1C,OACA,oBAAoBC,IAAI,CAAChB,UAAUe,QAExCE,MAAM,CAACC,SACPJ,GAAG,CAAC,CAACK,IAAMA,EAAGC,GAAG,IACjBC,MAAM,CAAC,CAACC,GAAGC,IAAOC,MAAMF,KAAKC,EAAEE,MAAM,GAAGC,KAAKC,GAAG,CAACL,GAAGC,EAAEE,MAAM,GAAIG;QAEnE,2EAA2E;QAC3E,8FAA8F;QAC9F,IAAIf,0BAA0B,GAAG;YAC/B,OAAOF,MACJG,GAAG,CAAC,CAACC,MAAMc,IACV,CAAEA,CAAAA,IAAId,KAAKe,OAAO,CAAC,IAAG,IAClBf,KAAKgB,SAAS,CAAC,GAAGF,KAClBd,KAAKgB,SAAS,CAACF,GAAGG,OAAO,CAAC,AAAC,UAAOnB,0BAAwB,KAAI,MAC9DE,MAELkB,IAAI,CAAC;QACV;QACA,OAAOtB,MAAMsB,IAAI,CAAC;IACpB,GAAG;QAACxB;KAAU;IAEd,MAAMyB,UAAUjC,QAAQ;QACtB,OAAOF,MAAMoC,UAAU,CAACzB,gBAAgB;YACtC0B,MAAM;YACNC,aAAa;YACbC,cAAc;QAChB;IACF,GAAG;QAAC5B;KAAe;IAEnB,MAAM6B,OAAOnC,gBAAgB;QAC3BoC,MAAMhC,WAAWgC,IAAI;QACrBC,YAAYjC,WAAWiC,UAAU;QACjCC,QAAQlC,WAAWkC,MAAM;IAC3B;IAEA,MAAMC,gBAAgBnC,+BAAAA,mBAAAA,WAAYgC,IAAI,qBAAhBhC,iBAAkBI,KAAK,CAAC,KAAKQ,GAAG;IAEtD,gCAAgC;IAChC,qBACE,MAACwB;QAAIC,uBAAqB;;0BACxB,KAACD;gBAAIE,WAAU;0BAMb,cAAA,MAACC;oBAAED,WAAU;;sCACX,KAACE;4BAAKF,WAAU;sCACd,cAAA,KAACxC;gCAAS2C,MAAMN;;;sCAElB,MAACK;4BAAKE,WAAS;;gCACZ/C,eAAeK;gCAAY;gCAAG;8CAC/B,KAACN;oCAAciD,MAAM3C,WAAW4C,UAAU;;;;sCAE5C,KAACC;4BACCC,cAAW;4BACXC,2CAAyC;4BACzCC,SAASjB;sCAET,cAAA,KAACS;gCAAKF,WAAU;gCAAkBW,aAAU;0CAC1C,cAAA,KAACpD;oCAAaqD,OAAO;oCAAIC,QAAQ;;;;;;;0BAKzC,KAACC;gBAAId,WAAU;0BACZZ,QAAQpB,GAAG,CAAC,CAAC+C,OAAOC,sBACnB,KAACd;wBAECe,OAAO;4BACLC,OAAOH,MAAMI,EAAE,GAAG,AAAC,iBAAcJ,MAAMI,EAAE,GAAC,MAAKC;4BAC/C,GAAIL,MAAMM,UAAU,KAAK,SAErB,8DAA8D;4BAC9D,2DAA2D;4BAC3D;gCAAEC,YAAY;4BAAI,IAClBP,MAAMM,UAAU,KAAK,WACnB;gCAAEE,WAAW;4BAAS,IACtBH,SAAS;wBACjB;kCAECL,MAAMS,OAAO;uBAbT,AAAC,WAAQR;;;;AAmB1B;AAEA,OAAO,MAAMS,oBAAqB,o5DA6FjC"}