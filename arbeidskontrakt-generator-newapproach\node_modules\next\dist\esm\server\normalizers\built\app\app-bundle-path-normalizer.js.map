{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-bundle-path-normalizer.ts"], "sourcesContent": ["import { Normalizers } from '../../normalizers'\nimport type { Normalizer } from '../../normalizer'\nimport { PrefixingNormalizer } from '../../prefixing-normalizer'\nimport { normalizePagePath } from '../../../../shared/lib/page-path/normalize-page-path'\n\nexport class AppBundlePathNormalizer extends PrefixingNormalizer {\n  constructor() {\n    super('app')\n  }\n\n  public normalize(page: string): string {\n    return super.normalize(normalizePagePath(page))\n  }\n}\n\nexport class DevAppBundlePathNormalizer extends Normalizers {\n  constructor(pageNormalizer: Normalizer) {\n    super([\n      // This should normalize the filename to a page.\n      pageNormalizer,\n      // Normalize the app page to a pathname.\n      new AppBundlePathNormalizer(),\n    ])\n  }\n\n  public normalize(filename: string): string {\n    return super.normalize(filename)\n  }\n}\n"], "names": ["Normalizers", "PrefixingNormalizer", "normalizePagePath", "AppBundlePathNormalizer", "constructor", "normalize", "page", "DevAppBundlePathNormalizer", "pageNormalizer", "filename"], "mappings": "AAAA,SAASA,WAAW,QAAQ,oBAAmB;AAE/C,SAASC,mBAAmB,QAAQ,6BAA4B;AAChE,SAASC,iBAAiB,QAAQ,uDAAsD;AAExF,OAAO,MAAMC,gCAAgCF;IAC3CG,aAAc;QACZ,KAAK,CAAC;IACR;IAEOC,UAAUC,IAAY,EAAU;QACrC,OAAO,KAAK,CAACD,UAAUH,kBAAkBI;IAC3C;AACF;AAEA,OAAO,MAAMC,mCAAmCP;IAC9CI,YAAYI,cAA0B,CAAE;QACtC,KAAK,CAAC;YACJ,gDAAgD;YAChDA;YACA,wCAAwC;YACxC,IAAIL;SACL;IACH;IAEOE,UAAUI,QAAgB,EAAU;QACzC,OAAO,KAAK,CAACJ,UAAUI;IACzB;AACF"}