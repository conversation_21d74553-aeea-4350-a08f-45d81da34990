{"version": 3, "sources": ["../../../src/server/app-render/create-flight-router-state-from-loader-tree.ts"], "sourcesContent": ["import type { LoaderTree } from '../lib/app-dir-module'\nimport type { FlightRouterState } from './types'\nimport type { GetDynamicParamFromSegment } from './app-render'\nimport { addSearchParamsIfPageSegment } from '../../shared/lib/segment'\n\nexport function createFlightRouterStateFromLoaderTree(\n  [segment, parallelRoutes, { layout }]: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment,\n  searchParams: any,\n  rootLayoutIncluded = false\n): FlightRouterState {\n  const dynamicParam = getDynamicParamFromSegment(segment)\n  const treeSegment = dynamicParam ? dynamicParam.treeSegment : segment\n\n  const segmentTree: FlightRouterState = [\n    addSearchParamsIfPageSegment(treeSegment, searchParams),\n    {},\n  ]\n\n  if (!rootLayoutIncluded && typeof layout !== 'undefined') {\n    rootLayoutIncluded = true\n    segmentTree[4] = true\n  }\n\n  segmentTree[1] = Object.keys(parallelRoutes).reduce(\n    (existingValue, currentValue) => {\n      existingValue[currentValue] = createFlightRouterStateFromLoaderTree(\n        parallelRoutes[currentValue],\n        getDynamicParamFromSegment,\n        searchParams,\n        rootLayoutIncluded\n      )\n      return existingValue\n    },\n    {} as FlightRouterState[1]\n  )\n\n  return segmentTree\n}\n"], "names": ["createFlightRouterStateFromLoaderTree", "segment", "parallelRoutes", "layout", "getDynamicParamFromSegment", "searchParams", "rootLayoutIncluded", "dynamicParam", "treeSegment", "segmentTree", "addSearchParamsIfPageSegment", "Object", "keys", "reduce", "existingValue", "currentValue"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;yBAF6B;AAEtC,SAASA,sCACd,CAACC,SAASC,gBAAgB,EAAEC,MAAM,EAAE,CAAa,EACjDC,0BAAsD,EACtDC,YAAiB,EACjBC,qBAAqB,KAAK;IAE1B,MAAMC,eAAeH,2BAA2BH;IAChD,MAAMO,cAAcD,eAAeA,aAAaC,WAAW,GAAGP;IAE9D,MAAMQ,cAAiC;QACrCC,IAAAA,qCAA4B,EAACF,aAAaH;QAC1C,CAAC;KACF;IAED,IAAI,CAACC,sBAAsB,OAAOH,WAAW,aAAa;QACxDG,qBAAqB;QACrBG,WAAW,CAAC,EAAE,GAAG;IACnB;IAEAA,WAAW,CAAC,EAAE,GAAGE,OAAOC,IAAI,CAACV,gBAAgBW,MAAM,CACjD,CAACC,eAAeC;QACdD,aAAa,CAACC,aAAa,GAAGf,sCAC5BE,cAAc,CAACa,aAAa,EAC5BX,4BACAC,cACAC;QAEF,OAAOQ;IACT,GACA,CAAC;IAGH,OAAOL;AACT"}