export interface FormData {
  // Basic Info
  employeeName: string
  employeeAddress: string
  employeeBirthDate: string
  startDate: string
  position: string
  hourlyRate: number
  accountNumber: string
  employmentType: "fast" | "midlertidig"
  isTemporary: boolean
  temporaryEndDate: string
  temporaryReason: string
  probationPeriod: boolean
  probationMonths: number
  ownTools: boolean

  // Advanced Settings
  companyName: string
  companyOrgNumber: string
  companyAddress: string
  workingHoursPerWeek: number
  workingTime: string
  breakTime: string
  overtimeRate: number
  paymentDay: number
  toolAllowance: string
  travelAllowance: string
  pensionProvider: string
  pensionOrgNumber: string
  insuranceProvider: string
  insuranceOrgNumber: string
  noticePeriod: string
  contractDuration: string
  notificationRules: string
}
