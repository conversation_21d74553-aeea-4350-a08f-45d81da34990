{"version": 3, "sources": ["../../../../../src/server/normalizers/built/app/app-page-normalizer.ts"], "sourcesContent": ["import { PAGE_TYPES } from '../../../../lib/page-types'\nimport { AbsoluteFilenameNormalizer } from '../../absolute-filename-normalizer'\n\n/**\n * DevAppPageNormalizer is a normalizer that is used to normalize a pathname\n * to a page in the `app` directory.\n */\nexport class DevAppPageNormalizer extends AbsoluteFilenameNormalizer {\n  constructor(appDir: string, extensions: ReadonlyArray<string>) {\n    super(appDir, extensions, PAGE_TYPES.APP)\n  }\n}\n"], "names": ["PAGE_TYPES", "AbsoluteFilenameNormalizer", "DevAppPageNormalizer", "constructor", "appDir", "extensions", "APP"], "mappings": "AAAA,SAASA,UAAU,QAAQ,6BAA4B;AACvD,SAASC,0BAA0B,QAAQ,qCAAoC;AAE/E;;;CAGC,GACD,OAAO,MAAMC,6BAA6BD;IACxCE,YAAYC,MAAc,EAAEC,UAAiC,CAAE;QAC7D,KAAK,CAACD,QAAQC,YAAYL,WAAWM,GAAG;IAC1C;AACF"}