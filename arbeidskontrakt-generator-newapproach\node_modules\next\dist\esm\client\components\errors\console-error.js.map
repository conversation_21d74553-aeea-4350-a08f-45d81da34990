{"version": 3, "sources": ["../../../../src/client/components/errors/console-error.ts"], "sourcesContent": ["// To distinguish from React error.digest, we use a different symbol here to determine if the error is from console.error or unhandled promise rejection.\nconst digestSym = Symbol.for('next.console.error.digest')\nconst consoleTypeSym = Symbol.for('next.console.error.type')\n\n// Represent non Error shape unhandled promise rejections or console.error errors.\n// Those errors will be captured and displayed in Error Overlay.\ntype UnhandledError = Error & {\n  [digestSym]: 'NEXT_UNHANDLED_ERROR'\n  [consoleTypeSym]: 'string' | 'error'\n  environmentName: string\n}\n\nexport function createUnhandledError(\n  message: string | Error,\n  environmentName?: string | null\n): UnhandledError {\n  const error = (\n    typeof message === 'string' ? new Error(message) : message\n  ) as UnhandledError\n  error[digestSym] = 'NEXT_UNHANDLED_ERROR'\n  error[consoleTypeSym] = typeof message === 'string' ? 'string' : 'error'\n\n  if (environmentName && !error.environmentName) {\n    error.environmentName = environmentName\n  }\n\n  return error\n}\n\nexport const isUnhandledConsoleOrRejection = (\n  error: any\n): error is UnhandledError => {\n  return error && error[digestSym] === 'NEXT_UNHANDLED_ERROR'\n}\n\nexport const getUnhandledErrorType = (error: UnhandledError) => {\n  return error[consoleTypeSym]\n}\n"], "names": ["digestSym", "Symbol", "for", "consoleTypeSym", "createUnhandledError", "message", "environmentName", "error", "Error", "isUnhandledConsoleOrRejection", "getUnhandledErrorType"], "mappings": "AAAA,yJAAyJ;AACzJ,MAAMA,YAAYC,OAAOC,GAAG,CAAC;AAC7B,MAAMC,iBAAiBF,OAAOC,GAAG,CAAC;AAUlC,OAAO,SAASE,qBACdC,OAAuB,EACvBC,eAA+B;IAE/B,MAAMC,QACJ,OAAOF,YAAY,WAAW,qBAAkB,CAAlB,IAAIG,MAAMH,UAAV,qBAAA;eAAA;oBAAA;sBAAA;IAAiB,KAAIA;IAErDE,KAAK,CAACP,UAAU,GAAG;IACnBO,KAAK,CAACJ,eAAe,GAAG,OAAOE,YAAY,WAAW,WAAW;IAEjE,IAAIC,mBAAmB,CAACC,MAAMD,eAAe,EAAE;QAC7CC,MAAMD,eAAe,GAAGA;IAC1B;IAEA,OAAOC;AACT;AAEA,OAAO,MAAME,gCAAgC,CAC3CF;IAEA,OAAOA,SAASA,KAAK,CAACP,UAAU,KAAK;AACvC,EAAC;AAED,OAAO,MAAMU,wBAAwB,CAACH;IACpC,OAAOA,KAAK,CAACJ,eAAe;AAC9B,EAAC"}