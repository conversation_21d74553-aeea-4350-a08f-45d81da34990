{"version": 3, "sources": ["../../../../src/experimental/testing/server/config-testing-utils.ts"], "sourcesContent": ["import type { IncomingHttpHeaders } from 'node:http'\nimport { parse, type UrlWithParsedQuery } from 'node:url'\nimport { match } from 'next/dist/compiled/path-to-regexp'\nimport {\n  matchHas,\n  prepareDestination,\n} from '../../../shared/lib/router/utils/prepare-destination'\nimport { buildCustomRoute } from '../../../lib/build-custom-route'\nimport loadCustomRoutes from '../../../lib/load-custom-routes'\nimport type { NextConfig } from '../../../server/config-shared'\nimport { NextResponse } from '../../../server/web/exports'\nimport { getRedirectStatus } from '../../../lib/redirect-status'\nimport type {\n  ManifestHeaderRoute,\n  ManifestRedirectRoute,\n  ManifestRewriteRoute,\n} from '../../../build'\nimport type { BaseNextRequest } from '../../../server/base-http'\nimport type { Params } from '../../../server/request/params'\nimport { constructRequest } from './utils'\nimport { parsedUrlQueryToParams } from '../../../server/route-modules/app-route/helpers/parsed-url-query-to-params'\n\n/**\n * Tries to match the current request against the provided route. If there is\n * a match, it returns the params extracted from the path. If not, it returns\n * undefined.\n */\nfunction matchRoute(\n  route: ManifestHeaderRoute | ManifestRedirectRoute | ManifestRewriteRoute,\n  request: BaseNextRequest,\n  parsedUrl: UrlWithParsedQuery\n): Params | undefined {\n  const pathname = parsedUrl.pathname\n  if (!pathname) {\n    return\n  }\n  const regexMatches = pathname?.match(route.regex)\n\n  if (regexMatches) {\n    const pathMatch = match<Params>(route.source)(pathname)\n    if (!pathMatch) {\n      throw new Error(\n        'Unexpected error: extracting params from path failed but the regular expression matched'\n      )\n    }\n    if (route.has || route.missing) {\n      if (!matchHas(request, parsedUrl.query, route.has, route.missing)) {\n        return\n      }\n    }\n    return pathMatch.params\n  }\n}\n\n/**\n * Tests the logic of `headers`, `redirects`, and `rewrites` in `next.config.js`.\n * Given the provided next config, this function will return a `NextResponse`\n * with the result of running the request through the custom routes.\n *\n * @example Test whether a given URL results in a redirect.\n * ```\n * import { unstable_getResponseFromNextConfig, getRedirectUrl } from 'next/server/testing'\n * const response = await unstable_getResponseFromNextConfig({\n *   url: 'https://nextjs.org/test',\n *   nextConfig: {\n *    async redirects() {\n *     return [\n *       { source: '/test', destination: '/test2', permanent: false },\n *     ]\n *    },\n *   }\n * });\n * expect(response.status).toEqual(307);\n * expect(getRedirectUrl(response)).toEqual('https://nextjs.org/test2');\n * ```\n */\nexport async function unstable_getResponseFromNextConfig({\n  url,\n  nextConfig,\n  headers = {},\n  cookies = {},\n}: {\n  url: string\n  nextConfig: NextConfig\n  headers?: IncomingHttpHeaders\n  cookies?: Record<string, string>\n}): Promise<NextResponse> {\n  const parsedUrl = parse(url, true)\n  const request = constructRequest({ url, headers, cookies })\n  const routes = await loadCustomRoutes(nextConfig)\n\n  const headerRoutes = routes.headers.map((route) =>\n    buildCustomRoute('header', route)\n  )\n  const redirectRoutes = routes.redirects.map((route) =>\n    buildCustomRoute('redirect', route, ['/_next/'])\n  )\n  const rewriteRoutes = [\n    ...routes.rewrites.beforeFiles,\n    ...routes.rewrites.afterFiles,\n    ...routes.rewrites.fallback,\n  ].map((route) => buildCustomRoute('rewrite', route))\n\n  const respHeaders: Record<string, string> = {}\n  for (const route of headerRoutes) {\n    const matched = matchRoute(route, request, parsedUrl)\n    if (matched) {\n      for (const header of route.headers) {\n        respHeaders[header.key] = header.value\n      }\n    }\n  }\n  function matchRouteAndGetDestination(\n    route: ManifestRedirectRoute | ManifestRewriteRoute\n  ): URL | undefined {\n    const params = matchRoute(route, request, parsedUrl)\n    if (!params) {\n      return\n    }\n    const { newUrl, parsedDestination } = prepareDestination({\n      appendParamsToQuery: false,\n      destination: route.destination,\n      params,\n      query: parsedUrl.query,\n    })\n    const searchParams = new URLSearchParams(\n      parsedUrlQueryToParams(parsedDestination.query) as Record<string, string>\n    )\n    return new URL(\n      searchParams.size > 0 ? `${newUrl}?${searchParams.toString()}` : newUrl,\n      parsedDestination.hostname\n        ? `${parsedDestination.protocol}//${parsedDestination.hostname}`\n        : parsedUrl.host\n          ? `${parsedUrl.protocol}//${parsedUrl.host}`\n          : 'https://example.com'\n    )\n  }\n  for (const route of redirectRoutes) {\n    const redirectUrl = matchRouteAndGetDestination(route)\n    if (!redirectUrl) {\n      continue\n    }\n    const statusCode = getRedirectStatus(route)\n    return NextResponse.redirect(redirectUrl, {\n      status: statusCode,\n      headers: respHeaders,\n    })\n  }\n  for (const route of rewriteRoutes) {\n    const rewriteUrl = matchRouteAndGetDestination(route)\n    if (!rewriteUrl) {\n      continue\n    }\n    return NextResponse.rewrite(rewriteUrl, {\n      headers: respHeaders,\n    })\n  }\n  return new NextResponse('', { status: 200, headers: respHeaders })\n}\n"], "names": ["unstable_getResponseFromNextConfig", "matchRoute", "route", "request", "parsedUrl", "pathname", "regexMatches", "match", "regex", "pathMatch", "source", "Error", "has", "missing", "matchHas", "query", "params", "url", "nextConfig", "headers", "cookies", "parse", "constructRequest", "routes", "loadCustomRoutes", "headerRoutes", "map", "buildCustomRoute", "redirectRoutes", "redirects", "rewriteRoutes", "rewrites", "beforeFiles", "afterFiles", "fallback", "respHeaders", "matched", "header", "key", "value", "matchRouteAndGetDestination", "newUrl", "parsedDestination", "prepareDestination", "appendParamsToQuery", "destination", "searchParams", "URLSearchParams", "parsedUrlQueryToParams", "URL", "size", "toString", "hostname", "protocol", "host", "redirectUrl", "statusCode", "getRedirectStatus", "NextResponse", "redirect", "status", "rewriteUrl", "rewrite"], "mappings": ";;;;+BA4EsBA;;;eAAAA;;;yBA3EyB;8BACzB;oCAIf;kCAC0B;yEACJ;yBAEA;gCACK;uBAQD;wCACM;;;;;;AAEvC;;;;CAIC,GACD,SAASC,WACPC,KAAyE,EACzEC,OAAwB,EACxBC,SAA6B;IAE7B,MAAMC,WAAWD,UAAUC,QAAQ;IACnC,IAAI,CAACA,UAAU;QACb;IACF;IACA,MAAMC,eAAeD,4BAAAA,SAAUE,KAAK,CAACL,MAAMM,KAAK;IAEhD,IAAIF,cAAc;QAChB,MAAMG,YAAYF,IAAAA,mBAAK,EAASL,MAAMQ,MAAM,EAAEL;QAC9C,IAAI,CAACI,WAAW;YACd,MAAM,qBAEL,CAFK,IAAIE,MACR,4FADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QACA,IAAIT,MAAMU,GAAG,IAAIV,MAAMW,OAAO,EAAE;YAC9B,IAAI,CAACC,IAAAA,4BAAQ,EAACX,SAASC,UAAUW,KAAK,EAAEb,MAAMU,GAAG,EAAEV,MAAMW,OAAO,GAAG;gBACjE;YACF;QACF;QACA,OAAOJ,UAAUO,MAAM;IACzB;AACF;AAwBO,eAAehB,mCAAmC,EACvDiB,GAAG,EACHC,UAAU,EACVC,UAAU,CAAC,CAAC,EACZC,UAAU,CAAC,CAAC,EAMb;IACC,MAAMhB,YAAYiB,IAAAA,cAAK,EAACJ,KAAK;IAC7B,MAAMd,UAAUmB,IAAAA,uBAAgB,EAAC;QAAEL;QAAKE;QAASC;IAAQ;IACzD,MAAMG,SAAS,MAAMC,IAAAA,yBAAgB,EAACN;IAEtC,MAAMO,eAAeF,OAAOJ,OAAO,CAACO,GAAG,CAAC,CAACxB,QACvCyB,IAAAA,kCAAgB,EAAC,UAAUzB;IAE7B,MAAM0B,iBAAiBL,OAAOM,SAAS,CAACH,GAAG,CAAC,CAACxB,QAC3CyB,IAAAA,kCAAgB,EAAC,YAAYzB,OAAO;YAAC;SAAU;IAEjD,MAAM4B,gBAAgB;WACjBP,OAAOQ,QAAQ,CAACC,WAAW;WAC3BT,OAAOQ,QAAQ,CAACE,UAAU;WAC1BV,OAAOQ,QAAQ,CAACG,QAAQ;KAC5B,CAACR,GAAG,CAAC,CAACxB,QAAUyB,IAAAA,kCAAgB,EAAC,WAAWzB;IAE7C,MAAMiC,cAAsC,CAAC;IAC7C,KAAK,MAAMjC,SAASuB,aAAc;QAChC,MAAMW,UAAUnC,WAAWC,OAAOC,SAASC;QAC3C,IAAIgC,SAAS;YACX,KAAK,MAAMC,UAAUnC,MAAMiB,OAAO,CAAE;gBAClCgB,WAAW,CAACE,OAAOC,GAAG,CAAC,GAAGD,OAAOE,KAAK;YACxC;QACF;IACF;IACA,SAASC,4BACPtC,KAAmD;QAEnD,MAAMc,SAASf,WAAWC,OAAOC,SAASC;QAC1C,IAAI,CAACY,QAAQ;YACX;QACF;QACA,MAAM,EAAEyB,MAAM,EAAEC,iBAAiB,EAAE,GAAGC,IAAAA,sCAAkB,EAAC;YACvDC,qBAAqB;YACrBC,aAAa3C,MAAM2C,WAAW;YAC9B7B;YACAD,OAAOX,UAAUW,KAAK;QACxB;QACA,MAAM+B,eAAe,IAAIC,gBACvBC,IAAAA,8CAAsB,EAACN,kBAAkB3B,KAAK;QAEhD,OAAO,IAAIkC,IACTH,aAAaI,IAAI,GAAG,IAAI,GAAGT,OAAO,CAAC,EAAEK,aAAaK,QAAQ,IAAI,GAAGV,QACjEC,kBAAkBU,QAAQ,GACtB,GAAGV,kBAAkBW,QAAQ,CAAC,EAAE,EAAEX,kBAAkBU,QAAQ,EAAE,GAC9DhD,UAAUkD,IAAI,GACZ,GAAGlD,UAAUiD,QAAQ,CAAC,EAAE,EAAEjD,UAAUkD,IAAI,EAAE,GAC1C;IAEV;IACA,KAAK,MAAMpD,SAAS0B,eAAgB;QAClC,MAAM2B,cAAcf,4BAA4BtC;QAChD,IAAI,CAACqD,aAAa;YAChB;QACF;QACA,MAAMC,aAAaC,IAAAA,iCAAiB,EAACvD;QACrC,OAAOwD,qBAAY,CAACC,QAAQ,CAACJ,aAAa;YACxCK,QAAQJ;YACRrC,SAASgB;QACX;IACF;IACA,KAAK,MAAMjC,SAAS4B,cAAe;QACjC,MAAM+B,aAAarB,4BAA4BtC;QAC/C,IAAI,CAAC2D,YAAY;YACf;QACF;QACA,OAAOH,qBAAY,CAACI,OAAO,CAACD,YAAY;YACtC1C,SAASgB;QACX;IACF;IACA,OAAO,IAAIuB,qBAAY,CAAC,IAAI;QAAEE,QAAQ;QAAKzC,SAASgB;IAAY;AAClE"}