{"version": 3, "sources": ["../../../src/shared/lib/amp-context.shared-runtime.ts"], "sourcesContent": ["import React from 'react'\n\nexport const AmpStateContext: React.Context<any> = React.createContext({})\n\nif (process.env.NODE_ENV !== 'production') {\n  AmpStateContext.displayName = 'AmpStateContext'\n}\n"], "names": ["React", "AmpStateContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA,OAAOA,WAAW,QAAO;AAEzB,OAAO,MAAMC,kBAAsCD,MAAME,aAAa,CAAC,CAAC,GAAE;AAE1E,IAAIC,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCJ,gBAAgBK,WAAW,GAAG;AAChC"}