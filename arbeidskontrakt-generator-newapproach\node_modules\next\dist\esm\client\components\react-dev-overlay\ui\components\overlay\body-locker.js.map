{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/overlay/body-locker.ts"], "sourcesContent": ["let previousBodyPaddingRight: string | undefined\nlet previousBodyOverflowSetting: string | undefined\n\nlet activeLocks = 0\n\nexport function lock() {\n  setTimeout(() => {\n    if (activeLocks++ > 0) {\n      return\n    }\n\n    const scrollBarGap =\n      window.innerWidth - document.documentElement.clientWidth\n\n    if (scrollBarGap > 0) {\n      previousBodyPaddingRight = document.body.style.paddingRight\n      document.body.style.paddingRight = `${scrollBarGap}px`\n    }\n\n    previousBodyOverflowSetting = document.body.style.overflow\n    document.body.style.overflow = 'hidden'\n  })\n}\n\nexport function unlock() {\n  setTimeout(() => {\n    if (activeLocks === 0 || --activeLocks !== 0) {\n      return\n    }\n\n    if (previousBodyPaddingRight !== undefined) {\n      document.body.style.paddingRight = previousBodyPaddingRight\n      previousBodyPaddingRight = undefined\n    }\n\n    if (previousBodyOverflowSetting !== undefined) {\n      document.body.style.overflow = previousBodyOverflowSetting\n      previousBodyOverflowSetting = undefined\n    }\n  })\n}\n"], "names": ["previousBodyPaddingRight", "previousBodyOverflowSetting", "activeLocks", "lock", "setTimeout", "scrollBarGap", "window", "innerWidth", "document", "documentElement", "clientWidth", "body", "style", "paddingRight", "overflow", "unlock", "undefined"], "mappings": "AAAA,IAAIA;AACJ,IAAIC;AAEJ,IAAIC,cAAc;AAElB,OAAO,SAASC;IACdC,WAAW;QACT,IAAIF,gBAAgB,GAAG;YACrB;QACF;QAEA,MAAMG,eACJC,OAAOC,UAAU,GAAGC,SAASC,eAAe,CAACC,WAAW;QAE1D,IAAIL,eAAe,GAAG;YACpBL,2BAA2BQ,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY;YAC3DL,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAG,AAAC,KAAER,eAAa;QACrD;QAEAJ,8BAA8BO,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ;QAC1DN,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAG;IACjC;AACF;AAEA,OAAO,SAASC;IACdX,WAAW;QACT,IAAIF,gBAAgB,KAAK,EAAEA,gBAAgB,GAAG;YAC5C;QACF;QAEA,IAAIF,6BAA6BgB,WAAW;YAC1CR,SAASG,IAAI,CAACC,KAAK,CAACC,YAAY,GAAGb;YACnCA,2BAA2BgB;QAC7B;QAEA,IAAIf,gCAAgCe,WAAW;YAC7CR,SAASG,IAAI,CAACC,KAAK,CAACE,QAAQ,GAAGb;YAC/BA,8BAA8Be;QAChC;IACF;AACF"}