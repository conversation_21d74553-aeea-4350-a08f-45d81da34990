{"version": 3, "sources": ["../../src/lib/client-and-server-references.ts"], "sourcesContent": ["import { extractInfoFromServerReferenceId } from '../shared/lib/server-reference-info'\n\n// Only contains the properties we're interested in.\nexport interface ServerReference {\n  $$typeof: Symbol\n  $$id: string\n}\n\nexport type ServerFunction = ServerReference &\n  ((...args: unknown[]) => Promise<unknown>)\n\nexport function isServerReference<T>(\n  value: T & Partial<ServerReference>\n): value is T & ServerFunction {\n  return value.$$typeof === Symbol.for('react.server.reference')\n}\n\nexport function isUseCacheFunction<T>(\n  value: T & Partial<ServerReference>\n): value is T & ServerFunction {\n  if (!isServerReference(value)) {\n    return false\n  }\n\n  const { type } = extractInfoFromServerReferenceId(value.$$id)\n\n  return type === 'use-cache'\n}\n\nexport function isClientReference(mod: any): boolean {\n  const defaultExport = mod?.default || mod\n  return defaultExport?.$$typeof === Symbol.for('react.client.reference')\n}\n"], "names": ["extractInfoFromServerReferenceId", "isServerReference", "value", "$$typeof", "Symbol", "for", "isUseCacheFunction", "type", "$$id", "isClientReference", "mod", "defaultExport", "default"], "mappings": "AAAA,SAASA,gCAAgC,QAAQ,sCAAqC;AAWtF,OAAO,SAASC,kBACdC,KAAmC;IAEnC,OAAOA,MAAMC,QAAQ,KAAKC,OAAOC,GAAG,CAAC;AACvC;AAEA,OAAO,SAASC,mBACdJ,KAAmC;IAEnC,IAAI,CAACD,kBAAkBC,QAAQ;QAC7B,OAAO;IACT;IAEA,MAAM,EAAEK,IAAI,EAAE,GAAGP,iCAAiCE,MAAMM,IAAI;IAE5D,OAAOD,SAAS;AAClB;AAEA,OAAO,SAASE,kBAAkBC,GAAQ;IACxC,MAAMC,gBAAgBD,CAAAA,uBAAAA,IAAKE,OAAO,KAAIF;IACtC,OAAOC,CAAAA,iCAAAA,cAAeR,QAAQ,MAAKC,OAAOC,GAAG,CAAC;AAChD"}