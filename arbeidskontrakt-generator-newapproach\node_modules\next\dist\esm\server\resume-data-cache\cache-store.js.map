{"version": 3, "sources": ["../../../src/server/resume-data-cache/cache-store.ts"], "sourcesContent": ["import {\n  arrayBufferToString,\n  stringToUint8Array,\n} from '../app-render/encryption-utils'\nimport type { CacheEntry } from '../lib/cache-handlers/types'\nimport type { CachedFetchValue } from '../response-cache/types'\n\n/**\n * A generic cache store type that provides a subset of Map functionality\n */\ntype CacheStore<T> = Pick<\n  Map<string, T>,\n  'entries' | 'keys' | 'size' | 'get' | 'set'\n>\n\n/**\n * A cache store specifically for fetch cache values\n */\nexport type FetchCacheStore = CacheStore<CachedFetchValue>\n\n/**\n * A cache store for encrypted bound args of inline server functions.\n */\nexport type EncryptedBoundArgsCacheStore = CacheStore<string>\n\n/**\n * An in-memory-only cache store for decrypted bound args of inline server\n * functions.\n */\nexport type DecryptedBoundArgsCacheStore = CacheStore<string>\n\n/**\n * Serialized format for \"use cache\" entries\n */\ninterface UseCacheCacheStoreSerialized {\n  value: string\n  tags: string[]\n  stale: number\n  timestamp: number\n  expire: number\n  revalidate: number\n}\n\n/**\n * A cache store specifically for \"use cache\" values that stores promises of\n * cache entries.\n */\nexport type UseCacheCacheStore = CacheStore<Promise<CacheEntry>>\n\n/**\n * Parses serialized cache entries into a UseCacheCacheStore\n * @param entries - The serialized entries to parse\n * @returns A new UseCacheCacheStore containing the parsed entries\n */\nexport function parseUseCacheCacheStore(\n  entries: Iterable<[string, UseCacheCacheStoreSerialized]>\n): UseCacheCacheStore {\n  const store = new Map<string, Promise<CacheEntry>>()\n\n  for (const [\n    key,\n    { value, tags, stale, timestamp, expire, revalidate },\n  ] of entries) {\n    store.set(\n      key,\n      Promise.resolve({\n        // Create a ReadableStream from the Uint8Array\n        value: new ReadableStream<Uint8Array>({\n          start(controller) {\n            // Enqueue the Uint8Array to the stream\n            controller.enqueue(stringToUint8Array(atob(value)))\n\n            // Close the stream\n            controller.close()\n          },\n        }),\n        tags,\n        stale,\n        timestamp,\n        expire,\n        revalidate,\n      })\n    )\n  }\n\n  return store\n}\n\n/**\n * Serializes UseCacheCacheStore entries into an array of key-value pairs\n * @param entries - The store entries to stringify\n * @returns A promise that resolves to an array of key-value pairs with serialized values\n */\nexport async function serializeUseCacheCacheStore(\n  entries: IterableIterator<[string, Promise<CacheEntry>]>\n): Promise<[string, UseCacheCacheStoreSerialized][]> {\n  return Promise.all(\n    Array.from(entries).map(([key, value]) => {\n      return value.then(async (entry) => {\n        const [left, right] = entry.value.tee()\n        entry.value = right\n\n        let binaryString: string = ''\n\n        // We want to encode the value as a string, but we aren't sure if the\n        // value is a a stream of UTF-8 bytes or not, so let's just encode it\n        // as a string using base64.\n        for await (const chunk of left) {\n          binaryString += arrayBufferToString(chunk)\n        }\n\n        return [\n          key,\n          {\n            // Encode the value as a base64 string.\n            value: btoa(binaryString),\n            tags: entry.tags,\n            stale: entry.stale,\n            timestamp: entry.timestamp,\n            expire: entry.expire,\n            revalidate: entry.revalidate,\n          },\n        ] satisfies [string, UseCacheCacheStoreSerialized]\n      })\n    })\n  )\n}\n"], "names": ["arrayBufferToString", "stringToUint8Array", "parseUseCacheCacheStore", "entries", "store", "Map", "key", "value", "tags", "stale", "timestamp", "expire", "revalidate", "set", "Promise", "resolve", "ReadableStream", "start", "controller", "enqueue", "atob", "close", "serializeUseCacheCacheStore", "all", "Array", "from", "map", "then", "entry", "left", "right", "tee", "binaryString", "chunk", "btoa"], "mappings": "AAAA,SACEA,mBAAmB,EACnBC,kBAAkB,QACb,iCAAgC;AA8CvC;;;;CAIC,GACD,OAAO,SAASC,wBACdC,OAAyD;IAEzD,MAAMC,QAAQ,IAAIC;IAElB,KAAK,MAAM,CACTC,KACA,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,UAAU,EAAE,CACtD,IAAIT,QAAS;QACZC,MAAMS,GAAG,CACPP,KACAQ,QAAQC,OAAO,CAAC;YACd,8CAA8C;YAC9CR,OAAO,IAAIS,eAA2B;gBACpCC,OAAMC,UAAU;oBACd,uCAAuC;oBACvCA,WAAWC,OAAO,CAAClB,mBAAmBmB,KAAKb;oBAE3C,mBAAmB;oBACnBW,WAAWG,KAAK;gBAClB;YACF;YACAb;YACAC;YACAC;YACAC;YACAC;QACF;IAEJ;IAEA,OAAOR;AACT;AAEA;;;;CAIC,GACD,OAAO,eAAekB,4BACpBnB,OAAwD;IAExD,OAAOW,QAAQS,GAAG,CAChBC,MAAMC,IAAI,CAACtB,SAASuB,GAAG,CAAC,CAAC,CAACpB,KAAKC,MAAM;QACnC,OAAOA,MAAMoB,IAAI,CAAC,OAAOC;YACvB,MAAM,CAACC,MAAMC,MAAM,GAAGF,MAAMrB,KAAK,CAACwB,GAAG;YACrCH,MAAMrB,KAAK,GAAGuB;YAEd,IAAIE,eAAuB;YAE3B,qEAAqE;YACrE,qEAAqE;YACrE,4BAA4B;YAC5B,WAAW,MAAMC,SAASJ,KAAM;gBAC9BG,gBAAgBhC,oBAAoBiC;YACtC;YAEA,OAAO;gBACL3B;gBACA;oBACE,uCAAuC;oBACvCC,OAAO2B,KAAKF;oBACZxB,MAAMoB,MAAMpB,IAAI;oBAChBC,OAAOmB,MAAMnB,KAAK;oBAClBC,WAAWkB,MAAMlB,SAAS;oBAC1BC,QAAQiB,MAAMjB,MAAM;oBACpBC,YAAYgB,MAAMhB,UAAU;gBAC9B;aACD;QACH;IACF;AAEJ"}