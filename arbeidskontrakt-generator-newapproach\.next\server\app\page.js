/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"4c5ceb8252e9\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRFNLXFxEZXNrdG9wXFxfX1NDUkFUQ0hfX1xcMjAyNS4wNi4yNC1rbC4yMS41My0ta29udHJha3Rza2plbWFcXGtvbnRyYWt0c2tqZW1hX3YwMDFcXGFyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2hcXGFwcFxcZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0YzVjZWI4MjUyZTlcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n\n\nconst metadata = {\n    title: 'v0 App',\n    description: 'Created with v0',\n    generator: 'v0.dev'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDc0I7QUFFZixNQUFNQSxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFdBQVc7QUFDYixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdSO0lBQ0EscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEU0tcXERlc2t0b3BcXF9fU0NSQVRDSF9fXFwyMDI1LjA2LjI0LWtsLjIxLjUzLS1rb250cmFrdHNramVtYVxca29udHJha3Rza2plbWFfdjAwMVxcYXJiZWlkc2tvbnRyYWt0LWdlbmVyYXRvci1uZXdhcHByb2FjaFxcYXBwXFxsYXlvdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tICduZXh0J1xuaW1wb3J0ICcuL2dsb2JhbHMuY3NzJ1xuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ3YwIEFwcCcsXG4gIGRlc2NyaXB0aW9uOiAnQ3JlYXRlZCB3aXRoIHYwJyxcbiAgZ2VuZXJhdG9yOiAndjAuZGV2Jyxcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiPlxuICAgICAgPGJvZHk+e2NoaWxkcmVufTwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJnZW5lcmF0b3IiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\Users\\<USER>\\Desktop\\__SCRATCH__\\2025.06.24-kl.21.53--kontraktskjema\\kontraktskjema_v001\\arbeidskontrakt-generator-newapproach\\app\\page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module0, \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RTSyU1QyU1Q0Rlc2t0b3AlNUMlNUNfX1NDUkFUQ0hfXyU1QyU1QzIwMjUuMDYuMjQta2wuMjEuNTMtLWtvbnRyYWt0c2tqZW1hJTVDJTVDa29udHJha3Rza2plbWFfdjAwMSU1QyU1Q2FyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2glNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXlMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU0tcXFxcRGVza3RvcFxcXFxfX1NDUkFUQ0hfX1xcXFwyMDI1LjA2LjI0LWtsLjIxLjUzLS1rb250cmFrdHNramVtYVxcXFxrb250cmFrdHNramVtYV92MDAxXFxcXGFyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2hcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContractGenerator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/progress */ \"(ssr)/./components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _components_BasicInfoStep__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/BasicInfoStep */ \"(ssr)/./components/BasicInfoStep.tsx\");\n/* harmony import */ var _components_AdvancedSettingsStep__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/AdvancedSettingsStep */ \"(ssr)/./components/AdvancedSettingsStep.tsx\");\n/* harmony import */ var _components_ContractGenerationStep__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ContractGenerationStep */ \"(ssr)/./components/ContractGenerationStep.tsx\");\n/* harmony import */ var _components_Header__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/Header */ \"(ssr)/./components/Header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nconst initialFormData = {\n    // Basic Info\n    employeeName: \"\",\n    employeeAddress: \"\",\n    employeeBirthDate: \"\",\n    startDate: \"\",\n    position: \"\",\n    hourlyRate: 300,\n    accountNumber: \"\",\n    employmentType: \"fast\",\n    isTemporary: false,\n    temporaryEndDate: \"\",\n    temporaryReason: \"\",\n    probationPeriod: true,\n    probationMonths: 6,\n    ownTools: false,\n    // Advanced Settings\n    companyName: \"Ringerike Landskap AS\",\n    companyOrgNumber: \"***********\",\n    companyAddress: \"Birchs vei 7, 3530 Røyse\",\n    workingHoursPerWeek: 37.5,\n    workingTime: \"07:00-15:00\",\n    breakTime: \"Minst 30 min. ubetalt pause ved arbeidsdag >5,5 t\",\n    overtimeRate: 40,\n    paymentDay: 5,\n    toolAllowance: \"kr 1,85 per time ved bruk av eget håndverktøy\",\n    travelAllowance: \"Statens gjeldende satser (pt. 3,50 kr/km)\",\n    pensionProvider: \"Storebrand\",\n    pensionOrgNumber: \"***********\",\n    insuranceProvider: \"Gjensidige Forsikring ASA\",\n    insuranceOrgNumber: \"***********\",\n    noticePeriod: \"1 måned gjensidig etter prøvetid\",\n    contractDuration: \"\",\n    notificationRules: \"Endringer varsles minimum 2 uker i forveien der mulig\"\n};\nfunction ContractGenerator() {\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialFormData);\n    const steps = [\n        {\n            number: 1,\n            title: \"Grunnleggende informasjon\",\n            description: \"Personopplysninger og stillingsinformasjon\"\n        },\n        {\n            number: 2,\n            title: \"Kontraktdetaljer\",\n            description: \"Bedriftsinformasjon og juridiske bestemmelser\"\n        },\n        {\n            number: 3,\n            title: \"Kontraktgenerering\",\n            description: \"Sammendrag og generering av kontrakt\"\n        }\n    ];\n    const updateFormData = (data)=>{\n        setFormData((prev)=>({\n                ...prev,\n                ...data,\n                // Update employmentType based on isTemporary\n                employmentType: data.isTemporary !== undefined ? data.isTemporary ? \"midlertidig\" : \"fast\" : prev.employmentType\n            }));\n    };\n    const nextStep = ()=>{\n        if (currentStep < 3) {\n            setCurrentStep(currentStep + 1);\n        }\n    };\n    const prevStep = ()=>{\n        if (currentStep > 1) {\n            setCurrentStep(currentStep - 1);\n        }\n    };\n    const goToStep = (step)=>{\n        setCurrentStep(step);\n    };\n    const renderStep = ()=>{\n        switch(currentStep){\n            case 1:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_BasicInfoStep__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 16\n                }, this);\n            case 2:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AdvancedSettingsStep__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    formData: formData,\n                    updateFormData: updateFormData,\n                    onNext: nextStep,\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 11\n                }, this);\n            case 3:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractGenerationStep__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    formData: formData,\n                    onPrev: prevStep\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 16\n                }, this);\n            default:\n                return null;\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-gray-50 via-green-50 to-lime-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Header__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"pb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                            className: \"text-lg\",\n                                            children: \"Fremdrift\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                            lineNumber: 117,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: [\n                                                \"Steg \",\n                                                currentStep,\n                                                \" av 3\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_4__.Progress, {\n                                    value: currentStep / 3 * 100,\n                                    className: \"mb-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"pt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-between\",\n                                children: steps.map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                className: `w-12 h-12 rounded-full p-0 mb-2 ${currentStep === step.number ? \"bg-green-500 text-white hover:bg-green-600\" : currentStep > step.number ? \"bg-green-100 text-green-600 hover:bg-green-200\" : \"bg-gray-100 text-gray-400\"}`,\n                                                onClick: ()=>goToStep(step.number),\n                                                children: currentStep > step.number ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 50\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 88\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: `font-medium text-sm ${currentStep >= step.number ? \"text-gray-900\" : \"text-gray-500\"}`,\n                                                        children: step.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-500 mt-1 max-w-32\",\n                                                        children: step.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, step.number, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                renderStep()\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n            lineNumber: 109,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\app\\\\page.tsx\",\n        lineNumber: 108,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/AdvancedSettingsStep.tsx":
/*!*********************************************!*\
  !*** ./components/AdvancedSettingsStep.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AdvancedSettingsStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/building.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scale.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,ArrowRight,Building,Clock,Scale,Shield!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction AdvancedSettingsStep({ formData, updateFormData, onNext, onPrev }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 24,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Kontraktdetaljer\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 23,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                children: \"Bedriftsinformasjon og juridiske bestemmelser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"companyName\",\n                                                children: \"Bedriftsnavn\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 32,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"companyName\",\n                                                value: formData.companyName,\n                                                onChange: (e)=>updateFormData({\n                                                        companyName: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 33,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"companyOrgNumber\",\n                                                children: \"Organisasjonsnummer\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 40,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"companyOrgNumber\",\n                                                value: formData.companyOrgNumber,\n                                                onChange: (e)=>updateFormData({\n                                                        companyOrgNumber: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"companyAddress\",\n                                        children: \"Bedriftsadresse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"companyAddress\",\n                                        value: formData.companyAddress,\n                                        onChange: (e)=>updateFormData({\n                                                companyAddress: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 48,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Arbeidstid og godtgj\\xf8relser\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                children: \"Arbeidstidsordning og tilleggsgodtgj\\xf8relser\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"workingHoursPerWeek\",\n                                                children: \"Timer per uke\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"workingHoursPerWeek\",\n                                                type: \"number\",\n                                                value: formData.workingHoursPerWeek,\n                                                onChange: (e)=>updateFormData({\n                                                        workingHoursPerWeek: Number(e.target.value)\n                                                    }),\n                                                step: \"0.5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"workingTime\",\n                                                children: \"Arbeidstid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"workingTime\",\n                                                value: formData.workingTime,\n                                                onChange: (e)=>updateFormData({\n                                                        workingTime: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 81,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"breakTime\",\n                                        children: \"Pauseordning\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"breakTime\",\n                                        value: formData.breakTime,\n                                        onChange: (e)=>updateFormData({\n                                                breakTime: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"overtimeRate\",\n                                                children: \"Overtidstillegg (%)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"overtimeRate\",\n                                                type: \"number\",\n                                                value: formData.overtimeRate,\n                                                onChange: (e)=>updateFormData({\n                                                        overtimeRate: Number(e.target.value)\n                                                    }),\n                                                min: \"40\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"paymentDay\",\n                                                children: \"L\\xf8nnsutbetaling (dag i mnd)\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"paymentDay\",\n                                                type: \"number\",\n                                                min: \"1\",\n                                                max: \"31\",\n                                                value: formData.paymentDay,\n                                                onChange: (e)=>updateFormData({\n                                                        paymentDay: Number(e.target.value)\n                                                    }),\n                                                placeholder: \"5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 107,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 96,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"toolAllowance\",\n                                        children: \"Verkt\\xf8ygodtgj\\xf8relse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"toolAllowance\",\n                                        value: formData.toolAllowance,\n                                        onChange: (e)=>updateFormData({\n                                                toolAllowance: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 120,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"travelAllowance\",\n                                        children: \"Kj\\xf8regodtgj\\xf8relse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"travelAllowance\",\n                                        value: formData.travelAllowance,\n                                        onChange: (e)=>updateFormData({\n                                                travelAllowance: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 130,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Pensjon og forsikring\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                children: \"Pensjons- og forsikringsordninger\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"pensionProvider\",\n                                                children: \"Pensjonsleverand\\xf8r\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"pensionProvider\",\n                                                value: formData.pensionProvider,\n                                                onChange: (e)=>updateFormData({\n                                                        pensionProvider: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"pensionOrgNumber\",\n                                                children: \"Pensjon org.nr\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"pensionOrgNumber\",\n                                                value: formData.pensionOrgNumber,\n                                                onChange: (e)=>updateFormData({\n                                                        pensionOrgNumber: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"insuranceProvider\",\n                                                children: \"Forsikringsleverand\\xf8r\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"insuranceProvider\",\n                                                value: formData.insuranceProvider,\n                                                onChange: (e)=>updateFormData({\n                                                        insuranceProvider: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                                htmlFor: \"insuranceOrgNumber\",\n                                                children: \"Forsikring org.nr\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                id: \"insuranceOrgNumber\",\n                                                value: formData.insuranceOrgNumber,\n                                                onChange: (e)=>updateFormData({\n                                                        insuranceOrgNumber: e.target.value\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Juridiske bestemmelser\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardDescription, {\n                                children: \"Oppsigelsesfrister og andre juridiske forhold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"noticePeriod\",\n                                        children: \"Oppsigelsesfrister\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"noticePeriod\",\n                                        value: formData.noticePeriod,\n                                        onChange: (e)=>updateFormData({\n                                                noticePeriod: e.target.value\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"contractDuration\",\n                                        children: \"Kontraktvarighet (kun midlertidig)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                        id: \"contractDuration\",\n                                        value: formData.contractDuration,\n                                        onChange: (e)=>updateFormData({\n                                                contractDuration: e.target.value\n                                            }),\n                                        placeholder: \"F.eks. 6 m\\xe5neder, til 31.12.2024\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 204,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_4__.Label, {\n                                        htmlFor: \"notificationRules\",\n                                        children: \"Varslingsregler\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                        id: \"notificationRules\",\n                                        value: formData.notificationRules,\n                                        onChange: (e)=>updateFormData({\n                                                notificationRules: e.target.value\n                                            }),\n                                        rows: 3\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                lineNumber: 187,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        variant: \"outline\",\n                        onClick: onPrev,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            \"Forrige steg\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        onClick: onNext,\n                        className: \"bg-green-500 hover:bg-green-600\",\n                        children: [\n                            \"Neste steg\",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_ArrowRight_Building_Clock_Scale_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"h-4 w-4 ml-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\AdvancedSettingsStep.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL0FkdmFuY2VkU2V0dGluZ3NTdGVwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0c7QUFDakQ7QUFDRjtBQUNBO0FBQ007QUFDaUM7QUFVckUsU0FBU2UscUJBQXFCLEVBQUVDLFFBQVEsRUFBRUMsY0FBYyxFQUFFQyxNQUFNLEVBQUVDLE1BQU0sRUFBNkI7SUFDbEgscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDckIscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBO3dCQUFDa0IsV0FBVTs7MENBQ3BCLDhEQUFDakIsMERBQVNBO2dDQUFDaUIsV0FBVTs7a0RBQ25CLDhEQUFDViw0SEFBUUE7d0NBQUNVLFdBQVU7Ozs7OztvQ0FBZ0M7Ozs7Ozs7MENBR3RELDhEQUFDbkIsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBRW5CLDhEQUFDRCw0REFBV0E7d0JBQUNvQixXQUFVOzswQ0FDckIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDYix1REFBS0E7Z0RBQUNlLFNBQVE7MERBQWM7Ozs7OzswREFDN0IsOERBQUNoQix1REFBS0E7Z0RBQ0ppQixJQUFHO2dEQUNIQyxPQUFPUixTQUFTUyxXQUFXO2dEQUMzQkMsVUFBVSxDQUFDQyxJQUFNVixlQUFlO3dEQUFFUSxhQUFhRSxFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUM7Ozs7Ozs7Ozs7OztrREFHbEUsOERBQUNKOzswREFDQyw4REFBQ2IsdURBQUtBO2dEQUFDZSxTQUFROzBEQUFtQjs7Ozs7OzBEQUNsQyw4REFBQ2hCLHVEQUFLQTtnREFDSmlCLElBQUc7Z0RBQ0hDLE9BQU9SLFNBQVNhLGdCQUFnQjtnREFDaENILFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTt3REFBRVksa0JBQWtCRixFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJekUsOERBQUNKOztrREFDQyw4REFBQ2IsdURBQUtBO3dDQUFDZSxTQUFRO2tEQUFpQjs7Ozs7O2tEQUNoQyw4REFBQ2hCLHVEQUFLQTt3Q0FDSmlCLElBQUc7d0NBQ0hDLE9BQU9SLFNBQVNjLGNBQWM7d0NBQzlCSixVQUFVLENBQUNDLElBQU1WLGVBQWU7Z0RBQUVhLGdCQUFnQkgsRUFBRUMsTUFBTSxDQUFDSixLQUFLOzRDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTXpFLDhEQUFDeEIscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBO3dCQUFDa0IsV0FBVTs7MENBQ3BCLDhEQUFDakIsMERBQVNBO2dDQUFDaUIsV0FBVTs7a0RBQ25CLDhEQUFDVCw0SEFBS0E7d0NBQUNTLFdBQVU7Ozs7OztvQ0FBZ0M7Ozs7Ozs7MENBR25ELDhEQUFDbkIsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBRW5CLDhEQUFDRCw0REFBV0E7d0JBQUNvQixXQUFVOzswQ0FDckIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDYix1REFBS0E7Z0RBQUNlLFNBQVE7MERBQXNCOzs7Ozs7MERBQ3JDLDhEQUFDaEIsdURBQUtBO2dEQUNKaUIsSUFBRztnREFDSFEsTUFBSztnREFDTFAsT0FBT1IsU0FBU2dCLG1CQUFtQjtnREFDbkNOLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTt3REFBRWUscUJBQXFCQyxPQUFPTixFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUU7Z0RBQzlFVSxNQUFLOzs7Ozs7Ozs7Ozs7a0RBR1QsOERBQUNkOzswREFDQyw4REFBQ2IsdURBQUtBO2dEQUFDZSxTQUFROzBEQUFjOzs7Ozs7MERBQzdCLDhEQUFDaEIsdURBQUtBO2dEQUNKaUIsSUFBRztnREFDSEMsT0FBT1IsU0FBU21CLFdBQVc7Z0RBQzNCVCxVQUFVLENBQUNDLElBQU1WLGVBQWU7d0RBQUVrQixhQUFhUixFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJcEUsOERBQUNKOztrREFDQyw4REFBQ2IsdURBQUtBO3dDQUFDZSxTQUFRO2tEQUFZOzs7Ozs7a0RBQzNCLDhEQUFDaEIsdURBQUtBO3dDQUNKaUIsSUFBRzt3Q0FDSEMsT0FBT1IsU0FBU29CLFNBQVM7d0NBQ3pCVixVQUFVLENBQUNDLElBQU1WLGVBQWU7Z0RBQUVtQixXQUFXVCxFQUFFQyxNQUFNLENBQUNKLEtBQUs7NENBQUM7Ozs7Ozs7Ozs7OzswQ0FHaEUsOERBQUNKO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDYix1REFBS0E7Z0RBQUNlLFNBQVE7MERBQWU7Ozs7OzswREFDOUIsOERBQUNoQix1REFBS0E7Z0RBQ0ppQixJQUFHO2dEQUNIUSxNQUFLO2dEQUNMUCxPQUFPUixTQUFTcUIsWUFBWTtnREFDNUJYLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTt3REFBRW9CLGNBQWNKLE9BQU9OLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSztvREFBRTtnREFDdkVjLEtBQUk7Ozs7Ozs7Ozs7OztrREFHUiw4REFBQ2xCOzswREFDQyw4REFBQ2IsdURBQUtBO2dEQUFDZSxTQUFROzBEQUFhOzs7Ozs7MERBQzVCLDhEQUFDaEIsdURBQUtBO2dEQUNKaUIsSUFBRztnREFDSFEsTUFBSztnREFDTE8sS0FBSTtnREFDSkMsS0FBSTtnREFDSmYsT0FBT1IsU0FBU3dCLFVBQVU7Z0RBQzFCZCxVQUFVLENBQUNDLElBQU1WLGVBQWU7d0RBQUV1QixZQUFZUCxPQUFPTixFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUU7Z0RBQ3JFaUIsYUFBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUlsQiw4REFBQ3JCOztrREFDQyw4REFBQ2IsdURBQUtBO3dDQUFDZSxTQUFRO2tEQUFnQjs7Ozs7O2tEQUMvQiw4REFBQ2hCLHVEQUFLQTt3Q0FDSmlCLElBQUc7d0NBQ0hDLE9BQU9SLFNBQVMwQixhQUFhO3dDQUM3QmhCLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTtnREFBRXlCLGVBQWVmLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSzs0Q0FBQzs7Ozs7Ozs7Ozs7OzBDQUdwRSw4REFBQ0o7O2tEQUNDLDhEQUFDYix1REFBS0E7d0NBQUNlLFNBQVE7a0RBQWtCOzs7Ozs7a0RBQ2pDLDhEQUFDaEIsdURBQUtBO3dDQUNKaUIsSUFBRzt3Q0FDSEMsT0FBT1IsU0FBUzJCLGVBQWU7d0NBQy9CakIsVUFBVSxDQUFDQyxJQUFNVixlQUFlO2dEQUFFMEIsaUJBQWlCaEIsRUFBRUMsTUFBTSxDQUFDSixLQUFLOzRDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTFFLDhEQUFDeEIscURBQUlBOztrQ0FDSCw4REFBQ0csMkRBQVVBO3dCQUFDa0IsV0FBVTs7MENBQ3BCLDhEQUFDakIsMERBQVNBO2dDQUFDaUIsV0FBVTs7a0RBQ25CLDhEQUFDUiw0SEFBTUE7d0NBQUNRLFdBQVU7Ozs7OztvQ0FBZ0M7Ozs7Ozs7MENBR3BELDhEQUFDbkIsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBRW5CLDhEQUFDRCw0REFBV0E7d0JBQUNvQixXQUFVOzswQ0FDckIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDYix1REFBS0E7Z0RBQUNlLFNBQVE7MERBQWtCOzs7Ozs7MERBQ2pDLDhEQUFDaEIsdURBQUtBO2dEQUNKaUIsSUFBRztnREFDSEMsT0FBT1IsU0FBUzRCLGVBQWU7Z0RBQy9CbEIsVUFBVSxDQUFDQyxJQUFNVixlQUFlO3dEQUFFMkIsaUJBQWlCakIsRUFBRUMsTUFBTSxDQUFDSixLQUFLO29EQUFDOzs7Ozs7Ozs7Ozs7a0RBR3RFLDhEQUFDSjs7MERBQ0MsOERBQUNiLHVEQUFLQTtnREFBQ2UsU0FBUTswREFBbUI7Ozs7OzswREFDbEMsOERBQUNoQix1REFBS0E7Z0RBQ0ppQixJQUFHO2dEQUNIQyxPQUFPUixTQUFTNkIsZ0JBQWdCO2dEQUNoQ25CLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTt3REFBRTRCLGtCQUFrQmxCLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSztvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUl6RSw4REFBQ0o7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDRDs7MERBQ0MsOERBQUNiLHVEQUFLQTtnREFBQ2UsU0FBUTswREFBb0I7Ozs7OzswREFDbkMsOERBQUNoQix1REFBS0E7Z0RBQ0ppQixJQUFHO2dEQUNIQyxPQUFPUixTQUFTOEIsaUJBQWlCO2dEQUNqQ3BCLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTt3REFBRTZCLG1CQUFtQm5CLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSztvREFBQzs7Ozs7Ozs7Ozs7O2tEQUd4RSw4REFBQ0o7OzBEQUNDLDhEQUFDYix1REFBS0E7Z0RBQUNlLFNBQVE7MERBQXFCOzs7Ozs7MERBQ3BDLDhEQUFDaEIsdURBQUtBO2dEQUNKaUIsSUFBRztnREFDSEMsT0FBT1IsU0FBUytCLGtCQUFrQjtnREFDbENyQixVQUFVLENBQUNDLElBQU1WLGVBQWU7d0RBQUU4QixvQkFBb0JwQixFQUFFQyxNQUFNLENBQUNKLEtBQUs7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPL0UsOERBQUN4QixxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7d0JBQUNrQixXQUFVOzswQ0FDcEIsOERBQUNqQiwwREFBU0E7Z0NBQUNpQixXQUFVOztrREFDbkIsOERBQUNQLDRIQUFLQTt3Q0FBQ08sV0FBVTs7Ozs7O29DQUFnQzs7Ozs7OzswQ0FHbkQsOERBQUNuQixnRUFBZUE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FFbkIsOERBQUNELDREQUFXQTt3QkFBQ29CLFdBQVU7OzBDQUNyQiw4REFBQ0Q7O2tEQUNDLDhEQUFDYix1REFBS0E7d0NBQUNlLFNBQVE7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNoQix1REFBS0E7d0NBQ0ppQixJQUFHO3dDQUNIQyxPQUFPUixTQUFTZ0MsWUFBWTt3Q0FDNUJ0QixVQUFVLENBQUNDLElBQU1WLGVBQWU7Z0RBQUUrQixjQUFjckIsRUFBRUMsTUFBTSxDQUFDSixLQUFLOzRDQUFDOzs7Ozs7Ozs7Ozs7MENBR25FLDhEQUFDSjs7a0RBQ0MsOERBQUNiLHVEQUFLQTt3Q0FBQ2UsU0FBUTtrREFBbUI7Ozs7OztrREFDbEMsOERBQUNoQix1REFBS0E7d0NBQ0ppQixJQUFHO3dDQUNIQyxPQUFPUixTQUFTaUMsZ0JBQWdCO3dDQUNoQ3ZCLFVBQVUsQ0FBQ0MsSUFBTVYsZUFBZTtnREFBRWdDLGtCQUFrQnRCLEVBQUVDLE1BQU0sQ0FBQ0osS0FBSzs0Q0FBQzt3Q0FDbkVpQixhQUFZOzs7Ozs7Ozs7Ozs7MENBR2hCLDhEQUFDckI7O2tEQUNDLDhEQUFDYix1REFBS0E7d0NBQUNlLFNBQVE7a0RBQW9COzs7Ozs7a0RBQ25DLDhEQUFDZCw2REFBUUE7d0NBQ1BlLElBQUc7d0NBQ0hDLE9BQU9SLFNBQVNrQyxpQkFBaUI7d0NBQ2pDeEIsVUFBVSxDQUFDQyxJQUFNVixlQUFlO2dEQUFFaUMsbUJBQW1CdkIsRUFBRUMsTUFBTSxDQUFDSixLQUFLOzRDQUFDO3dDQUNwRTJCLE1BQU07Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFNZCw4REFBQy9CO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ2hCLHlEQUFNQTt3QkFBQytDLFNBQVE7d0JBQVVDLFNBQVNsQzs7MENBQ2pDLDhEQUFDViw2SEFBU0E7Z0NBQUNZLFdBQVU7Ozs7Ozs0QkFBaUI7Ozs7Ozs7a0NBR3hDLDhEQUFDaEIseURBQU1BO3dCQUFDZ0QsU0FBU25DO3dCQUFRRyxXQUFVOzs0QkFBa0M7MENBRW5FLDhEQUFDWCw2SEFBVUE7Z0NBQUNXLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUtoQyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEU0tcXERlc2t0b3BcXF9fU0NSQVRDSF9fXFwyMDI1LjA2LjI0LWtsLjIxLjUzLS1rb250cmFrdHNramVtYVxca29udHJha3Rza2plbWFfdjAwMVxcYXJiZWlkc2tvbnRyYWt0LWdlbmVyYXRvci1uZXdhcHByb2FjaFxcY29tcG9uZW50c1xcQWR2YW5jZWRTZXR0aW5nc1N0ZXAudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkRGVzY3JpcHRpb24sIENhcmRIZWFkZXIsIENhcmRUaXRsZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2FyZFwiXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2J1dHRvblwiXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIlxuaW1wb3J0IHsgTGFiZWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2xhYmVsXCJcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS90ZXh0YXJlYVwiXG5pbXBvcnQgeyBBcnJvd0xlZnQsIEFycm93UmlnaHQsIEJ1aWxkaW5nLCBDbG9jaywgU2hpZWxkLCBTY2FsZSB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxuaW1wb3J0IHR5cGUgeyBGb3JtRGF0YSB9IGZyb20gXCJAL3R5cGVzL2NvbnRyYWN0XCJcblxuaW50ZXJmYWNlIEFkdmFuY2VkU2V0dGluZ3NTdGVwUHJvcHMge1xuICBmb3JtRGF0YTogRm9ybURhdGFcbiAgdXBkYXRlRm9ybURhdGE6IChkYXRhOiBQYXJ0aWFsPEZvcm1EYXRhPikgPT4gdm9pZFxuICBvbk5leHQ6ICgpID0+IHZvaWRcbiAgb25QcmV2OiAoKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFkdmFuY2VkU2V0dGluZ3NTdGVwKHsgZm9ybURhdGEsIHVwZGF0ZUZvcm1EYXRhLCBvbk5leHQsIG9uUHJldiB9OiBBZHZhbmNlZFNldHRpbmdzU3RlcFByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgIDxCdWlsZGluZyBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgS29udHJha3RkZXRhbGplclxuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+QmVkcmlmdHNpbmZvcm1hc2pvbiBvZyBqdXJpZGlza2UgYmVzdGVtbWVsc2VyPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNCBwdC0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbXBhbnlOYW1lXCI+QmVkcmlmdHNuYXZuPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJjb21wYW55TmFtZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbXBhbnlOYW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBjb21wYW55TmFtZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiY29tcGFueU9yZ051bWJlclwiPk9yZ2FuaXNhc2pvbnNudW1tZXI8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cImNvbXBhbnlPcmdOdW1iZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5jb21wYW55T3JnTnVtYmVyfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBjb21wYW55T3JnTnVtYmVyOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cImNvbXBhbnlBZGRyZXNzXCI+QmVkcmlmdHNhZHJlc3NlPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBpZD1cImNvbXBhbnlBZGRyZXNzXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbXBhbnlBZGRyZXNzfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKHsgY29tcGFueUFkZHJlc3M6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cblxuICAgICAgPENhcmQ+XG4gICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cInBiLTNcIj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHRleHQtYmFzZVwiPlxuICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNSB3LTUgbXItMiB0ZXh0LWdyZWVuLTUwMFwiIC8+XG4gICAgICAgICAgICBBcmJlaWRzdGlkIG9nIGdvZHRnasO4cmVsc2VyXG4gICAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5BcmJlaWRzdGlkc29yZG5pbmcgb2cgdGlsbGVnZ3Nnb2R0Z2rDuHJlbHNlcjwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJzcGFjZS15LTQgcHQtMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJ3b3JraW5nSG91cnNQZXJXZWVrXCI+VGltZXIgcGVyIHVrZTwvTGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIGlkPVwid29ya2luZ0hvdXJzUGVyV2Vla1wiXG4gICAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLndvcmtpbmdIb3Vyc1BlcldlZWt9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSh7IHdvcmtpbmdIb3Vyc1BlcldlZWs6IE51bWJlcihlLnRhcmdldC52YWx1ZSkgfSl9XG4gICAgICAgICAgICAgICAgc3RlcD1cIjAuNVwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwid29ya2luZ1RpbWVcIj5BcmJlaWRzdGlkPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJ3b3JraW5nVGltZVwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLndvcmtpbmdUaW1lfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyB3b3JraW5nVGltZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJicmVha1RpbWVcIj5QYXVzZW9yZG5pbmc8L0xhYmVsPlxuICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgIGlkPVwiYnJlYWtUaW1lXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmJyZWFrVGltZX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSh7IGJyZWFrVGltZTogZS50YXJnZXQudmFsdWUgfSl9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJvdmVydGltZVJhdGVcIj5PdmVydGlkc3RpbGxlZ2cgKCUpPC9MYWJlbD5cbiAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgaWQ9XCJvdmVydGltZVJhdGVcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5vdmVydGltZVJhdGV9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSh7IG92ZXJ0aW1lUmF0ZTogTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSB9KX1cbiAgICAgICAgICAgICAgICBtaW49XCI0MFwiXG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwicGF5bWVudERheVwiPkzDuG5uc3V0YmV0YWxpbmcgKGRhZyBpIG1uZCk8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cInBheW1lbnREYXlcIlxuICAgICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICAgIG1pbj1cIjFcIlxuICAgICAgICAgICAgICAgIG1heD1cIjMxXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucGF5bWVudERheX1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKHsgcGF5bWVudERheTogTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSB9KX1cbiAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjVcIlxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidG9vbEFsbG93YW5jZVwiPlZlcmt0w7h5Z29kdGdqw7hyZWxzZTwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJ0b29sQWxsb3dhbmNlXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRvb2xBbGxvd2FuY2V9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyB0b29sQWxsb3dhbmNlOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwidHJhdmVsQWxsb3dhbmNlXCI+S2rDuHJlZ29kdGdqw7hyZWxzZTwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJ0cmF2ZWxBbGxvd2FuY2VcIlxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEudHJhdmVsQWxsb3dhbmNlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKHsgdHJhdmVsQWxsb3dhbmNlOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgIDxTaGllbGQgY2xhc3NOYW1lPVwiaC01IHctNSBtci0yIHRleHQtZ3JlZW4tNTAwXCIgLz5cbiAgICAgICAgICAgIFBlbnNqb24gb2cgZm9yc2lrcmluZ1xuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+UGVuc2pvbnMtIG9nIGZvcnNpa3Jpbmdzb3JkbmluZ2VyPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNCBwdC0wXCI+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBlbnNpb25Qcm92aWRlclwiPlBlbnNqb25zbGV2ZXJhbmTDuHI8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cInBlbnNpb25Qcm92aWRlclwiXG4gICAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnBlbnNpb25Qcm92aWRlcn1cbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHVwZGF0ZUZvcm1EYXRhKHsgcGVuc2lvblByb3ZpZGVyOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJwZW5zaW9uT3JnTnVtYmVyXCI+UGVuc2pvbiBvcmcubnI8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cInBlbnNpb25PcmdOdW1iZXJcIlxuICAgICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wZW5zaW9uT3JnTnVtYmVyfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBwZW5zaW9uT3JnTnVtYmVyOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbnN1cmFuY2VQcm92aWRlclwiPkZvcnNpa3JpbmdzbGV2ZXJhbmTDuHI8L0xhYmVsPlxuICAgICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgICBpZD1cImluc3VyYW5jZVByb3ZpZGVyXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuaW5zdXJhbmNlUHJvdmlkZXJ9XG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSh7IGluc3VyYW5jZVByb3ZpZGVyOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJpbnN1cmFuY2VPcmdOdW1iZXJcIj5Gb3JzaWtyaW5nIG9yZy5ucjwvTGFiZWw+XG4gICAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICAgIGlkPVwiaW5zdXJhbmNlT3JnTnVtYmVyXCJcbiAgICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuaW5zdXJhbmNlT3JnTnVtYmVyfVxuICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBpbnN1cmFuY2VPcmdOdW1iZXI6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJwYi0zXCI+XG4gICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciB0ZXh0LWJhc2VcIj5cbiAgICAgICAgICAgIDxTY2FsZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi01MDBcIiAvPlxuICAgICAgICAgICAgSnVyaWRpc2tlIGJlc3RlbW1lbHNlclxuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+T3Bwc2lnZWxzZXNmcmlzdGVyIG9nIGFuZHJlIGp1cmlkaXNrZSBmb3Job2xkPC9DYXJkRGVzY3JpcHRpb24+XG4gICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNCBwdC0wXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwibm90aWNlUGVyaW9kXCI+T3Bwc2lnZWxzZXNmcmlzdGVyPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBpZD1cIm5vdGljZVBlcmlvZFwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ub3RpY2VQZXJpb2R9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBub3RpY2VQZXJpb2Q6IGUudGFyZ2V0LnZhbHVlIH0pfVxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJjb250cmFjdER1cmF0aW9uXCI+S29udHJha3R2YXJpZ2hldCAoa3VuIG1pZGxlcnRpZGlnKTwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgaWQ9XCJjb250cmFjdER1cmF0aW9uXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNvbnRyYWN0RHVyYXRpb259XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gdXBkYXRlRm9ybURhdGEoeyBjb250cmFjdER1cmF0aW9uOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCJGLmVrcy4gNiBtw6VuZWRlciwgdGlsIDMxLjEyLjIwMjRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGh0bWxGb3I9XCJub3RpZmljYXRpb25SdWxlc1wiPlZhcnNsaW5nc3JlZ2xlcjwvTGFiZWw+XG4gICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgaWQ9XCJub3RpZmljYXRpb25SdWxlc1wiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5ub3RpZmljYXRpb25SdWxlc31cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVGb3JtRGF0YSh7IG5vdGlmaWNhdGlvblJ1bGVzOiBlLnRhcmdldC52YWx1ZSB9KX1cbiAgICAgICAgICAgICAgcm93cz17M31cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICA8L0NhcmQ+XG5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e29uUHJldn0+XG4gICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgIEZvcnJpZ2Ugc3RlZ1xuICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtvbk5leHR9IGNsYXNzTmFtZT1cImJnLWdyZWVuLTUwMCBob3ZlcjpiZy1ncmVlbi02MDBcIj5cbiAgICAgICAgICBOZXN0ZSBzdGVnXG4gICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNCBtbC0yXCIgLz5cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiVGV4dGFyZWEiLCJBcnJvd0xlZnQiLCJBcnJvd1JpZ2h0IiwiQnVpbGRpbmciLCJDbG9jayIsIlNoaWVsZCIsIlNjYWxlIiwiQWR2YW5jZWRTZXR0aW5nc1N0ZXAiLCJmb3JtRGF0YSIsInVwZGF0ZUZvcm1EYXRhIiwib25OZXh0Iiwib25QcmV2IiwiZGl2IiwiY2xhc3NOYW1lIiwiaHRtbEZvciIsImlkIiwidmFsdWUiLCJjb21wYW55TmFtZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsImNvbXBhbnlPcmdOdW1iZXIiLCJjb21wYW55QWRkcmVzcyIsInR5cGUiLCJ3b3JraW5nSG91cnNQZXJXZWVrIiwiTnVtYmVyIiwic3RlcCIsIndvcmtpbmdUaW1lIiwiYnJlYWtUaW1lIiwib3ZlcnRpbWVSYXRlIiwibWluIiwibWF4IiwicGF5bWVudERheSIsInBsYWNlaG9sZGVyIiwidG9vbEFsbG93YW5jZSIsInRyYXZlbEFsbG93YW5jZSIsInBlbnNpb25Qcm92aWRlciIsInBlbnNpb25PcmdOdW1iZXIiLCJpbnN1cmFuY2VQcm92aWRlciIsImluc3VyYW5jZU9yZ051bWJlciIsIm5vdGljZVBlcmlvZCIsImNvbnRyYWN0RHVyYXRpb24iLCJub3RpZmljYXRpb25SdWxlcyIsInJvd3MiLCJ2YXJpYW50Iiwib25DbGljayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/AdvancedSettingsStep.tsx\n");

/***/ }),

/***/ "(ssr)/./components/BasicInfoStep.tsx":
/*!**************************************!*\
  !*** ./components/BasicInfoStep.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ BasicInfoStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(ssr)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(ssr)/./components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Briefcase,DollarSign,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Briefcase,DollarSign,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/briefcase.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Briefcase,DollarSign,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Briefcase,DollarSign,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction BasicInfoStep({ formData, updateFormData, onNext }) {\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateForm = ()=>{\n        const newErrors = {};\n        if (!formData.employeeName.trim()) newErrors.employeeName = \"Navn er påkrevd\";\n        if (!formData.employeeAddress.trim()) newErrors.employeeAddress = \"Adresse er påkrevd\";\n        if (!formData.employeeBirthDate) newErrors.employeeBirthDate = \"Fødselsdato er påkrevd\";\n        if (!formData.startDate) newErrors.startDate = \"Startdato er påkrevd\";\n        if (!formData.position.trim()) newErrors.position = \"Stillingstittel er påkrevd\";\n        if (!formData.accountNumber.trim()) newErrors.accountNumber = \"Kontonummer er påkrevd\";\n        if (formData.isTemporary && !formData.temporaryEndDate) newErrors.temporaryEndDate = \"Sluttdato er påkrevd for midlertidig ansettelse\";\n        if (formData.isTemporary && !formData.temporaryReason.trim()) newErrors.temporaryReason = \"Grunnlag er påkrevd for midlertidig ansettelse\";\n        if (formData.probationPeriod && (formData.probationMonths < 1 || formData.probationMonths > 6)) newErrors.probationMonths = \"Prøvetid må være mellom 1 og 6 måneder\";\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNext = ()=>{\n        validateForm() // Vis advarsler, men ikke stopp brukeren\n        ;\n        onNext() // Gå videre uansett\n        ;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Personopplysninger\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Grunnleggende informasjon om den ansatte\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"employeeName\",\n                                                children: \"Fullt navn *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 60,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"employeeName\",\n                                                value: formData.employeeName,\n                                                onChange: (e)=>updateFormData({\n                                                        employeeName: e.target.value\n                                                    }),\n                                                placeholder: \"Ola Nordmann\",\n                                                className: errors.employeeName ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 61,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.employeeName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.employeeName\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 68,\n                                                columnNumber: 39\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"employeeBirthDate\",\n                                                children: \"F\\xf8dselsdato *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 71,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"employeeBirthDate\",\n                                                type: \"date\",\n                                                value: formData.employeeBirthDate,\n                                                onChange: (e)=>updateFormData({\n                                                        employeeBirthDate: e.target.value\n                                                    }),\n                                                className: errors.employeeBirthDate ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 72,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.employeeBirthDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.employeeBirthDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 44\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"employeeAddress\",\n                                        children: \"Adresse *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        id: \"employeeAddress\",\n                                        value: formData.employeeAddress,\n                                        onChange: (e)=>updateFormData({\n                                                employeeAddress: e.target.value\n                                            }),\n                                        placeholder: \"Gateadresse, postnummer og poststed\",\n                                        className: errors.employeeAddress ? \"border-red-500\" : \"\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.employeeAddress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-500 text-sm mt-1\",\n                                        children: errors.employeeAddress\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 40\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Stillingsinformasjon\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"Informasjon om stillingen og ansettelsesforholdet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"position\",\n                                                children: \"Stillingstittel *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 107,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"position\",\n                                                value: formData.position,\n                                                onChange: (e)=>updateFormData({\n                                                        position: e.target.value\n                                                    }),\n                                                placeholder: \"Anleggsgartner\",\n                                                className: errors.position ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 108,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.position && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.position\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 35\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"startDate\",\n                                                children: \"Startdato *\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                id: \"startDate\",\n                                                type: \"date\",\n                                                value: formData.startDate,\n                                                onChange: (e)=>updateFormData({\n                                                        startDate: e.target.value\n                                                    }),\n                                                className: errors.startDate ? \"border-red-500\" : \"\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 15\n                                            }, this),\n                                            errors.startDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-red-500 text-sm mt-1\",\n                                                children: errors.startDate\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 36\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                id: \"isTemporary\",\n                                                checked: formData.isTemporary,\n                                                onCheckedChange: (checked)=>updateFormData({\n                                                        isTemporary: checked,\n                                                        // Clear temporary fields if unchecked\n                                                        temporaryEndDate: checked ? formData.temporaryEndDate : \"\",\n                                                        temporaryReason: checked ? formData.temporaryReason : \"\"\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"isTemporary\",\n                                                children: \"Midlertidig ansettelse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 131,\n                                        columnNumber: 13\n                                    }, this),\n                                    formData.isTemporary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-6 space-y-4 border-l-2 border-green-200 pl-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"temporaryEndDate\",\n                                                        children: \"Midlertidig til dato *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                        id: \"temporaryEndDate\",\n                                                        type: \"date\",\n                                                        value: formData.temporaryEndDate,\n                                                        onChange: (e)=>updateFormData({\n                                                                temporaryEndDate: e.target.value\n                                                            }),\n                                                        className: errors.temporaryEndDate ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.temporaryEndDate && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.temporaryEndDate\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 47\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                        htmlFor: \"temporaryReason\",\n                                                        children: \"Grunnlag for midlertidig ansettelse *\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                                        id: \"temporaryReason\",\n                                                        value: formData.temporaryReason,\n                                                        onChange: (e)=>updateFormData({\n                                                                temporaryReason: e.target.value\n                                                            }),\n                                                        placeholder: \"Sesongarbeid i anleggsperioden\",\n                                                        rows: 3,\n                                                        className: errors.temporaryReason ? \"border-red-500\" : \"\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    errors.temporaryReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-red-500 text-sm mt-1\",\n                                                        children: errors.temporaryReason\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 46\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                                id: \"probationPeriod\",\n                                                checked: formData.probationPeriod,\n                                                onCheckedChange: (checked)=>updateFormData({\n                                                        probationPeriod: checked,\n                                                        // Reset to default 6 months if unchecked\n                                                        probationMonths: checked ? formData.probationMonths : 6\n                                                    })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                htmlFor: \"probationPeriod\",\n                                                children: \"Pr\\xf8vetid\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 13\n                                    }, this),\n                                    formData.probationPeriod && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-6 border-l-2 border-green-200 pl-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                    htmlFor: \"probationMonths\",\n                                                    children: \"Antall m\\xe5neder (maks 6) *\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                                    id: \"probationMonths\",\n                                                    type: \"number\",\n                                                    min: \"1\",\n                                                    max: \"6\",\n                                                    value: formData.probationMonths,\n                                                    onChange: (e)=>updateFormData({\n                                                            probationMonths: Number(e.target.value)\n                                                        }),\n                                                    className: errors.probationMonths ? \"border-red-500\" : \"\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.probationMonths && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-red-500 text-sm mt-1\",\n                                                    children: errors.probationMonths\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 46\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 194,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 176,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_6__.Checkbox, {\n                                        id: \"ownTools\",\n                                        checked: formData.ownTools,\n                                        onCheckedChange: (checked)=>updateFormData({\n                                                ownTools: checked\n                                            })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"ownTools\",\n                                        children: \"Holder eget h\\xe5ndverkt\\xf8y (gir kr 1,85/time)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 217,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        className: \"pb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center text-base\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5 mr-2 text-green-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"L\\xf8nn og konto\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                children: \"L\\xf8nnsinformasjon og kontoopplysninger\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4 pt-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"hourlyRate\",\n                                            children: \"Timel\\xf8nn (kr) *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"hourlyRate\",\n                                            type: \"number\",\n                                            value: formData.hourlyRate,\n                                            onChange: (e)=>updateFormData({\n                                                    hourlyRate: Number(e.target.value)\n                                                }),\n                                            min: \"0\",\n                                            step: \"0.01\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                            htmlFor: \"accountNumber\",\n                                            children: \"Kontonummer *\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            id: \"accountNumber\",\n                                            value: formData.accountNumber,\n                                            onChange: (e)=>updateFormData({\n                                                    accountNumber: e.target.value\n                                                }),\n                                            placeholder: \"1234.56.78901\",\n                                            className: errors.accountNumber ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.accountNumber && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 text-sm mt-1\",\n                                            children: errors.accountNumber\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                            lineNumber: 252,\n                                            columnNumber: 40\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-end\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: handleNext,\n                    className: \"bg-green-500 hover:bg-green-600\",\n                    children: [\n                        \"Neste steg\",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Briefcase_DollarSign_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            className: \"h-4 w-4 ml-2\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                    lineNumber: 259,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\BasicInfoStep.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/BasicInfoStep.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ContractGenerationStep.tsx":
/*!***********************************************!*\
  !*** ./components/ContractGenerationStep.tsx ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContractGenerationStep)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,FileText,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,FileText,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,FileText,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Eye,FileText,Printer!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _components_ContractPreview__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ContractPreview */ \"(ssr)/./components/ContractPreview.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction ContractGenerationStep({ formData, onPrev }) {\n    const [showPreview, setShowPreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handlePrint = ()=>{\n        window.print();\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"no-NO\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: !showPreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"pb-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center text-base\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-5 w-5 mr-2 text-green-500\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sammendrag av kontraktinformasjon\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardDescription, {\n                                    children: \"Kontroller informasjonen f\\xf8r du genererer kontrakten\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-6 pt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: \"Ansatt\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 48,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Navn:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 51,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.employeeName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 50,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Adresse:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 54,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.employeeAddress\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 53,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"F\\xf8dselsdato:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 57,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate(formData.employeeBirthDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 56,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 49,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                lineNumber: 47,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: \"Stilling\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Stillingstittel:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 66,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.position\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 65,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Startdato:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 69,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formatDate(formData.startDate)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 68,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Ansettelsestype:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 72,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.employmentType === \"fast\" ? \"Fast ansettelse\" : \"Midlertidig ansettelse\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 71,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Pr\\xf8vetid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.probationPeriod ? \"Ja (6 måneder)\" : \"Nei\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                        lineNumber: 46,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: \"L\\xf8nn og arbeidstid\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Timel\\xf8nn:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 88,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" kr \",\n                                                                    formData.hourlyRate,\n                                                                    \",-\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 87,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Timer per uke:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 91,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.workingHoursPerWeek\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 90,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Arbeidstid:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 94,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.workingTime\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 93,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Kontonummer:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.accountNumber\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 86,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                lineNumber: 84,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-semibold text-gray-900 mb-2\",\n                                                        children: \"Bedrift\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-1 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Navn:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 106,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.companyName\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Org.nr:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 109,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.companyOrgNumber\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 108,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: \"Adresse:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                        lineNumber: 112,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \" \",\n                                                                    formData.companyAddress\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                                lineNumber: 111,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                lineNumber: 45,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: onPrev,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 15\n                                }, this),\n                                \"Forrige steg\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setShowPreview(true),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Forh\\xe5ndsvis kontrakt\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>setShowPreview(true),\n                                    className: \"bg-green-500 hover:bg-green-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Generer kontrakt\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>setShowPreview(false),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 15\n                                }, this),\n                                \"Tilbake til sammendrag\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handlePrint,\n                            className: \"bg-green-500 hover:bg-green-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Eye_FileText_Printer_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                \"Skriv ut kontrakt\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                            lineNumber: 145,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                    lineNumber: 140,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ContractPreview__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    formData: formData\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n                    lineNumber: 150,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractGenerationStep.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ContractGenerationStep.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ContractPreview.tsx":
/*!****************************************!*\
  !*** ./components/ContractPreview.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContractPreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction ContractPreview({ formData }) {\n    const formatDate = (dateString)=>{\n        if (!dateString) return \"\";\n        const date = new Date(dateString);\n        return date.toLocaleDateString(\"no-NO\", {\n            day: \"2-digit\",\n            month: \"2-digit\",\n            year: \"numeric\"\n        });\n    };\n    const contractHTML = `\n    <!DOCTYPE html>\n    <html lang=\"no\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>Arbeidskontrakt - ${formData.employeeName}</title>\n      <style>\n        @media print {\n          body { margin: 0; }\n          .no-print { display: none; }\n        }\n        \n        body {\n          font-family: 'Calibri', Arial, sans-serif;\n          font-size: 11pt;\n          line-height: 1.4;\n          color: #000;\n          max-width: 210mm;\n          margin: 0 auto;\n          padding: 20mm;\n          background: white;\n        }\n        \n        .header {\n          text-align: center;\n          margin-bottom: 30px;\n          border-bottom: 2px solid #22c55e;\n          padding-bottom: 20px;\n        }\n        \n        .company-name {\n          font-size: 18pt;\n          font-weight: bold;\n          color: #22c55e;\n          margin-bottom: 5px;\n        }\n        \n        .document-title {\n          font-size: 16pt;\n          font-weight: bold;\n          margin-top: 15px;\n        }\n        \n        .section {\n          margin-bottom: 20px;\n        }\n        \n        .section-title {\n          font-size: 12pt;\n          font-weight: bold;\n          color: #22c55e;\n          margin-bottom: 10px;\n          border-bottom: 1px solid #22c55e;\n          padding-bottom: 2px;\n        }\n        \n        .info-grid {\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 20px;\n          margin-bottom: 15px;\n        }\n        \n        .info-item {\n          margin-bottom: 8px;\n        }\n        \n        .label {\n          font-weight: bold;\n          display: inline-block;\n          min-width: 120px;\n        }\n        \n        .signature-section {\n          margin-top: 40px;\n          display: grid;\n          grid-template-columns: 1fr 1fr;\n          gap: 40px;\n        }\n        \n        .signature-box {\n          border-top: 1px solid #000;\n          padding-top: 5px;\n          text-align: center;\n          margin-top: 40px;\n        }\n        \n        .legal-text {\n          font-size: 10pt;\n          margin-top: 20px;\n          padding: 10px;\n          background-color: #f9f9f9;\n          border-left: 3px solid #22c55e;\n        }\n        \n        ul {\n          margin: 10px 0;\n          padding-left: 20px;\n        }\n        \n        li {\n          margin-bottom: 5px;\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"header\">\n        <div style=\"display: flex; align-items: center; justify-content: center; margin-bottom: 15px;\">\n          <div style=\"width: 40px; height: 40px; background-color: #22c55e; border-radius: 8px; margin-right: 15px; position: relative; overflow: hidden;\">\n            <svg viewBox=\"0 0 40 40\" style=\"width: 100%; height: 100%;\">\n              <path d=\"M0 40 L14 18 L25 25 L40 10 L40 40 Z\" fill=\"white\" opacity=\"0.9\"/>\n              <path d=\"M0 40 L10 22 L20 28 L32 14 L40 18 L40 40 Z\" fill=\"white\" opacity=\"0.7\"/>\n            </svg>\n          </div>\n          <div>\n            <div class=\"company-name\">${formData.companyName}</div>\n            <div style=\"font-size: 10pt; color: #666; margin-top: 2px;\">Anleggsgartner & maskinentreprenør</div>\n          </div>\n        </div>\n        <div>Org.nr: ${formData.companyOrgNumber}</div>\n        <div>${formData.companyAddress}</div>\n        <div class=\"document-title\">ARBEIDSKONTRAKT</div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">1. PARTENES IDENTITET</div>\n        <div class=\"info-grid\">\n          <div>\n            <div class=\"info-item\">\n              <span class=\"label\">Arbeidsgiver:</span> ${formData.companyName}\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Org.nr:</span> ${formData.companyOrgNumber}\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Adresse:</span> ${formData.companyAddress}\n            </div>\n          </div>\n          <div>\n            <div class=\"info-item\">\n              <span class=\"label\">Arbeidstaker:</span> ${formData.employeeName}\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Fødselsdato:</span> ${formatDate(formData.employeeBirthDate)}\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Adresse:</span> ${formData.employeeAddress}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">2. ARBEIDSSTED OG ARBEIDSOPPGAVER</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Arbeidssted:</span> Prosjektbasert innen Ringerike og omegn; oppmøtested avtales for hvert prosjekt (AML § 14-6 (1) bokstav b)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Stillingsbetegnelse:</span> ${formData.position} (AML § 14-6 (1) bokstav c)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Arbeidsoppgaver:</span> Arbeid innen anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten (AML § 14-6 (1) bokstav c)\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">3. ANSETTELSESFORHOLD</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Tiltredelsesdato:</span> ${formatDate(formData.startDate)} (AML § 14-6 (1) bokstav d)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Ansettelsestype:</span> ${formData.isTemporary ? \"Midlertidig ansettelse\" : \"Fast ansettelse\"}\n        </div>\n        ${formData.isTemporary && formData.temporaryEndDate ? `\n        <div class=\"info-item\">\n          <span class=\"label\">Varighet:</span> Til ${formatDate(formData.temporaryEndDate)} (AML § 14-6 (1) bokstav e)\n        </div>\n        ` : \"\"}\n        ${formData.isTemporary && formData.temporaryReason ? `\n        <div class=\"info-item\">\n          <span class=\"label\">Grunnlag:</span> ${formData.temporaryReason} (AML § 14-6 (1) bokstav e)\n        </div>\n        ` : \"\"}\n        ${formData.probationPeriod ? `\n        <div class=\"info-item\">\n          <span class=\"label\">Prøvetid:</span> ${formData.probationMonths} måneder med 14 dagers gjensidig oppsigelsesfrist (AML § 15-3 (7) og § 15-6 (4)) (AML § 14-6 (1) bokstav e)\n        </div>\n        ` : \"\"}\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">4. ARBEIDSTID OG LØNN</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Arbeidstid:</span> ${formData.workingHoursPerWeek} t/uke, normalt ${formData.workingTime} (AML § 14-6 (1) bokstav f)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Pauser:</span> ${formData.breakTime} (AML § 14-6 (1) bokstav f)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Timesats:</span> kr ${formData.hourlyRate},- (AML § 14-6 (1) bokstav g)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Overtidstillegg:</span> Minst ${formData.overtimeRate}% av timelønn (AML § 10-6 (11)) (AML § 14-6 (1) bokstav g)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Utbetaling:</span> Den ${formData.paymentDay}. hver måned til kontonummer ${formData.accountNumber} (elektronisk) (AML § 14-6 (1) bokstav g)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Kjøregodtgjørelse:</span> ${formData.travelAllowance} ved bruk av egen bil i tjenesten\n        </div>\n        ${formData.ownTools ? `\n<div class=\"info-item\">\n  <span class=\"label\">Håndverktøygodtgjørelse:</span> kr 1,85 per time ved bruk av eget håndverktøy\n</div>\n` : \"\"}\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">5. FERIE OG PERMISJON</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Ferie:</span> 5 uker pr. år (Ferieloven § 5) (AML § 14-6 (1) bokstav h)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Feriepenger:</span> 12% av feriepengegrunnlaget for 5 uker ferie (Ferieloven § 10 og § 11) (AML § 14-6 (1) bokstav h)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Betalt fravær:</span> Arbeidsgiver dekker lønn i arbeidsgiverperioden ved sykdom. Rett til annet betalt fravær i henhold til folketrygdloven og arbeidsmiljøloven (AML § 14-6 (1) bokstav m)\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">6. OPPSIGELSE OG ENDRINGER</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Oppsigelsesfrister:</span> ${formData.noticePeriod} (fristene øker med ansiennitet og alder iht. AML § 15-3) (AML § 14-6 (1) bokstav i)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Fremgangsmåte:</span> Oppsigelse skal være skriftlig. Henvisning til AML kapittel 15 (Opphør av arbeidsforhold) og kapittel 17 (Tvisteløsning) (AML § 14-6 (1) bokstav i og n)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Endringer i arbeidsplan:</span> ${formData.notificationRules} (AML § 14-6 (1) bokstav n)\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">7. PENSJON OG FORSIKRING</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Pensjon:</span> ${formData.pensionProvider} (org.nr ${formData.pensionOrgNumber}) (AML § 14-6 (1) bokstav k)\n        </div>\n        <div class=\"info-item\">\n          <span class=\"label\">Yrkesskadeforsikring:</span> ${formData.insuranceProvider} (org.nr ${formData.insuranceOrgNumber}) (AML § 1-4)\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">8. KOMPETANSE OG UTVIKLING</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Kompetanseutvikling:</span> Arbeidsgiver tilbyr nødvendig opplæring og vil vurdere kompetanseutviklingstiltak for stillingen (AML § 14-6 (1) bokstav l)\n        </div>\n      </div>\n\n      <div class=\"section\">\n        <div class=\"section-title\">9. TARIFFAVTALE</div>\n        <div class=\"info-item\">\n          <span class=\"label\">Status:</span> Ingen tariffavtale er gjeldende pr. dags dato (AML § 14-6 (1) bokstav j)\n        </div>\n      </div>\n\n      <div class=\"legal-text\">\n        <strong>Juridisk grunnlag:</strong> Denne kontrakten er utarbeidet i henhold til Arbeidsmiljøloven § 14-6 og oppfyller alle krav til obligatoriske opplysninger i arbeidskontrakter. Kontrakten skal foreligge skriftlig snarest mulig og senest syv dager etter arbeidsforholdets begynnelse for grunnleggende opplysninger, og senest en måned for øvrige bestemmelser.\n      </div>\n\n      <div class=\"signature-section\">\n        <div>\n          <div class=\"signature-box\">\n            Dato og sted\n          </div>\n        </div>\n        <div>\n          <div class=\"signature-box\">\n            Dato og sted\n          </div>\n        </div>\n      </div>\n\n      <div class=\"signature-section\">\n        <div>\n          <div class=\"signature-box\">\n            ${formData.companyName}<br>\n            (Arbeidsgiver)\n          </div>\n        </div>\n        <div>\n          <div class=\"signature-box\">\n            ${formData.employeeName}<br>\n            (Arbeidstaker)\n          </div>\n        </div>\n      </div>\n    </body>\n    </html>\n  `;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-lg rounded-lg overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contract-preview\",\n            dangerouslySetInnerHTML: {\n                __html: contractHTML\n            },\n            style: {\n                fontFamily: \"Calibri, Arial, sans-serif\",\n                fontSize: \"11pt\",\n                lineHeight: \"1.4\",\n                color: \"#000\",\n                padding: \"20mm\",\n                maxWidth: \"210mm\",\n                margin: \"0 auto\",\n                background: \"white\"\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractPreview.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ContractPreview.tsx\",\n        lineNumber: 340,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ContractPreview.tsx\n");

/***/ }),

/***/ "(ssr)/./components/Header.tsx":
/*!*******************************!*\
  !*** ./components/Header.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nfunction Header() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-center mb-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center w-14 h-14 bg-green-500 rounded-lg mr-4 relative overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                viewBox: \"0 0 56 56\",\n                                className: \"w-full h-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M0 56 L20 25 L35 35 L56 15 L56 56 Z\",\n                                        fill: \"white\",\n                                        opacity: \"0.9\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                                        lineNumber: 12,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        d: \"M0 56 L15 30 L28 40 L45 20 L56 25 L56 56 Z\",\n                                        fill: \"white\",\n                                        opacity: \"0.7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                                        lineNumber: 13,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                                lineNumber: 11,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-gray-800 leading-tight\",\n                                children: \"Arbeidskontrakt Generator\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-green-600 font-semibold mt-1\",\n                                children: \"Ringerike Landskap AS\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500 mt-2 max-w-2xl mx-auto\",\n                children: [\n                    \"Juridisk korrekte arbeidskontrakter i henhold til Arbeidsmilj\\xf8loven \\xa7 14-6\",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Anleggsgartner & maskinentrepren\\xf8r\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\Header.tsx\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/checkbox.tsx":
/*!************************************!*\
  !*** ./components/ui/checkbox.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-checkbox */ \"(ssr)/./node_modules/@radix-ui/react-checkbox/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Checkbox auto */ \n\n\n\n\nconst Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center justify-center text-current\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\checkbox.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\checkbox.tsx\",\n            lineNumber: 21,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\checkbox.tsx\",\n        lineNumber: 13,\n        columnNumber: 3\n    }, undefined));\nCheckbox.displayName = _radix_ui_react_checkbox__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/checkbox.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERTS1xcRGVza3RvcFxcX19TQ1JBVENIX19cXDIwMjUuMDYuMjQta2wuMjEuNTMtLWtvbnRyYWt0c2tqZW1hXFxrb250cmFrdHNramVtYV92MDAxXFxhcmJlaWRza29udHJha3QtZ2VuZXJhdG9yLW5ld2FwcHJvYWNoXFxjb21wb25lbnRzXFx1aVxcaW5wdXQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgSW5wdXQgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxJbnB1dEVsZW1lbnQsIFJlYWN0LkNvbXBvbmVudFByb3BzPFwiaW5wdXRcIj4+KFxuICAoeyBjbGFzc05hbWUsIHR5cGUsIC4uLnByb3BzIH0sIHJlZikgPT4ge1xuICAgIHJldHVybiAoXG4gICAgICA8aW5wdXRcbiAgICAgICAgdHlwZT17dHlwZX1cbiAgICAgICAgY2xhc3NOYW1lPXtjbihcbiAgICAgICAgICBcImZsZXggaC0xMCB3LWZ1bGwgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgcHgtMyBweS0yIHRleHQtYmFzZSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIGZpbGU6Ym9yZGVyLTAgZmlsZTpiZy10cmFuc3BhcmVudCBmaWxlOnRleHQtc20gZmlsZTpmb250LW1lZGl1bSBmaWxlOnRleHQtZm9yZWdyb3VuZCBwbGFjZWhvbGRlcjp0ZXh0LW11dGVkLWZvcmVncm91bmQgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTIgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZm9jdXMtdmlzaWJsZTpyaW5nLW9mZnNldC0yIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcbiAgICAgICAgICBjbGFzc05hbWVcbiAgICAgICAgKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxuXG5leHBvcnQgeyBJbnB1dCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIklucHV0IiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsInJlZiIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcRFNLXFxEZXNrdG9wXFxfX1NDUkFUQ0hfX1xcMjAyNS4wNi4yNC1rbC4yMS41My0ta29udHJha3Rza2plbWFcXGtvbnRyYWt0c2tqZW1hX3YwMDFcXGFyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2hcXGNvbXBvbmVudHNcXHVpXFxsYWJlbC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCAqIGFzIExhYmVsUHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtbGFiZWxcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGxhYmVsVmFyaWFudHMgPSBjdmEoXG4gIFwidGV4dC1zbSBmb250LW1lZGl1bSBsZWFkaW5nLW5vbmUgcGVlci1kaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWQgcGVlci1kaXNhYmxlZDpvcGFjaXR5LTcwXCJcbilcblxuY29uc3QgTGFiZWwgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBMYWJlbFByaW1pdGl2ZS5Sb290PiAmXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBsYWJlbFZhcmlhbnRzPlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8TGFiZWxQcmltaXRpdmUuUm9vdFxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24obGFiZWxWYXJpYW50cygpLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5MYWJlbC5kaXNwbGF5TmFtZSA9IExhYmVsUHJpbWl0aXZlLlJvb3QuZGlzcGxheU5hbWVcblxuZXhwb3J0IHsgTGFiZWwgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiTGFiZWxQcmltaXRpdmUiLCJjdmEiLCJjbiIsImxhYmVsVmFyaWFudHMiLCJMYWJlbCIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJwcm9wcyIsInJlZiIsIlJvb3QiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/progress.tsx":
/*!************************************!*\
  !*** ./components/ui/progress.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Progress: () => (/* binding */ Progress)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-progress */ \"(ssr)/./node_modules/@radix-ui/react-progress/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Progress auto */ \n\n\n\nconst Progress = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, value, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative h-4 w-full overflow-hidden rounded-full bg-secondary\", className),\n        ...props,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Indicator, {\n            className: \"h-full w-full flex-1 bg-primary transition-all\",\n            style: {\n                transform: `translateX(-${100 - (value || 0)}%)`\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\progress.tsx\",\n            lineNumber: 17,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\progress.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nProgress.displayName = _radix_ui_react_progress__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/progress.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/textarea.tsx":
/*!************************************!*\
  !*** ./components/ui/textarea.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Textarea: () => (/* binding */ Textarea)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Textarea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\__SCRATCH__\\\\2025.06.24-kl.21.53--kontraktskjema\\\\kontraktskjema_v001\\\\arbeidskontrakt-generator-newapproach\\\\components\\\\ui\\\\textarea.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n});\nTextarea.displayName = \"Textarea\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3RleHRhcmVhLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFJaEMsTUFBTUUseUJBQVdGLDZDQUFnQixDQUFxQyxDQUFDLEVBQUVJLFNBQVMsRUFBRSxHQUFHQyxPQUFPLEVBQUVDO0lBQzlGLHFCQUNFLDhEQUFDQztRQUNDSCxXQUFXSCw4Q0FBRUEsQ0FDWCx3U0FDQUc7UUFFRkUsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUNBSCxTQUFTTSxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxEU0tcXERlc2t0b3BcXF9fU0NSQVRDSF9fXFwyMDI1LjA2LjI0LWtsLjIxLjUzLS1rb250cmFrdHNramVtYVxca29udHJha3Rza2plbWFfdjAwMVxcYXJiZWlkc2tvbnRyYWt0LWdlbmVyYXRvci1uZXdhcHByb2FjaFxcY29tcG9uZW50c1xcdWlcXHRleHRhcmVhLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmV4cG9ydCBpbnRlcmZhY2UgVGV4dGFyZWFQcm9wcyBleHRlbmRzIFJlYWN0LlRleHRhcmVhSFRNTEF0dHJpYnV0ZXM8SFRNTFRleHRBcmVhRWxlbWVudD4ge31cblxuY29uc3QgVGV4dGFyZWEgPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxUZXh0QXJlYUVsZW1lbnQsIFRleHRhcmVhUHJvcHM+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gIHJldHVybiAoXG4gICAgPHRleHRhcmVhXG4gICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICBcImZsZXggbWluLWgtWzgwcHhdIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1zbSByaW5nLW9mZnNldC1iYWNrZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTBcIixcbiAgICAgICAgY2xhc3NOYW1lLFxuICAgICAgKX1cbiAgICAgIHJlZj17cmVmfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIClcbn0pXG5UZXh0YXJlYS5kaXNwbGF5TmFtZSA9IFwiVGV4dGFyZWFcIlxuXG5leHBvcnQgeyBUZXh0YXJlYSB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIlRleHRhcmVhIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwidGV4dGFyZWEiLCJkaXNwbGF5TmFtZSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/textarea.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXERTS1xcRGVza3RvcFxcX19TQ1JBVENIX19cXDIwMjUuMDYuMjQta2wuMjEuNTMtLWtvbnRyYWt0c2tqZW1hXFxrb250cmFrdHNramVtYV92MDAxXFxhcmJlaWRza29udHJha3QtZ2VuZXJhdG9yLW5ld2FwcHJvYWNoXFxsaWJcXHV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0RTSyU1QyU1Q0Rlc2t0b3AlNUMlNUNfX1NDUkFUQ0hfXyU1QyU1QzIwMjUuMDYuMjQta2wuMjEuNTMtLWtvbnRyYWt0c2tqZW1hJTVDJTVDa29udHJha3Rza2plbWFfdjAwMSU1QyU1Q2FyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2glNUMlNUNhcHAlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0lBQXlMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxVc2Vyc1xcXFxEU0tcXFxcRGVza3RvcFxcXFxfX1NDUkFUQ0hfX1xcXFwyMDI1LjA2LjI0LWtsLjIxLjUzLS1rb250cmFrdHNramVtYVxcXFxrb250cmFrdHNramVtYV92MDAxXFxcXGFyYmVpZHNrb250cmFrdC1nZW5lcmF0b3ItbmV3YXBwcm9hY2hcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDSK%5C%5CDesktop%5C%5C__SCRATCH__%5C%5C2025.06.24-kl.21.53--kontraktskjema%5C%5Ckontraktskjema_v001%5C%5Carbeidskontrakt-generator-newapproach%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/lucide-react","vendor-chunks/@radix-ui","vendor-chunks/@swc","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDSK%5CDesktop%5C__SCRATCH__%5C2025.06.24-kl.21.53--kontraktskjema%5Ckontraktskjema_v001%5Carbeidskontrakt-generator-newapproach&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();